# 生产环境配置示例
# 复制此文件为 .env 并根据需要修改配置
# 注意：生产环境中的敏感信息应该使用 Docker Secrets 或外部密钥管理系统

# 应用环境
GO_ENV=production
GIN_MODE=release

# 镜像配置
REGISTRY=ghcr.io
VERSION=latest

# 服务端口配置
CONTENT_MANAGEMENT_PORT=12010

# 数据库配置（生产环境请使用强密码）
POSTGRES_DB=content_management_prod
POSTGRES_USER=content_user_prod
POSTGRES_PASSWORD=CHANGE_THIS_STRONG_PASSWORD
POSTGRES_PORT=5432

DB_HOST=postgres
DB_PORT=5432
DB_USER=content_user_prod
DB_PASSWORD=CHANGE_THIS_STRONG_PASSWORD
DB_NAME=content_management_prod
DB_SSL_MODE=require

# Redis配置（生产环境请使用强密码）
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD
REDIS_DB=0

# JWT配置（生产环境请使用强密钥）
JWT_SECRET_KEY=CHANGE_THIS_SUPER_SECRET_JWT_KEY_IN_PRODUCTION
JWT_TOKEN_DURATION=1h
JWT_REFRESH_DURATION=24h

# Consul配置
CONSUL_HOST=consul
CONSUL_PORT=8500
CONSUL_DATACENTER=dc1
CONSUL_ENCRYPT=CHANGE_THIS_CONSUL_ENCRYPT_KEY

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=CHANGE_THIS_GRAFANA_PASSWORD
JAEGER_UI_PORT=16686
ALERTMANAGER_PORT=9093

# 外部服务配置
VIDEO_SERVICE_URL=http://video-service:12001
INTERACTION_SERVICE_URL=http://interaction-service:12002

# SSL/TLS配置
SSL_CERT_PATH=/app/ssl/cert.pem
SSL_KEY_PATH=/app/ssl/key.pem

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# 安全配置
ENABLE_PPROF=false
ENABLE_SWAGGER=false
ENABLE_DEBUG_ROUTES=false
CORS_ALLOWED_ORIGINS=https://yourdomain.com
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
