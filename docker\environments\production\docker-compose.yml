# 生产环境完整配置
version: '3.8'

services:
  # 内容管理服务
  content-management-service:
    image: ${REGISTRY:-ghcr.io}/pxpat-backend/content-management-service:${VERSION:-latest}
    container_name: content-management-service
    restart: unless-stopped
    ports:
      - "12010:12010"
    environment:
      - GO_ENV=production
      - GIN_MODE=release
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DB_SSL_MODE=require
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_TOKEN_DURATION=1h
      - JWT_REFRESH_DURATION=24h
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      - CONSUL_DATACENTER=dc1
      - LOG_LEVEL=info
      - LOG_FORMAT=json
    volumes:
      - ../logs:/app/logs
      - ../configs:/app/config:ro
    depends_on:
      - postgres
      - redis
      - consul
    networks:
      - content-network
      - database-network
      - service-discovery-network
      - monitoring-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:12010/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../configs/init-scripts:/docker-entrypoint-initdb.d:ro
      - ../configs/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    ports:
      - "5432:5432"
    networks:
      - database-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ../configs/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - database-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Consul 服务发现
  consul:
    image: consul:1.16
    container_name: consul
    restart: unless-stopped
    command: >
      agent -server -bootstrap-expect=1 -ui -client=0.0.0.0 -bind=0.0.0.0
      -config-file=/consul/config/consul.json
    environment:
      - CONSUL_DATACENTER=dc1
      - CONSUL_ENCRYPT=${CONSUL_ENCRYPT}
    volumes:
      - consul_data:/consul/data
      - ../configs/consul/consul.json:/consul/config/consul.json:ro
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    networks:
      - service-discovery-network
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # MinIO 对象存储
  minio:
    image: minio/minio:RELEASE.2024-04-18T19-09-19Z
    container_name: pxpat-minio
    ports:
      - "9000:9000"  # API端口
      - "9001:9001"  # Web控制台端口
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    command: server --console-address ":9001" /data
    networks:
      - pxpat-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

# 网络配置
networks:
  content-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  database-network:
    driver: bridge
  service-discovery-network:
    driver: bridge
  monitoring-network:
    driver: bridge
  pxpat-network:
    driver: bridge

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  consul_data:
    driver: local
  minio_data:
    driver: local
