# Kibana 配置文件 - pxpat 微服务集群日志可视化

# 服务器配置
server.name: "pxpat-kibana"
server.host: "0.0.0.0"
server.port: 5601

# Elasticsearch 连接配置
elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.requestTimeout: 30000
elasticsearch.shardTimeout: 30000

# 监控配置
monitoring.ui.container.elasticsearch.enabled: true

# 安全配置（暂时禁用以解决版本兼容性问题）
xpack.security.enabled: false
xpack.encryptedSavedObjects.encryptionKey: "pxpat-kibana-encryption-key-32-chars"

# 日志配置
logging.root:
  level: info

# 国际化配置
i18n.locale: "zh-CN"

# 数据视图配置
data.search.aggs.shardDelay.enabled: true

# 保存对象配置
savedObjects.maxImportPayloadBytes: 26214400

# 遥测配置（禁用以提高性能）
telemetry.enabled: false
telemetry.optIn: false

# 新闻订阅配置（禁用）
newsfeed.enabled: false

# UI 配置
uiSettings.overrides:
  "defaultIndex": "logstash-*"
  "timepicker:timeDefaults": "{ \"from\": \"now-15m\", \"to\": \"now\" }" 