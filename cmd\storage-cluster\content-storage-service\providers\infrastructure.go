package providers

import (
	"pxpat-backend/internal/storage-cluster/content-storage-service/migrations"
	"pxpat-backend/internal/storage-cluster/content-storage-service/types"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/middleware/cors"
	metricsMiddleware "pxpat-backend/pkg/middleware/metrics"
	tracingMiddleware "pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideOtelProvider 提供OpenTelemetry Provider
func ProvideOtelProvider(cfg *types.Config) (*opentelemetry.Provider, error) {
	otelProvider, err := opentelemetry.NewProvider(cfg.Otlp)
	if err != nil {
		log.Fatal().Err(err).Msg("OpenTelemetry 初始化失败")
		return nil, err
	}

	if otelProvider.IsTracingEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Tracing.ServiceName).
			Str("exporter_type", cfg.Otlp.Tracing.ExporterType).
			Msg("链路追踪已启用")
	} else {
		log.Info().Msg("链路追踪已禁用")
	}

	if otelProvider.IsMetricsEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Metrics.ServiceName).
			Str("exporter_type", cfg.Otlp.Metrics.ExporterType).
			Msg("指标收集已启用")
	} else {
		log.Info().Msg("指标收集已禁用")
	}

	log.Info().Msg("OpenTelemetry Provider 初始化成功")
	return otelProvider, nil
}

// ProvideDatabase 提供数据库连接
func ProvideDatabase(cfg *types.Config) (*gorm.DB, error) {
	log.Info().Msg("Attempting to connect to database...")
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Msgf("数据库连接失败: %v", err)
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	// 数据库迁移
	if err := migrations.AutoMigrate(db); err != nil {
		log.Fatal().Msgf("数据库迁移失败: %v", err)
		return nil, err
	}
	log.Info().Msg("Database migration completed")

	return db, nil
}

// ProvideConsulManager 提供Consul管理器
func ProvideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	log.Info().Msg("Consul管理器初始化成功")
	return consulManager, nil
}

// ProvideHealthHandler 提供健康检查处理器
func ProvideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	log.Info().Msg("健康检查处理器初始化成功")
	return healthHandler
}

// ProvideGinEngine 提供Gin引擎
func ProvideGinEngine(
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	healthHandler *consul.HealthHandler,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	r := gin.Default()

	// 添加中间件
	r.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 添加指标收集中间件
	if otelProvider.IsMetricsEnabled() {
		r.Use(metricsMiddleware.Middleware(otelProvider.MetricsProvider(), cfg.Otlp.Metrics.ServiceName))
	}

	// 添加链路追踪中间件
	if otelProvider.IsTracingEnabled() {
		r.Use(tracingMiddleware.Middleware(otelProvider.TracingProvider(), cfg.Otlp.Tracing.ServiceName))
	}

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(r)

	log.Info().Msg("Gin引擎初始化成功")
	return r
}
