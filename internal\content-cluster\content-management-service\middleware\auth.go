package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/jwt"
	"pxpat-backend/pkg/middleware/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

// AuthMiddleware JWT认证中间件
type AuthMiddleware struct {
	jwtManager jwt.Manager
}

// NewAuthMiddleware 创建认证中间件实例
func NewAuthMiddleware(jwtManager jwt.Manager) *AuthMiddleware {
	return &AuthMiddleware{
		jwtManager: jwtManager,
	}
}

// RequireAuth 需要认证的中间件
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 链路追踪
		ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "middleware", "RequireAuth")
		defer span.End()
		c.Request = c.Request.WithContext(ctx)

		// 2. 获取trace信息
		traceID := tracing.GetTraceID(c)

		// 3. 添加span属性
		opentelemetry.AddAttribute(span, "method", c.Request.Method)
		opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

		// 4. 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Msg("缺少Authorization头")

			opentelemetry.AddError(span, errors.NewAuthError("缺少认证信息"))
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "缺少认证信息",
			})
			c.Abort()
			return
		}

		// 5. 检查Bearer前缀
		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Msg("Authorization头格式错误")

			opentelemetry.AddError(span, errors.NewAuthError("认证信息格式错误"))
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "认证信息格式错误",
			})
			c.Abort()
			return
		}

		// 6. 提取token
		token := strings.TrimPrefix(authHeader, bearerPrefix)
		if token == "" {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Msg("Token为空")

			opentelemetry.AddError(span, errors.NewAuthError("Token为空"))
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "Token为空",
			})
			c.Abort()
			return
		}

		// 7. 验证token
		claims, err := m.jwtManager.ValidateToken(token)
		if err != nil {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Err(err).
				Msg("Token验证失败")

			opentelemetry.AddError(span, err)
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "Token无效或已过期",
			})
			c.Abort()
			return
		}

		// 8. 设置用户信息到上下文
		userKSUID := claims.UserKSUID
		if userKSUID == "" {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Msg("Token中缺少用户KSUID")

			opentelemetry.AddError(span, errors.NewAuthError("Token中缺少用户信息"))
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "Token中缺少用户信息",
			})
			c.Abort()
			return
		}

		// 9. 设置用户KSUID到上下文
		ksuid.SetKSUID(c, userKSUID)

		// 10. 添加用户信息到span
		opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
		opentelemetry.AddAttribute(span, "user_role", claims.Role)

		// 11. 记录认证成功日志
		log.Debug().
			Str("trace_id", traceID).
			Str("user_ksuid", userKSUID).
			Str("user_role", claims.Role).
			Str("path", c.Request.URL.Path).
			Msg("用户认证成功")

		// 12. 继续处理请求
		c.Next()
	}
}

// OptionalAuth 可选认证的中间件（不强制要求认证）
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 链路追踪
		ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "middleware", "OptionalAuth")
		defer span.End()
		c.Request = c.Request.WithContext(ctx)

		// 2. 获取trace信息
		traceID := tracing.GetTraceID(c)

		// 3. 添加span属性
		opentelemetry.AddAttribute(span, "method", c.Request.Method)
		opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

		// 4. 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// 没有认证信息，继续处理（匿名用户）
			log.Debug().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Msg("匿名用户访问")

			opentelemetry.AddAttribute(span, "user_type", "anonymous")
			c.Next()
			return
		}

		// 5. 检查Bearer前缀
		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			// 格式错误，继续处理（匿名用户）
			log.Debug().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Msg("Authorization头格式错误，作为匿名用户处理")

			opentelemetry.AddAttribute(span, "user_type", "anonymous")
			c.Next()
			return
		}

		// 6. 提取token
		token := strings.TrimPrefix(authHeader, bearerPrefix)
		if token == "" {
			// Token为空，继续处理（匿名用户）
			log.Debug().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Msg("Token为空，作为匿名用户处理")

			opentelemetry.AddAttribute(span, "user_type", "anonymous")
			c.Next()
			return
		}

		// 7. 验证token
		claims, err := m.jwtManager.ValidateToken(token)
		if err != nil {
			// Token无效，继续处理（匿名用户）
			log.Debug().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Err(err).
				Msg("Token验证失败，作为匿名用户处理")

			opentelemetry.AddAttribute(span, "user_type", "anonymous")
			c.Next()
			return
		}

		// 8. 设置用户信息到上下文
		userKSUID := claims.UserKSUID
		if userKSUID != "" {
			ksuid.SetKSUID(c, userKSUID)

			// 添加用户信息到span
			opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
			opentelemetry.AddAttribute(span, "user_role", claims.Role)
			opentelemetry.AddAttribute(span, "user_type", "authenticated")

			log.Debug().
				Str("trace_id", traceID).
				Str("user_ksuid", userKSUID).
				Str("user_role", claims.Role).
				Str("path", c.Request.URL.Path).
				Msg("用户认证成功")
		} else {
			opentelemetry.AddAttribute(span, "user_type", "anonymous")
		}

		// 9. 继续处理请求
		c.Next()
	}
}

// AdminOnly 仅管理员可访问的中间件
func (m *AuthMiddleware) AdminOnly() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 链路追踪
		ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "middleware", "AdminOnly")
		defer span.End()
		c.Request = c.Request.WithContext(ctx)

		// 2. 获取trace信息
		traceID := tracing.GetTraceID(c)
		userKSUID := ksuid.GetKSUID(c)

		// 3. 添加span属性
		opentelemetry.AddAttribute(span, "method", c.Request.Method)
		opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
		opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

		// 4. 检查是否已认证
		if userKSUID == "" {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Msg("未认证用户尝试访问管理员接口")

			opentelemetry.AddError(span, errors.NewAuthError("需要管理员权限"))
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "需要管理员权限",
			})
			c.Abort()
			return
		}

		// 5. 这里应该检查用户角色，但由于没有用户服务客户端，暂时跳过
		// TODO: 实现用户角色检查
		log.Debug().
			Str("trace_id", traceID).
			Str("user_ksuid", userKSUID).
			Str("path", c.Request.URL.Path).
			Msg("管理员权限检查通过")

		// 6. 继续处理请求
		c.Next()
	}
}
