package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
	globalTypes "pxpat-backend/pkg/types"
	"time"
)

// Config 内容管理服务配置结构
type Config struct {
	Server   globalTypes.GlobalServerConfig   `mapstructure:"server"`
	Database globalTypes.GlobalDatabaseConfig `mapstructure:"database"`
	Redis    globalTypes.GlobalRedisConfig    `mapstructure:"redis"`
	RabbitMQ globalTypes.GlobalRabbitMQConfig `mapstructure:"rabbitmq"`
	JWT      globalTypes.GlobalJWTConfig      `mapstructure:"jwt"`
	Log      globalTypes.GlobalLogConfig      `mapstructure:"log"`
	Consul   globalTypes.GlobalConsulConfig   `mapstructure:"consul"`

	// 业务配置
	Management ManagementConfig `mapstructure:"management"`
	Services   ServicesConfig   `mapstructure:"services"`
	Cache      CacheConfig      `mapstructure:"cache"`
	Security   SecurityConfig   `mapstructure:"security"`
}

// ManagementConfig 内容管理配置
type ManagementConfig struct {
	Limits LimitsConfig `mapstructure:"limits"`
}

// LimitsConfig 限制配置
type LimitsConfig struct {
	MaxBatchSize     int `mapstructure:"max_batch_size"`     // 批量操作最大数量
	MaxSearchResults int `mapstructure:"max_search_results"` // 搜索结果最大数量
	MaxPageSize      int `mapstructure:"max_page_size"`      // 分页最大大小
	DefaultPageSize  int `mapstructure:"default_page_size"`  // 默认分页大小
	MaxContentAge    int `mapstructure:"max_content_age"`    // 内容最大保留天数
}

// ServicesConfig 外部服务配置
type ServicesConfig struct {
	VideoService       ServiceConfig `mapstructure:"video_service"`
	NovelService       ServiceConfig `mapstructure:"novel_service"`
	MusicService       ServiceConfig `mapstructure:"music_service"`
	InteractionService ServiceConfig `mapstructure:"interaction_service"`
}

// ServiceConfig 单个服务配置
type ServiceConfig struct {
	Enabled    bool          `mapstructure:"enabled"`     // 是否启用
	BaseURL    string        `mapstructure:"base_url"`    // 服务基础URL
	Timeout    time.Duration `mapstructure:"timeout"`     // 请求超时时间
	RetryCount int           `mapstructure:"retry_count"` // 重试次数
	RetryDelay time.Duration `mapstructure:"retry_delay"` // 重试延迟
}

// CacheConfig 缓存配置
type CacheConfig struct {
	ContentTTL     time.Duration `mapstructure:"content_ttl"`      // 内容缓存TTL
	InteractionTTL time.Duration `mapstructure:"interaction_ttl"`  // 交互统计缓存TTL
	UserContentTTL time.Duration `mapstructure:"user_content_ttl"` // 用户内容缓存TTL
	StatsTTL       time.Duration `mapstructure:"stats_ttl"`        // 统计数据缓存TTL
	CategoryTTL    time.Duration `mapstructure:"category_ttl"`     // 分类缓存TTL
	TagTTL         time.Duration `mapstructure:"tag_ttl"`          // 标签缓存TTL
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	RateLimit RateLimitConfig `mapstructure:"rate_limit"`
	Auth      AuthConfig      `mapstructure:"auth"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled    bool          `mapstructure:"enabled"`     // 是否启用限流
	RPM        int           `mapstructure:"rpm"`         // 每分钟请求数
	BurstSize  int           `mapstructure:"burst_size"`  // 突发请求数
	WindowSize time.Duration `mapstructure:"window_size"` // 时间窗口大小
}

// AuthConfig 认证配置
type AuthConfig struct {
	RequireAuth  bool     `mapstructure:"require_auth"`  // 是否需要认证
	AdminRoles   []string `mapstructure:"admin_roles"`   // 管理员角色
	ManagerRoles []string `mapstructure:"manager_roles"` // 管理者角色
	TokenHeader  string   `mapstructure:"token_header"`  // Token头部名称
	TokenPrefix  string   `mapstructure:"token_prefix"`  // Token前缀
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *Config {
	return &Config{
		Management: ManagementConfig{
			Limits: LimitsConfig{
				MaxBatchSize:     100,
				MaxSearchResults: 1000,
				MaxPageSize:      100,
				DefaultPageSize:  20,
				MaxContentAge:    365, // 365天
			},
		},
		Services: ServicesConfig{
			VideoService: ServiceConfig{
				Enabled:    true,
				BaseURL:    "http://localhost:12001",
				Timeout:    30 * time.Second,
				RetryCount: 3,
				RetryDelay: 1 * time.Second,
			},
			NovelService: ServiceConfig{
				Enabled:    false, // 预留，暂时不启用
				BaseURL:    "http://localhost:12002",
				Timeout:    30 * time.Second,
				RetryCount: 3,
				RetryDelay: 1 * time.Second,
			},
			MusicService: ServiceConfig{
				Enabled:    false, // 预留，暂时不启用
				BaseURL:    "http://localhost:12003",
				Timeout:    30 * time.Second,
				RetryCount: 3,
				RetryDelay: 1 * time.Second,
			},
			InteractionService: ServiceConfig{
				Enabled:    true,
				BaseURL:    "http://localhost:12009",
				Timeout:    15 * time.Second,
				RetryCount: 2,
				RetryDelay: 500 * time.Millisecond,
			},
		},
		Cache: CacheConfig{
			ContentTTL:     5 * time.Minute,
			InteractionTTL: 1 * time.Minute,
			UserContentTTL: 3 * time.Minute,
			StatsTTL:       10 * time.Minute,
			CategoryTTL:    30 * time.Minute,
			TagTTL:         15 * time.Minute,
		},
		Security: SecurityConfig{
			RateLimit: RateLimitConfig{
				Enabled:    true,
				RPM:        1000,
				BurstSize:  100,
				WindowSize: 1 * time.Minute,
			},
			Auth: AuthConfig{
				RequireAuth:  true,
				AdminRoles:   []string{"admin", "super_admin"},
				ManagerRoles: []string{"manager", "content_manager"},
				TokenHeader:  "Authorization",
				TokenPrefix:  "Bearer ",
			},
		},
	}
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 验证限制配置
	if c.Management.Limits.MaxBatchSize <= 0 {
		return NewConfigError("max_batch_size must be greater than 0")
	}
	if c.Management.Limits.MaxPageSize <= 0 {
		return NewConfigError("max_page_size must be greater than 0")
	}
	if c.Management.Limits.DefaultPageSize <= 0 {
		return NewConfigError("default_page_size must be greater than 0")
	}
	if c.Management.Limits.DefaultPageSize > c.Management.Limits.MaxPageSize {
		return NewConfigError("default_page_size cannot be greater than max_page_size")
	}

	// 验证服务配置
	if c.Services.VideoService.Enabled && c.Services.VideoService.BaseURL == "" {
		return NewConfigError("video_service base_url is required when enabled")
	}
	if c.Services.InteractionService.Enabled && c.Services.InteractionService.BaseURL == "" {
		return NewConfigError("interaction_service base_url is required when enabled")
	}

	// 验证缓存配置
	if c.Cache.ContentTTL <= 0 {
		return NewConfigError("content_ttl must be greater than 0")
	}
	if c.Cache.InteractionTTL <= 0 {
		return NewConfigError("interaction_ttl must be greater than 0")
	}

	return nil
}

// ConfigError 配置错误
type ConfigError struct {
	Message string
}

func (e *ConfigError) Error() string {
	return "config error: " + e.Message
}

// NewConfigError 创建配置错误
func NewConfigError(message string) *ConfigError {
	return &ConfigError{Message: message}
}

// IsEnabled 检查服务是否启用
func (c *Config) IsVideoServiceEnabled() bool {
	return c.Services.VideoService.Enabled
}

func (c *Config) IsNovelServiceEnabled() bool {
	return c.Services.NovelService.Enabled
}

func (c *Config) IsMusicServiceEnabled() bool {
	return c.Services.MusicService.Enabled
}

func (c *Config) IsInteractionServiceEnabled() bool {
	return c.Services.InteractionService.Enabled
}

// GetServiceConfig 获取服务配置
func (c *Config) GetVideoServiceConfig() ServiceConfig {
	return c.Services.VideoService
}

func (c *Config) GetNovelServiceConfig() ServiceConfig {
	return c.Services.NovelService
}

func (c *Config) GetMusicServiceConfig() ServiceConfig {
	return c.Services.MusicService
}

func (c *Config) GetInteractionServiceConfig() ServiceConfig {
	return c.Services.InteractionService
}

// LoadConfig 加载配置
func LoadConfig(cfg *Config) error {
	// 设置配置文件名和路径
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")

	// 添加配置文件搜索路径
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")
	viper.AddConfigPath("../config")
	viper.AddConfigPath("../../config")

	// 获取当前工作目录
	if wd, err := os.Getwd(); err == nil {
		// 添加相对于当前目录的配置路径
		viper.AddConfigPath(filepath.Join(wd, "config"))
		viper.AddConfigPath(filepath.Join(wd, "../config"))
		viper.AddConfigPath(filepath.Join(wd, "../../config"))
	}

	// 设置环境变量前缀
	viper.SetEnvPrefix("CONTENT_MANAGEMENT")
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(cfg); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	// 设置默认值
	setDefaultValues(cfg)

	// 验证配置
	if err := validateConfig(cfg); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	return nil
}

// setDefaultValues 设置默认值
func setDefaultValues(cfg *Config) {
	// 服务器默认配置
	if cfg.Server.Port == 0 {
		cfg.Server.Port = 12010
	}
	if cfg.Server.ReadTimeout == 0 {
		cfg.Server.ReadTimeout = 30 * time.Second
	}
	if cfg.Server.WriteTimeout == 0 {
		cfg.Server.WriteTimeout = 30 * time.Second
	}
	if cfg.Server.IdleTimeout == 0 {
		cfg.Server.IdleTimeout = 60 * time.Second
	}

	// 管理配置默认值
	if cfg.Management.Limits.MaxBatchSize == 0 {
		cfg.Management.Limits.MaxBatchSize = 100
	}
	if cfg.Management.Limits.MaxSearchResults == 0 {
		cfg.Management.Limits.MaxSearchResults = 1000
	}
	if cfg.Management.Limits.MaxPageSize == 0 {
		cfg.Management.Limits.MaxPageSize = 100
	}
	if cfg.Management.Limits.DefaultPageSize == 0 {
		cfg.Management.Limits.DefaultPageSize = 20
	}
	if cfg.Management.Limits.MaxContentAge == 0 {
		cfg.Management.Limits.MaxContentAge = 365 // 1年
	}

	// 缓存配置默认值
	if cfg.Cache.ContentTTL == 0 {
		cfg.Cache.ContentTTL = 5 * time.Minute
	}
	if cfg.Cache.InteractionTTL == 0 {
		cfg.Cache.InteractionTTL = 1 * time.Minute
	}
	if cfg.Cache.StatsTTL == 0 {
		cfg.Cache.StatsTTL = 10 * time.Minute
	}

	// 安全配置默认值
	if cfg.Security.RateLimit.DefaultLimit == 0 {
		cfg.Security.RateLimit.DefaultLimit = 100
	}
	if cfg.Security.RateLimit.DefaultWindow == 0 {
		cfg.Security.RateLimit.DefaultWindow = time.Minute
	}
	if cfg.Security.RateLimit.BatchLimit == 0 {
		cfg.Security.RateLimit.BatchLimit = 10
	}
	if cfg.Security.RateLimit.BatchWindow == 0 {
		cfg.Security.RateLimit.BatchWindow = time.Minute
	}
}

// validateConfig 验证配置
func validateConfig(cfg *Config) error {
	// 验证服务器配置
	if cfg.Server.Port <= 0 || cfg.Server.Port > 65535 {
		return NewConfigError("服务器端口必须在1-65535之间")
	}

	// 验证数据库配置
	if cfg.Database.Host == "" {
		return NewConfigError("数据库主机不能为空")
	}
	if cfg.Database.Database == "" {
		return NewConfigError("数据库名称不能为空")
	}

	// 验证Redis配置
	if cfg.Redis.Address == "" {
		return NewConfigError("Redis地址不能为空")
	}

	// 验证JWT配置
	if cfg.JWT.SecretKey == "" {
		return NewConfigError("JWT密钥不能为空")
	}

	// 验证服务配置
	if cfg.Services.VideoService.Enabled && cfg.Services.VideoService.BaseURL == "" {
		return NewConfigError("视频服务已启用但未配置BaseURL")
	}
	if cfg.Services.InteractionService.Enabled && cfg.Services.InteractionService.BaseURL == "" {
		return NewConfigError("交互服务已启用但未配置BaseURL")
	}

	return nil
}
