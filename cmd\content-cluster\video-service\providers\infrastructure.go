package providers

import (
	"context"

	"pxpat-backend/internal/content-cluster/video-service/migrations"
	"pxpat-backend/internal/content-cluster/video-service/types"
	"pxpat-backend/pkg/auth"
	"pxpat-backend/pkg/cache"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideDatabase 提供数据库连接
func ProvideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	err = migrations.AutoMigrate(db)
	if err != nil {
		log.Fatal().Err(err).Msg("Database migration failed")
		return nil, err
	}
	log.Info().Msg("Database migration completed")
	return db, nil
}

// ProvideRedis 提供Redis连接
func ProvideRedis(cfg *types.Config) (*redis.Client, error) {
	rdb, err := DBLoader.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Redis")
		return nil, err
	}
	log.Info().Msg("Redis connected successfully")
	return rdb, nil
}

// ProvideCacheManager 提供缓存管理器
func ProvideCacheManager(rdb *redis.Client) (cache.Manager, error) {
	cacheManager, err := cache.NewManager(rdb)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cache manager")
		return nil, err
	}
	log.Info().Msg("Cache manager initialized")
	return cacheManager, nil
}

// ProvideConsulManager 提供Consul管理器
func ProvideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	log.Info().Msg("Consul manager initialized successfully")
	return consulManager, nil
}

// ProvideHealthHandler 提供健康检查处理器
func ProvideHealthHandler(consulManager *consul.Manager, db *gorm.DB, rdb *redis.Client) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 添加Redis健康检查
	healthHandler.AddChecker(health.NewRedisHealthChecker("redis", func() error {
		return rdb.Ping(context.Background()).Err()
	}))

	log.Info().Msg("Health handler initialized successfully")
	return healthHandler
}

// ProvideJWTManager 提供JWT管理器
func ProvideJWTManager(cfg *types.Config) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, "pxpat-video")
	log.Info().Msg("JWT manager initialized successfully")
	return &jwtManager
}
