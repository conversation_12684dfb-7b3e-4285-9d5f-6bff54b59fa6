package routes

import (
	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/user-cluster/complaint-service/intra/handler"
	"pxpat-backend/internal/user-cluster/complaint-service/middleware"
)

// RegisterInternalRoutes 注册内部路由
func RegisterInternalRoutes(
	router *gin.Engine,
	internalComplaintHandler *handler.InternalComplaintHandler,
) {
	// 内部API路由组
	internal := router.Group("/internal/v1")

	// 添加内部API认证中间件
	internal.Use(middleware.InternalAPIAuth())

	// 投诉相关内部接口
	complaints := internal.Group("/complaints")
	{
		// 用户投诉统计
		complaints.GET("/users/:user_ksuid/stats", internalComplaintHandler.GetUserComplaintStats)

		// 用户投诉权限检查
		complaints.GET("/users/:user_ksuid/permission", internalComplaintHandler.CanUserComplain)

		// 内容投诉统计
		complaints.GET("/contents/:content_ksuid/stats", internalComplaintHandler.GetContentComplaintStats)

		// 批量获取内容投诉状态
		complaints.GET("/contents/batch-status", internalComplaintHandler.BatchGetContentComplaintStatus)

		// 投诉处理结果
		complaints.GET("/:complaint_ksuid/result", internalComplaintHandler.GetComplaintResult)
	}
}
