package types

import (
	"fmt"
)

// ManagementError 内容管理服务错误类型
type ManagementError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error 实现error接口
func (e *ManagementError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// 错误代码常量
const (
	// 通用错误
	ErrCodeInternalError      = "INTERNAL_ERROR"
	ErrCodeInvalidParameter   = "INVALID_PARAMETER"
	ErrCodeServiceUnavailable = "SERVICE_UNAVAILABLE"
	ErrCodeServiceError       = "SERVICE_ERROR"

	// 内容相关错误
	ErrCodeContentNotFound    = "CONTENT_NOT_FOUND"
	ErrCodeInvalidContentType = "INVALID_CONTENT_TYPE"
	ErrCodeContentExists      = "CONTENT_EXISTS"

	// 权限相关错误
	ErrCodePermissionDenied = "PERMISSION_DENIED"
	ErrCodeUnauthorized     = "UNAUTHORIZED"

	// 业务逻辑错误
	ErrCodeBusinessLogic    = "BUSINESS_LOGIC_ERROR"
	ErrCodeValidationFailed = "VALIDATION_FAILED"
)

// 预定义错误变量
var (
	ErrServiceUnavailable = &ManagementError{Code: ErrCodeServiceUnavailable, Message: "服务不可用"}
	ErrContentNotFound    = &ManagementError{Code: ErrCodeContentNotFound, Message: "内容不存在"}
	ErrInvalidContentType = &ManagementError{Code: ErrCodeInvalidContentType, Message: "无效的内容类型"}
	ErrPermissionDenied   = &ManagementError{Code: ErrCodePermissionDenied, Message: "权限不足"}
	ErrUnauthorized       = &ManagementError{Code: ErrCodeUnauthorized, Message: "未授权访问"}
)

// NewManagementError 创建新的管理错误
func NewManagementError(code, message string) *ManagementError {
	return &ManagementError{
		Code:    code,
		Message: message,
	}
}

// NewManagementErrorWithDetails 创建带详细信息的管理错误
func NewManagementErrorWithDetails(code, message, details string) *ManagementError {
	return &ManagementError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewInternalError 创建内部错误
func NewInternalError(message string) *ManagementError {
	return &ManagementError{
		Code:    ErrCodeInternalError,
		Message: message,
	}
}

// NewValidationError 创建验证错误
func NewValidationError(message string) *ManagementError {
	return &ManagementError{
		Code:    ErrCodeValidationFailed,
		Message: message,
	}
}

// NewBusinessError 创建业务逻辑错误
func NewBusinessError(message string) *ManagementError {
	return &ManagementError{
		Code:    ErrCodeBusinessLogic,
		Message: message,
	}
}

// IsNetworkError 判断是否为网络错误
func IsNetworkError(err error) bool {
	if err == nil {
		return false
	}
	// 这里可以根据具体的网络错误类型进行判断
	// 例如检查是否包含特定的错误字符串
	errStr := err.Error()
	return contains(errStr, "connection refused") ||
		contains(errStr, "timeout") ||
		contains(errStr, "network unreachable") ||
		contains(errStr, "no route to host")
}

// IsHTTPError 判断是否为HTTP错误
func IsHTTPError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return contains(errStr, "HTTP") &&
		(contains(errStr, "4") || contains(errStr, "5"))
}

// IsInteractionServiceError 判断是否为交互服务错误
func IsInteractionServiceError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return contains(errStr, "interaction") || contains(errStr, "like") || contains(errStr, "favorite")
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				containsIgnoreCase(s, substr)))
}

// containsIgnoreCase 忽略大小写检查包含关系
func containsIgnoreCase(s, substr string) bool {
	s = toLower(s)
	substr = toLower(substr)
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// toLower 转换为小写
func toLower(s string) string {
	result := make([]byte, len(s))
	for i := 0; i < len(s); i++ {
		if s[i] >= 'A' && s[i] <= 'Z' {
			result[i] = s[i] + 32
		} else {
			result[i] = s[i]
		}
	}
	return string(result)
}
