<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="用户网关" type="GoApplicationRunConfiguration" factoryName="Go Application" folderName="用户组">
    <module name="pxpat-backend" />
    <working_directory value="$PROJECT_DIR$" />
    <kind value="PACKAGE" />
    <package value="pxpat-backend/cmd/user-cluster/gateway" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/user-cluster/gateway/main.go" />
    <method v="2" />
  </configuration>
</component>