# 内容管理服务 Docker Compose 配置
version: '3.8'

services:
  # 内容管理服务
  content-management-service:
    build:
      context: ../../..
      dockerfile: docker/services/content-management/Dockerfile
    container_name: content-management-service
    restart: unless-stopped
    ports:
      - "${CONTENT_MANAGEMENT_PORT:-12010}:12010"
    environment:
      # 服务配置
      - GO_ENV=${GO_ENV:-production}
      - GIN_MODE=${GIN_MODE:-release}
      
      # 数据库配置
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USER=${DB_USER:-content_user}
      - DB_PASSWORD=${DB_PASSWORD:-content_password}
      - DB_NAME=${DB_NAME:-content_management}
      - DB_SSL_MODE=${DB_SSL_MODE:-disable}
      
      # Redis配置
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      - REDIS_DB=${REDIS_DB:-0}
      
      # JWT配置
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-super-secret-jwt-key-change-in-production}
      - JWT_TOKEN_DURATION=${JWT_TOKEN_DURATION:-1h}
      - JWT_REFRESH_DURATION=${JWT_REFRESH_DURATION:-24h}
      
      # 外部服务配置
      - VIDEO_SERVICE_URL=${VIDEO_SERVICE_URL:-http://video-service:12001}
      - INTERACTION_SERVICE_URL=${INTERACTION_SERVICE_URL:-http://interaction-service:12002}
      
      # Consul配置
      - CONSUL_HOST=${CONSUL_HOST:-consul}
      - CONSUL_PORT=${CONSUL_PORT:-8500}
      - CONSUL_DATACENTER=${CONSUL_DATACENTER:-dc1}
      
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FORMAT=${LOG_FORMAT:-json}
    volumes:
      - ../../logs:/app/logs
      - ../../configs:/app/config:ro
    networks:
      - content-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:12010/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# 网络配置
networks:
  content-network:
    driver: bridge
    external: true
