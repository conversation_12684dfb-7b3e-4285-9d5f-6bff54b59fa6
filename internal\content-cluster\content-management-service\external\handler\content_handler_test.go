package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"pxpat-backend/internal/content-cluster/content-management-service/external/service"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
	"pxpat-backend/pkg/errors"
	globalTypes "pxpat-backend/pkg/types"
)

// MockContentManagementService 模拟内容管理服务
type MockContentManagementService struct {
	mock.Mock
}

func (m *MockContentManagementService) GetBaseContents(ctx context.Context, filters *types.ContentFilters) ([]*types.BaseContent, int64, *errors.Errors) {
	args := m.Called(ctx, filters)
	if args.Get(0) == nil {
		return nil, 0, args.Get(2).(*errors.Errors)
	}
	return args.Get(0).([]*types.BaseContent), args.Get(1).(int64), args.Get(2).(*errors.Errors)
}

func (m *MockContentManagementService) GetVideoContents(ctx context.Context, filters *types.ContentFilters) ([]*types.VideoContentWithDetails, int64, *errors.Errors) {
	args := m.Called(ctx, filters)
	if args.Get(0) == nil {
		return nil, 0, args.Get(2).(*errors.Errors)
	}
	return args.Get(0).([]*types.VideoContentWithDetails), args.Get(1).(int64), args.Get(2).(*errors.Errors)
}

func (m *MockContentManagementService) GetContentWithDetails(ctx context.Context, contentKSUID string) (interface{}, *errors.Errors) {
	args := m.Called(ctx, contentKSUID)
	return args.Get(0), args.Get(1).(*errors.Errors)
}

func (m *MockContentManagementService) UpdateContentStatus(ctx context.Context, contentKSUID, status, userKSUID string) *errors.Errors {
	args := m.Called(ctx, contentKSUID, status, userKSUID)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*errors.Errors)
}

func (m *MockContentManagementService) DeleteContent(ctx context.Context, contentKSUID, userKSUID string) *errors.Errors {
	args := m.Called(ctx, contentKSUID, userKSUID)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*errors.Errors)
}

func (m *MockContentManagementService) GetUserAllContents(ctx context.Context, userKSUID string, page, limit int) ([]*types.BaseContent, int64, *errors.Errors) {
	args := m.Called(ctx, userKSUID, page, limit)
	if args.Get(0) == nil {
		return nil, 0, args.Get(2).(*errors.Errors)
	}
	return args.Get(0).([]*types.BaseContent), args.Get(1).(int64), args.Get(2).(*errors.Errors)
}

func (m *MockContentManagementService) GetUserContentsByType(ctx context.Context, userKSUID, contentType string, page, limit int) ([]*types.BaseContent, int64, *errors.Errors) {
	args := m.Called(ctx, userKSUID, contentType, page, limit)
	if args.Get(0) == nil {
		return nil, 0, args.Get(2).(*errors.Errors)
	}
	return args.Get(0).([]*types.BaseContent), args.Get(1).(int64), args.Get(2).(*errors.Errors)
}

func (m *MockContentManagementService) BatchUpdateContentStatus(ctx context.Context, contentKSUIDs []string, status, userKSUID string) *errors.Errors {
	args := m.Called(ctx, contentKSUIDs, status, userKSUID)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*errors.Errors)
}

func (m *MockContentManagementService) BatchDeleteContents(ctx context.Context, contentKSUIDs []string, userKSUID string) *errors.Errors {
	args := m.Called(ctx, contentKSUIDs, userKSUID)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*errors.Errors)
}

// 创建测试用的Gin引擎
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	return router
}

// 创建测试用的ContentHandler
func createTestContentHandler() (*ContentHandler, *MockContentManagementService) {
	mockService := &MockContentManagementService{}
	handler := NewContentHandler(mockService)
	return handler, mockService
}

func TestContentHandler_GetBaseContents(t *testing.T) {
	handler, mockService := createTestContentHandler()
	router := setupTestRouter()
	router.GET("/contents", handler.GetBaseContents)

	// 准备测试数据
	expectedContents := []*types.BaseContent{
		{
			KSUID:       "content_1",
			ContentType: "video",
			Title:       "测试内容1",
			Description: "描述1",
			UserKSUID:   "user_1",
			Status:      "published",
			CreatedAt:   time.Now(),
		},
		{
			KSUID:       "content_2",
			ContentType: "video",
			Title:       "测试内容2",
			Description: "描述2",
			UserKSUID:   "user_2",
			Status:      "published",
			CreatedAt:   time.Now(),
		},
	}

	// 设置mock期望
	mockService.On("GetBaseContents", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("*types.ContentFilters")).
		Return(expectedContents, int64(2), (*errors.Errors)(nil))

	// 创建请求
	req, _ := http.NewRequest("GET", "/contents?page=1&limit=10&status=published", nil)
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, errors.SUCCESS, response.Code)
	assert.NotNil(t, response.Data)

	// 验证mock调用
	mockService.AssertExpectations(t)
}

func TestContentHandler_GetBaseContents_ValidationError(t *testing.T) {
	handler, mockService := createTestContentHandler()
	router := setupTestRouter()
	router.GET("/contents", handler.GetBaseContents)

	// 设置mock期望 - 验证错误
	validationError := errors.NewValidationError("参数验证失败")
	mockService.On("GetBaseContents", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("*types.ContentFilters")).
		Return(([]*types.BaseContent)(nil), int64(0), validationError)

	// 创建请求 - 无效参数
	req, _ := http.NewRequest("GET", "/contents?page=0&limit=1001", nil)
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response globalTypes.GlobalResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, errors.INVALID_PARAMETER, response.Code)
	assert.Contains(t, response.Msg, "参数验证失败")

	// 验证mock调用
	mockService.AssertExpectations(t)
}

func TestContentHandler_GetContentWithDetails(t *testing.T) {
	handler, mockService := createTestContentHandler()
	router := setupTestRouter()
	router.GET("/contents/:content_ksuid", handler.GetContentWithDetails)

	// 准备测试数据
	contentKSUID := "video_123"
	expectedContent := &types.VideoContentWithDetails{
		KSUID:       contentKSUID,
		Title:       "测试视频",
		Description: "测试描述",
		UserKSUID:   "user_123",
		Status:      "published",
		Duration:    300,
		Resolution:  "1920x1080",
		FileSize:    1024000,
		InteractionStats: types.InteractionStats{
			Views:     1000,
			Likes:     50,
			Favorites: 20,
		},
	}

	// 设置mock期望
	mockService.On("GetContentWithDetails", mock.AnythingOfType("*gin.Context"), contentKSUID).
		Return(expectedContent, (*errors.Errors)(nil))

	// 创建请求
	req, _ := http.NewRequest("GET", "/contents/"+contentKSUID, nil)
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, errors.SUCCESS, response.Code)
	assert.NotNil(t, response.Data)

	// 验证mock调用
	mockService.AssertExpectations(t)
}

func TestContentHandler_UpdateContentStatus(t *testing.T) {
	handler, mockService := createTestContentHandler()
	router := setupTestRouter()

	// 添加用户KSUID中间件模拟
	router.Use(func(c *gin.Context) {
		c.Set("user_ksuid", "user_123")
		c.Next()
	})

	router.PUT("/contents/:content_ksuid/status", handler.UpdateContentStatus)

	contentKSUID := "video_123"
	newStatus := "archived"
	userKSUID := "user_123"

	// 设置mock期望
	mockService.On("UpdateContentStatus", mock.AnythingOfType("*gin.Context"), contentKSUID, newStatus, userKSUID).
		Return((*errors.Errors)(nil))

	// 准备请求体
	requestBody := map[string]string{
		"status": newStatus,
	}
	jsonBody, _ := json.Marshal(requestBody)

	// 创建请求
	req, _ := http.NewRequest("PUT", "/contents/"+contentKSUID+"/status", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, errors.SUCCESS, response.Code)

	// 验证mock调用
	mockService.AssertExpectations(t)
}

func TestContentHandler_DeleteContent(t *testing.T) {
	handler, mockService := createTestContentHandler()
	router := setupTestRouter()

	// 添加用户KSUID中间件模拟
	router.Use(func(c *gin.Context) {
		c.Set("user_ksuid", "user_123")
		c.Next()
	})

	router.DELETE("/contents/:content_ksuid", handler.DeleteContent)

	contentKSUID := "video_123"
	userKSUID := "user_123"

	// 设置mock期望
	mockService.On("DeleteContent", mock.AnythingOfType("*gin.Context"), contentKSUID, userKSUID).
		Return((*errors.Errors)(nil))

	// 创建请求
	req, _ := http.NewRequest("DELETE", "/contents/"+contentKSUID, nil)
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, errors.SUCCESS, response.Code)

	// 验证mock调用
	mockService.AssertExpectations(t)
}

func TestContentHandler_HealthCheck(t *testing.T) {
	handler, _ := createTestContentHandler()
	router := setupTestRouter()
	router.GET("/health", handler.HealthCheck)

	// 创建请求
	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, errors.SUCCESS, response.Code)
	assert.Equal(t, "服务健康", response.Msg)
}

func TestContentHandler_BatchUpdateContentStatus(t *testing.T) {
	handler, mockService := createTestContentHandler()
	router := setupTestRouter()

	// 添加用户KSUID中间件模拟
	router.Use(func(c *gin.Context) {
		c.Set("user_ksuid", "user_123")
		c.Next()
	})

	router.PUT("/contents/batch/status", handler.BatchUpdateContentStatus)

	contentKSUIDs := []string{"video_1", "video_2", "video_3"}
	newStatus := "archived"
	userKSUID := "user_123"

	// 设置mock期望
	mockService.On("BatchUpdateContentStatus", mock.AnythingOfType("*gin.Context"), contentKSUIDs, newStatus, userKSUID).
		Return((*errors.Errors)(nil))

	// 准备请求体
	requestBody := map[string]interface{}{
		"content_ksuids": contentKSUIDs,
		"status":         newStatus,
	}
	jsonBody, _ := json.Marshal(requestBody)

	// 创建请求
	req, _ := http.NewRequest("PUT", "/contents/batch/status", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, errors.SUCCESS, response.Code)

	// 验证mock调用
	mockService.AssertExpectations(t)
}
