# 投诉服务API文档

## 概述

这是PXPAT平台投诉服务的OpenAPI 3.0规范文档，包含了完整的API接口定义。

## 文件信息

- **文件名**: `complaint-service-api.json`
- **格式**: OpenAPI 3.0.3
- **服务端口**: 11003
- **基础路径**: `/api/v1`

## 主要功能模块

### 1. 投诉管理 (`/complaints`)
- 创建投诉
- 获取投诉详情
- 更新投诉信息
- 删除投诉
- 获取我的投诉列表
- 获取针对我的投诉列表
- 获取投诉统计信息
- 获取违规类别列表

### 2. 身份认证 (`/identity`)
- 创建身份认证申请
- 获取身份认证详情
- 更新身份认证信息
- 获取我的身份认证列表
- 获取国家地区列表
- 获取商标类别列表

### 3. 权益认证 (`/rights`)
- 创建权益认证申请
- 获取权益认证详情
- 获取我的权益认证列表

### 4. 管理员接口
- **投诉管理** (`/admin/complaints`)
  - 获取投诉列表（管理员视图）
  - 处理投诉（通过/拒绝/要求补充材料）

- **身份认证管理** (`/admin/identity`)
  - 获取身份认证列表（管理员视图）
  - 审核身份认证申请

- **权益认证管理** (`/admin/rights`)
  - 获取权益认证列表（管理员视图）
  - 审核权益认证申请

## 认证方式

所有API接口都使用JWT Bearer Token认证：

```
Authorization: Bearer <your-jwt-token>
```

## 数据模型

### 投诉状态
- `pending`: 待处理
- `processing`: 处理中
- `resolved`: 已解决
- `rejected`: 已拒绝

### 身份认证类型
- `personal`: 个人认证
- `enterprise`: 企业认证

### 权益认证类型
- `copyright`: 版权
- `trademark`: 商标
- `personality_right`: 人格权

### 审核状态
- `pending`: 待审核
- `approved`: 已通过
- `rejected`: 已拒绝

## 使用方法

1. **导入到API工具**：
   - 可以将`complaint-service-api.json`导入到Postman、Insomnia、Swagger UI等工具中
   - 支持自动生成API客户端代码

2. **在线查看**：
   - 可以使用Swagger Editor在线查看：https://editor.swagger.io/
   - 将JSON内容粘贴到编辑器中即可查看完整的API文档

3. **生成客户端代码**：
   - 支持生成多种语言的客户端SDK
   - 可以使用OpenAPI Generator工具

## 服务信息

- **服务名称**: complaint-service
- **版本**: 1.0.0
- **开发环境**: http://localhost:11003
- **生产环境**: https://api.pxpat.com

## 联系信息

- **技术支持**: <EMAIL>
- **文档更新**: 2025-01-31
