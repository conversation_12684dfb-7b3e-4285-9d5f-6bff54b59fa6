# 内容管理服务故障排查指南

## 概述

本文档提供内容管理服务常见问题的诊断和解决方案，帮助运维人员快速定位和解决问题。

## 快速诊断

### 健康检查清单

```bash
# 1. 检查服务状态
curl -f http://localhost:12010/api/v1/health

# 2. 检查容器状态
docker ps | grep content-management

# 3. 检查资源使用
docker stats content-management-service

# 4. 检查日志
docker logs --tail=100 content-management-service
```

### 系统状态检查

```bash
# 检查系统负载
uptime

# 检查内存使用
free -h

# 检查磁盘空间
df -h

# 检查网络连接
netstat -tlnp | grep :12010
```

## 常见问题及解决方案

### 1. 服务启动问题

#### 问题：服务无法启动

**症状**:
- 容器启动后立即退出
- 健康检查失败
- 端口无法访问

**诊断步骤**:

```bash
# 查看容器状态
docker ps -a | grep content-management

# 查看启动日志
docker logs content-management-service

# 检查配置文件
docker exec content-management-service cat /app/config/config.yaml
```

**常见原因和解决方案**:

1. **配置文件错误**
   ```bash
   # 验证配置文件语法
   docker run --rm -v $(pwd)/config:/config \
     mikefarah/yq eval /config/config.yaml
   
   # 检查必需的环境变量
   docker exec content-management-service env | grep -E "(DB_|REDIS_|JWT_)"
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :12010
   
   # 修改端口配置
   vim docker-compose.yml  # 修改ports映射
   ```

3. **权限问题**
   ```bash
   # 检查文件权限
   ls -la config/
   
   # 修复权限
   chmod 644 config/config.yaml
   chown 1001:1001 config/
   ```

#### 问题：服务启动缓慢

**症状**:
- 容器启动时间超过2分钟
- 健康检查超时

**解决方案**:

```bash
# 检查依赖服务状态
docker-compose ps

# 优化启动顺序
# 在docker-compose.yml中添加depends_on和healthcheck
services:
  content-management-service:
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
```

### 2. 数据库连接问题

#### 问题：数据库连接失败

**症状**:
- 日志显示"connection refused"
- API返回500错误
- 数据库操作超时

**诊断步骤**:

```bash
# 检查PostgreSQL状态
docker exec content-postgres pg_isready -U content_user

# 测试网络连接
docker exec content-management-service nc -zv postgres 5432

# 检查数据库日志
docker logs content-postgres | tail -50
```

**解决方案**:

1. **数据库未启动**
   ```bash
   # 启动数据库
   docker-compose up -d postgres
   
   # 等待数据库就绪
   docker exec content-postgres pg_isready -U content_user
   ```

2. **连接参数错误**
   ```bash
   # 验证连接参数
   docker exec content-postgres psql -U content_user -d content_management -c "SELECT 1;"
   
   # 检查环境变量
   docker exec content-management-service env | grep DB_
   ```

3. **连接池耗尽**
   ```sql
   -- 检查活跃连接数
   SELECT count(*) FROM pg_stat_activity WHERE state = 'active';
   
   -- 检查连接池配置
   SHOW max_connections;
   ```

#### 问题：数据库性能问题

**症状**:
- 查询响应时间长
- CPU使用率高
- 大量慢查询

**诊断和解决**:

```sql
-- 查看慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

-- 检查锁等待
SELECT * FROM pg_locks WHERE NOT granted;

-- 分析表统计信息
ANALYZE;

-- 重建索引
REINDEX INDEX CONCURRENTLY idx_operation_logs_created_at;
```

### 3. Redis连接问题

#### 问题：Redis连接失败

**症状**:
- 缓存功能不可用
- 日志显示Redis连接错误

**诊断步骤**:

```bash
# 检查Redis状态
docker exec content-redis redis-cli ping

# 测试连接
docker exec content-management-service redis-cli -h redis -p 6379 ping

# 检查Redis日志
docker logs content-redis | tail -50
```

**解决方案**:

```bash
# 重启Redis
docker-compose restart redis

# 检查Redis配置
docker exec content-redis redis-cli config get "*"

# 清理Redis数据（谨慎操作）
docker exec content-redis redis-cli flushall
```

### 4. 性能问题

#### 问题：响应时间过长

**症状**:
- API响应时间超过1秒
- 用户体验差

**诊断步骤**:

```bash
# 检查系统资源
top -p $(docker inspect -f '{{.State.Pid}}' content-management-service)

# 分析请求日志
docker logs content-management-service | grep "duration" | tail -20

# 检查数据库性能
docker exec content-postgres pg_stat_statements_reset();
# 运行一些查询后
docker exec content-postgres psql -U content_user -d content_management \
  -c "SELECT query, mean_time FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 5;"
```

**优化方案**:

1. **数据库优化**
   ```sql
   -- 创建缺失的索引
   CREATE INDEX CONCURRENTLY idx_contents_status_created 
   ON contents(status, created_at);
   
   -- 更新表统计信息
   ANALYZE contents;
   ```

2. **缓存优化**
   ```bash
   # 检查缓存命中率
   docker exec content-redis redis-cli info stats | grep keyspace
   
   # 调整缓存策略
   # 在应用配置中增加缓存TTL
   ```

3. **应用优化**
   ```bash
   # 检查Goroutine数量
   curl http://localhost:12010/debug/pprof/goroutine?debug=1
   
   # 检查内存使用
   curl http://localhost:12010/debug/pprof/heap?debug=1
   ```

#### 问题：内存使用过高

**症状**:
- 容器内存使用超过限制
- 系统出现OOM

**诊断和解决**:

```bash
# 检查内存使用详情
docker exec content-management-service cat /proc/meminfo

# 生成内存profile
curl http://localhost:12010/debug/pprof/heap > heap.prof
go tool pprof heap.prof

# 调整内存限制
# 在docker-compose.yml中修改
services:
  content-management-service:
    deploy:
      resources:
        limits:
          memory: 1G
```

### 5. 网络问题

#### 问题：服务间通信失败

**症状**:
- 外部服务调用失败
- 服务发现问题

**诊断步骤**:

```bash
# 检查网络连接
docker exec content-management-service nslookup video-service
docker exec content-management-service curl -I http://video-service:12001/health

# 检查Consul服务注册
curl http://localhost:8500/v1/catalog/services
```

**解决方案**:

```bash
# 重启网络
docker-compose down
docker-compose up -d

# 检查DNS解析
docker exec content-management-service cat /etc/resolv.conf

# 手动注册服务到Consul
curl -X PUT http://localhost:8500/v1/agent/service/register \
  -d @service-registration.json
```

### 6. 监控告警问题

#### 问题：告警不生效

**症状**:
- 服务异常但未收到告警
- Prometheus无法抓取指标

**诊断步骤**:

```bash
# 检查Prometheus targets
curl http://localhost:9090/api/v1/targets

# 检查告警规则
curl http://localhost:9090/api/v1/rules

# 检查Alertmanager状态
curl http://localhost:9093/api/v1/status
```

**解决方案**:

```bash
# 重新加载Prometheus配置
curl -X POST http://localhost:9090/-/reload

# 测试告警规则
curl -X POST http://localhost:9093/api/v1/alerts

# 检查指标端点
curl http://localhost:12010/metrics
```

## 日志分析

### 日志级别说明

- **ERROR**: 需要立即处理的错误
- **WARN**: 需要关注的警告
- **INFO**: 一般信息
- **DEBUG**: 调试信息

### 常用日志查询

```bash
# 查看错误日志
docker logs content-management-service 2>&1 | grep ERROR

# 查看最近的警告
docker logs --since="1h" content-management-service | grep WARN

# 实时监控日志
docker logs -f content-management-service | grep -E "(ERROR|WARN)"

# 导出日志到文件
docker logs content-management-service > service.log 2>&1
```

### 日志分析工具

```bash
# 使用jq分析JSON日志
docker logs content-management-service | jq 'select(.level=="ERROR")'

# 统计错误类型
docker logs content-management-service | grep ERROR | awk '{print $4}' | sort | uniq -c

# 分析响应时间
docker logs content-management-service | grep "duration" | awk '{print $NF}' | sort -n
```

## 应急处理

### 服务降级

```bash
# 启用维护模式
docker exec content-management-service \
  curl -X POST http://localhost:12010/admin/maintenance/enable

# 切换到只读模式
docker exec content-management-service \
  curl -X POST http://localhost:12010/admin/readonly/enable
```

### 数据恢复

```bash
# 从备份恢复数据库
docker exec -i content-postgres psql -U content_user content_management < backup.sql

# 恢复Redis数据
docker cp redis_backup.rdb content-redis:/data/dump.rdb
docker-compose restart redis
```

### 紧急扩容

```bash
# 快速扩容服务实例
docker-compose up -d --scale content-management-service=3

# 临时增加资源限制
docker update --memory=2g --cpus=2 content-management-service
```

## 预防措施

### 监控配置

```yaml
# 添加更多健康检查
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:12010/api/v1/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
```

### 自动化脚本

```bash
# 创建自动重启脚本
cat > auto-restart.sh << 'EOF'
#!/bin/bash
if ! curl -f http://localhost:12010/api/v1/health; then
  echo "Service unhealthy, restarting..."
  docker-compose restart content-management-service
  sleep 30
  if curl -f http://localhost:12010/api/v1/health; then
    echo "Service recovered"
  else
    echo "Service still unhealthy, manual intervention required"
    # 发送告警通知
  fi
fi
EOF
```

## 联系支持

### 收集诊断信息

```bash
# 生成诊断报告
cat > collect-diagnostics.sh << 'EOF'
#!/bin/bash
REPORT_DIR="diagnostics-$(date +%Y%m%d-%H%M%S)"
mkdir -p $REPORT_DIR

# 收集系统信息
uname -a > $REPORT_DIR/system-info.txt
docker version > $REPORT_DIR/docker-version.txt
docker-compose version > $REPORT_DIR/compose-version.txt

# 收集服务状态
docker ps > $REPORT_DIR/containers.txt
docker-compose ps > $REPORT_DIR/services.txt

# 收集日志
docker logs content-management-service > $REPORT_DIR/service.log 2>&1
docker logs content-postgres > $REPORT_DIR/postgres.log 2>&1
docker logs content-redis > $REPORT_DIR/redis.log 2>&1

# 收集配置
docker-compose config > $REPORT_DIR/compose-config.yml

# 打包
tar -czf $REPORT_DIR.tar.gz $REPORT_DIR
echo "诊断信息已收集到: $REPORT_DIR.tar.gz"
EOF

chmod +x collect-diagnostics.sh
./collect-diagnostics.sh
```

### 支持联系方式

- **技术支持**: <EMAIL>
- **紧急热线**: +86-xxx-xxxx-xxxx
- **在线文档**: https://docs.pxpat.com
- **GitHub Issues**: https://github.com/your-org/pxpat-backend/issues
