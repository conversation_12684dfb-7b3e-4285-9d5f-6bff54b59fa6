package service

import (
	"pxpat-backend/internal/content-cluster/audit-service/client"
	publisher "pxpat-backend/internal/content-cluster/audit-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/audit-service/repository"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideExternalAuditTasksService 提供外部审核任务服务
func ProvideExternalAuditTasksService(
	auditTasksRepository *repository.AuditTasksRepository,
	contentServiceClient client.ContentServiceClient,
	userServiceClient client.UserServiceClient,
	tokenServiceClient client.TokenServiceClient,
	mqPublisher *publisher.Publisher,
	db *gorm.DB,
) *ExternalAuditTasksService {
	externalAuditService := NewExternalAuditTasksService(
		auditTasksRepository,
		contentServiceClient,
		userServiceClient,
		tokenServiceClient,
		mqPublisher,
		db,
	)
	log.Info().Msg("External audit tasks service initialized successfully")
	return externalAuditService
}
