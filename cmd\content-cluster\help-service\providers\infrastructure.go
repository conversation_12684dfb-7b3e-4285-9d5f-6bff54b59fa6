package providers

import (
	"pxpat-backend/internal/content-cluster/help-service/migrations"
	"pxpat-backend/internal/content-cluster/help-service/types"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	"pxpat-backend/pkg/database"
	"pxpat-backend/pkg/opentelemetry"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideOtelProvider 提供OpenTelemetry Provider
func ProvideOtelProvider(cfg *types.Config) (*opentelemetry.Provider, error) {
	otelProvider, err := opentelemetry.NewProvider(cfg.Otlp)
	if err != nil {
		log.Fatal().Err(err).Msg("OpenTelemetry 初始化失败")
		return nil, err
	}

	if otelProvider.IsTracingEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Tracing.ServiceName).
			Str("exporter_type", cfg.Otlp.Tracing.ExporterType).
			Msg("链路追踪已启用")
	} else {
		log.Info().Msg("链路追踪已禁用")
	}

	if otelProvider.IsMetricsEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Metrics.ServiceName).
			Str("exporter_type", cfg.Otlp.Metrics.ExporterType).
			Msg("指标收集已启用")
	} else {
		log.Info().Msg("指标收集已禁用")
	}

	log.Info().Msg("OpenTelemetry Provider 初始化成功")
	return otelProvider, nil
}

// ProvideDatabase 提供数据库连接
func ProvideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := database.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Msgf("数据库连接失败: %v", err)
		return nil, err
	}

	// 数据库迁移
	if err := migrations.AutoMigrate(db); err != nil {
		log.Fatal().Msgf("数据库迁移失败: %v", err)
		return nil, err
	}

	log.Info().Msg("数据库连接和迁移成功")
	return db, nil
}

// ProvideConsulManager 提供Consul管理器
func ProvideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}

	log.Info().Msg("Consul管理器初始化成功")
	return consulManager, nil
}

// ProvideHealthHandler 提供健康检查处理器
func ProvideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	log.Info().Msg("健康检查处理器初始化成功")
	return healthHandler
}
