package utils

import (
	"regexp"
	"strings"
	"unicode/utf8"
)

// ValidateKSUID 验证KSUID格式
func ValidateKSUID(ksuid string) bool {
	if len(ksuid) != 27 {
		return false
	}

	// KSUID使用base62编码，包含数字和大小写字母
	matched, _ := regexp.MatchString("^[0-9A-Za-z]{27}$", ksuid)
	return matched
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) bool {
	if email == "" {
		return false
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// ValidatePhone 验证手机号格式
func ValidatePhone(phone string) bool {
	if phone == "" {
		return false
	}

	// 支持中国大陆、香港、澳门、台湾手机号
	phoneRegex := regexp.MustCompile(`^(\+86|86)?1[3-9]\d{9}$|^(\+852|852)?[5-9]\d{7}$|^(\+853|853)?6\d{7}$|^(\+886|886)?9\d{8}$`)
	return phoneRegex.MatchString(phone)
}

// ValidateIDCard 验证身份证号格式
func ValidateIDCard(idCard string) bool {
	if idCard == "" {
		return false
	}

	// 中国大陆身份证号：18位数字，最后一位可能是X
	if len(idCard) == 18 {
		idCardRegex := regexp.MustCompile(`^\d{17}[\dXx]$`)
		return idCardRegex.MatchString(idCard)
	}

	// 香港身份证号：1-2位字母 + 6位数字 + 1位校验码
	if len(idCard) >= 8 && len(idCard) <= 10 {
		hkIdRegex := regexp.MustCompile(`^[A-Za-z]{1,2}\d{6}[\dA]$`)
		return hkIdRegex.MatchString(idCard)
	}

	return false
}

// ValidatePassport 验证护照号格式
func ValidatePassport(passport string) bool {
	if passport == "" {
		return false
	}

	// 护照号通常是6-9位字母数字组合
	passportRegex := regexp.MustCompile(`^[A-Za-z0-9]{6,9}$`)
	return passportRegex.MatchString(passport)
}

// ValidateBusinessLicense 验证营业执照号格式
func ValidateBusinessLicense(license string) bool {
	if license == "" {
		return false
	}

	// 统一社会信用代码：18位数字字母组合
	if len(license) == 18 {
		creditCodeRegex := regexp.MustCompile(`^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$`)
		return creditCodeRegex.MatchString(license)
	}

	// 旧版营业执照号：15位数字
	if len(license) == 15 {
		oldLicenseRegex := regexp.MustCompile(`^\d{15}$`)
		return oldLicenseRegex.MatchString(license)
	}

	return false
}

// ValidateTitle 验证标题
func ValidateTitle(title string) (bool, string) {
	if title == "" {
		return false, "标题不能为空"
	}

	title = strings.TrimSpace(title)
	if title == "" {
		return false, "标题不能为空"
	}

	if utf8.RuneCountInString(title) > 255 {
		return false, "标题长度不能超过255个字符"
	}

	// 检查是否包含敏感词
	if containsSensitiveWords(title) {
		return false, "标题包含敏感词"
	}

	return true, ""
}

// ValidateDescription 验证描述
func ValidateDescription(description string) (bool, string) {
	if description == "" {
		return false, "描述不能为空"
	}

	description = strings.TrimSpace(description)
	if description == "" {
		return false, "描述不能为空"
	}

	if utf8.RuneCountInString(description) > 5000 {
		return false, "描述长度不能超过5000个字符"
	}

	// 检查是否包含敏感词
	if containsSensitiveWords(description) {
		return false, "描述包含敏感词"
	}

	return true, ""
}

// ValidateURL 验证URL格式
func ValidateURL(url string) bool {
	if url == "" {
		return false
	}

	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	return urlRegex.MatchString(url)
}

// ValidateFileExtension 验证文件扩展名
func ValidateFileExtension(filename string, allowedExts []string) bool {
	if filename == "" {
		return false
	}

	ext := strings.ToLower(getFileExtension(filename))
	for _, allowedExt := range allowedExts {
		if ext == strings.ToLower(allowedExt) {
			return true
		}
	}

	return false
}

// getFileExtension 获取文件扩展名
func getFileExtension(filename string) string {
	parts := strings.Split(filename, ".")
	if len(parts) < 2 {
		return ""
	}
	return "." + parts[len(parts)-1]
}

// ValidateFileSize 验证文件大小
func ValidateFileSize(size int64, maxSize int64) bool {
	return size > 0 && size <= maxSize
}

// containsSensitiveWords 检查是否包含敏感词（简化实现）
func containsSensitiveWords(text string) bool {
	// 这里应该接入专业的敏感词检测服务
	// 简化实现，只检查一些基本的敏感词
	sensitiveWords := []string{
		"政治敏感词",
		"暴力",
		"色情",
		"赌博",
		"毒品",
		"诈骗",
	}

	text = strings.ToLower(text)
	for _, word := range sensitiveWords {
		if strings.Contains(text, strings.ToLower(word)) {
			return true
		}
	}

	return false
}

// SanitizeString 清理字符串
func SanitizeString(str string) string {
	// 移除前后空格
	str = strings.TrimSpace(str)

	// 移除多余的空格
	spaceRegex := regexp.MustCompile(`\s+`)
	str = spaceRegex.ReplaceAllString(str, " ")

	// 移除特殊字符（保留基本标点符号）
	specialCharRegex := regexp.MustCompile(`[^\w\s\u4e00-\u9fff.,!?;:()""''【】（）。，！？；：]`)
	str = specialCharRegex.ReplaceAllString(str, "")

	return str
}

// MaskSensitiveInfo 脱敏敏感信息
func MaskSensitiveInfo(info string, infoType string) string {
	if info == "" {
		return ""
	}

	switch infoType {
	case "phone":
		if len(info) >= 7 {
			return info[:3] + "****" + info[len(info)-4:]
		}
	case "email":
		parts := strings.Split(info, "@")
		if len(parts) == 2 && len(parts[0]) > 2 {
			return parts[0][:2] + "***@" + parts[1]
		}
	case "id_card":
		if len(info) >= 8 {
			return info[:4] + "**********" + info[len(info)-4:]
		}
	case "name":
		if utf8.RuneCountInString(info) > 1 {
			runes := []rune(info)
			return string(runes[0]) + "*" + string(runes[len(runes)-1])
		}
	}

	return info
}

// GenerateComplaintNumber 生成投诉编号
func GenerateComplaintNumber() string {
	// 格式：CP + 年月日 + 6位随机数
	// 例如：CP20240101123456
	return "CP" + generateDateString() + generateRandomNumber(6)
}

// GenerateVerificationNumber 生成认证编号
func GenerateVerificationNumber(prefix string) string {
	// 格式：前缀 + 年月日 + 6位随机数
	// 例如：ID20240101123456, RT20240101123456
	return prefix + generateDateString() + generateRandomNumber(6)
}

// generateDateString 生成日期字符串
func generateDateString() string {
	// 这里应该使用当前日期，简化实现
	return "20240101"
}

// generateRandomNumber 生成随机数字字符串
func generateRandomNumber(length int) string {
	// 这里应该使用真正的随机数生成，简化实现
	result := ""
	for i := 0; i < length; i++ {
		result += "1"
	}
	return result
}
