package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// BaseClient 基础HTTP客户端
type BaseClient struct {
	httpClient *http.Client
	baseURL    string
	timeout    time.Duration
	retryCount int
	retryDelay time.Duration
}

// ClientConfig 客户端配置
type ClientConfig struct {
	BaseURL    string
	Timeout    time.Duration
	RetryCount int
	RetryDelay time.Duration
}

// NewBaseClient 创建基础客户端
func NewBaseClient(config ClientConfig) *BaseClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.RetryCount == 0 {
		config.RetryCount = 3
	}
	if config.RetryDelay == 0 {
		config.RetryDelay = 1 * time.Second
	}

	return &BaseClient{
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		baseURL:    config.BaseURL,
		timeout:    config.Timeout,
		retryCount: config.RetryCount,
		retryDelay: config.RetryDelay,
	}
}

// Get 发送GET请求
func (c *BaseClient) Get(endpoint string, result interface{}) error {
	return c.GetWithContext(context.Background(), endpoint, result)
}

// GetWithContext 发送带上下文的GET请求
func (c *BaseClient) GetWithContext(ctx context.Context, endpoint string, result interface{}) error {
	url := c.baseURL + endpoint

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create GET request: %w", err)
	}

	return c.doRequestWithRetry(req, result)
}

// Post 发送POST请求
func (c *BaseClient) Post(endpoint string, data interface{}, result interface{}) error {
	return c.PostWithContext(context.Background(), endpoint, data, result)
}

// PostWithContext 发送带上下文的POST请求
func (c *BaseClient) PostWithContext(ctx context.Context, endpoint string, data interface{}, result interface{}) error {
	url := c.baseURL + endpoint

	var body io.Reader
	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return fmt.Errorf("failed to marshal request data: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, body)
	if err != nil {
		return fmt.Errorf("failed to create POST request: %w", err)
	}

	if data != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return c.doRequestWithRetry(req, result)
}

// Put 发送PUT请求
func (c *BaseClient) Put(endpoint string, data interface{}, result interface{}) error {
	return c.PutWithContext(context.Background(), endpoint, data, result)
}

// PutWithContext 发送带上下文的PUT请求
func (c *BaseClient) PutWithContext(ctx context.Context, endpoint string, data interface{}, result interface{}) error {
	url := c.baseURL + endpoint

	var body io.Reader
	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return fmt.Errorf("failed to marshal request data: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequestWithContext(ctx, "PUT", url, body)
	if err != nil {
		return fmt.Errorf("failed to create PUT request: %w", err)
	}

	if data != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return c.doRequestWithRetry(req, result)
}

// Delete 发送DELETE请求
func (c *BaseClient) Delete(endpoint string) error {
	return c.DeleteWithContext(context.Background(), endpoint)
}

// DeleteWithContext 发送带上下文的DELETE请求
func (c *BaseClient) DeleteWithContext(ctx context.Context, endpoint string) error {
	url := c.baseURL + endpoint

	req, err := http.NewRequestWithContext(ctx, "DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create DELETE request: %w", err)
	}

	return c.doRequestWithRetry(req, nil)
}

// doRequestWithRetry 执行带重试的请求
func (c *BaseClient) doRequestWithRetry(req *http.Request, result interface{}) error {
	var lastErr error

	for attempt := 0; attempt <= c.retryCount; attempt++ {
		if attempt > 0 {
			log.Debug().
				Str("url", req.URL.String()).
				Str("method", req.Method).
				Int("attempt", attempt).
				Msg("重试HTTP请求")

			time.Sleep(c.retryDelay * time.Duration(attempt))
		}

		resp, err := c.httpClient.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("HTTP request failed: %w", err)

			// 网络错误，继续重试
			if types.IsNetworkError(err) {
				continue
			}

			// 其他错误，直接返回
			return lastErr
		}

		defer resp.Body.Close()

		// 读取响应体
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			lastErr = fmt.Errorf("failed to read response body: %w", err)
			continue
		}

		// 检查HTTP状态码
		if resp.StatusCode >= 500 {
			// 服务器错误，重试
			lastErr = fmt.Errorf("server error: HTTP %d", resp.StatusCode)
			continue
		} else if resp.StatusCode >= 400 {
			// 客户端错误，不重试
			return c.handleErrorResponse(resp.StatusCode, body)
		}

		// 成功响应，解析结果
		if result != nil {
			if err := json.Unmarshal(body, result); err != nil {
				return fmt.Errorf("failed to unmarshal response: %w", err)
			}
		}

		log.Debug().
			Str("url", req.URL.String()).
			Str("method", req.Method).
			Int("status_code", resp.StatusCode).
			Int("attempt", attempt).
			Msg("HTTP请求成功")

		return nil
	}

	return fmt.Errorf("request failed after %d attempts: %w", c.retryCount+1, lastErr)
}

// handleErrorResponse 处理错误响应
func (c *BaseClient) handleErrorResponse(statusCode int, body []byte) error {
	// 尝试解析错误响应
	var errorResp struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data,omitempty"`
	}

	if err := json.Unmarshal(body, &errorResp); err != nil {
		// 无法解析错误响应，返回通用错误
		return types.NewManagementErrorWithDetails(
			types.ErrCodeServiceError,
			fmt.Sprintf("HTTP %d error", statusCode),
			string(body),
		)
	}

	// 根据错误代码映射到管理错误
	var managementErr *types.ManagementError
	switch statusCode {
	case http.StatusBadRequest:
		managementErr = types.NewManagementError(types.ErrCodeInvalidParameter, errorResp.Msg)
	case http.StatusUnauthorized:
		managementErr = types.NewManagementError(types.ErrCodeUnauthorized, errorResp.Msg)
	case http.StatusForbidden:
		managementErr = types.NewManagementError(types.ErrCodePermissionDenied, errorResp.Msg)
	case http.StatusNotFound:
		managementErr = types.NewManagementError(types.ErrCodeContentNotFound, errorResp.Msg)
	case http.StatusConflict:
		managementErr = types.NewManagementError(types.ErrCodeBusinessLogic, errorResp.Msg)
	default:
		managementErr = types.NewManagementError(types.ErrCodeServiceError, errorResp.Msg)
	}

	if errorResp.Msg != "" {
		managementErr.Details = errorResp.Msg
	}

	return managementErr
}

// SetTimeout 设置超时时间
func (c *BaseClient) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
	c.httpClient.Timeout = timeout
}

// SetRetryConfig 设置重试配置
func (c *BaseClient) SetRetryConfig(retryCount int, retryDelay time.Duration) {
	c.retryCount = retryCount
	c.retryDelay = retryDelay
}

// GetBaseURL 获取基础URL
func (c *BaseClient) GetBaseURL() string {
	return c.baseURL
}

// SetBaseURL 设置基础URL
func (c *BaseClient) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

// AddDefaultHeaders 添加默认请求头
func (c *BaseClient) AddDefaultHeaders(req *http.Request) {
	req.Header.Set("User-Agent", "content-management-service/1.0")
	req.Header.Set("Accept", "application/json")
}

// HealthCheck 健康检查
func (c *BaseClient) HealthCheck() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/health", nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("health check request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed: HTTP %d", resp.StatusCode)
	}

	return nil
}

// ClientStats 客户端统计
type ClientStats struct {
	BaseURL         string        `json:"base_url"`
	Timeout         time.Duration `json:"timeout"`
	RetryCount      int           `json:"retry_count"`
	RetryDelay      time.Duration `json:"retry_delay"`
	LastHealthCheck *time.Time    `json:"last_health_check,omitempty"`
	IsHealthy       bool          `json:"is_healthy"`
}

// GetStats 获取客户端统计信息
func (c *BaseClient) GetStats() *ClientStats {
	stats := &ClientStats{
		BaseURL:    c.baseURL,
		Timeout:    c.timeout,
		RetryCount: c.retryCount,
		RetryDelay: c.retryDelay,
	}

	// 执行健康检查
	if err := c.HealthCheck(); err == nil {
		stats.IsHealthy = true
		now := time.Now()
		stats.LastHealthCheck = &now
	} else {
		stats.IsHealthy = false
	}

	return stats
}
