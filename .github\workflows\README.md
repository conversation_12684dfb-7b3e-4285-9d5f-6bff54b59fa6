# GitHub Workflows 说明

## 目录结构

```
.github/workflows/
├── README.md                           # 本文件，工作流说明
├── templates/                          # 工作流模板
│   └── service-ci-template.yml         # 服务CI/CD模板
├── docker-build.yml                    # 多服务Docker构建工作流
├── content-management-service.yml      # 内容管理服务专用工作流
└── [future-service].yml               # 其他服务的工作流
```

## 工作流说明

### 1. docker-build.yml - 多服务Docker构建
**触发条件：**
- 推送到 main/develop 分支
- 创建 Pull Request 到 main 分支
- 推送标签

**功能：**
- 自动检测变更的服务
- 并行构建多个服务的Docker镜像
- 多架构支持 (linux/amd64, linux/arm64)
- 安全扫描 (Trivy)
- 镜像清理
- Slack通知

**服务配置：**
在 `detect-changes` job 中的 `services` 数组中添加新服务：
```json
{
  "name": "your-service-name",
  "dockerfile": "docker/services/your-service/Dockerfile",
  "context": ".",
  "path": "internal/your-cluster/your-service",
  "cluster": "your-cluster"
}
```

### 2. content-management-service.yml - 内容管理服务专用工作流
**触发条件：**
- 推送到 main/develop 分支（仅当内容管理服务相关文件变更时）
- 创建 Pull Request（仅当内容管理服务相关文件变更时）

**功能：**
- 代码质量检查 (golangci-lint, go vet)
- 单元测试和集成测试
- 测试覆盖率报告
- 安全扫描 (Gosec, Trivy)
- Docker镜像构建
- 环境部署 (开发/生产)
- 性能测试

## 添加新服务工作流

### 方法1：使用模板创建
1. 复制模板文件：
```bash
cp .github/workflows/templates/service-ci-template.yml .github/workflows/your-service.yml
```

2. 替换模板中的占位符：
- `{{SERVICE_NAME}}` → 你的服务名
- `{{CLUSTER_NAME}}` → 你的集群名
- `{{SERVICE_DESCRIPTION}}` → 服务描述

### 方法2：添加到多服务构建
1. 在 `docker-build.yml` 的 `detect-changes` job 中添加服务配置
2. 服务将自动包含在多服务构建流程中

## 环境配置

### 必需的 Secrets
- `GITHUB_TOKEN` - 自动提供，用于推送镜像
- `SLACK_WEBHOOK` - Slack通知（可选）
- `SNYK_TOKEN` - Snyk安全扫描（可选）

### 环境变量
- `GO_VERSION` - Go版本 (默认: 1.21)
- `REGISTRY` - 容器镜像仓库 (默认: ghcr.io)
- `BASE_IMAGE_NAME` - 基础镜像名 (默认: pxpat-backend)

## 工作流最佳实践

### 1. 路径过滤
- 使用 `paths` 过滤器确保只在相关文件变更时触发
- 包含服务代码、Docker文件和工作流文件本身

### 2. 并行执行
- 使用 matrix 策略并行构建多个服务
- 合理设置 job 依赖关系

### 3. 缓存策略
- Go模块缓存：加速依赖下载
- Docker层缓存：加速镜像构建
- 按服务分别缓存，避免冲突

### 4. 安全扫描
- 代码安全扫描 (Gosec)
- 镜像漏洞扫描 (Trivy)
- 依赖安全检查 (Snyk, 可选)

### 5. 测试策略
- 单元测试：快速反馈
- 集成测试：完整功能验证
- 性能测试：PR时执行
- 覆盖率报告：代码质量监控

## 部署策略

### 开发环境
- develop 分支自动部署
- 快速反馈，允许实验性功能

### 生产环境
- main 分支自动部署
- 需要通过所有测试和安全检查
- 支持回滚机制

## 监控和通知

### Slack集成
- 构建状态通知
- 部署结果通知
- 安全扫描警告

### 指标收集
- 构建时间
- 测试覆盖率
- 部署频率
- 失败率统计

## 故障排查

### 常见问题
1. **构建失败**
   - 检查 Go 版本兼容性
   - 验证依赖是否正确
   - 查看详细日志

2. **Docker构建失败**
   - 检查 Dockerfile 路径
   - 验证构建上下文
   - 检查多架构支持

3. **测试失败**
   - 检查测试数据库连接
   - 验证环境变量设置
   - 查看测试日志

### 调试技巧
- 使用 `act` 本地运行工作流
- 启用调试日志：设置 `ACTIONS_STEP_DEBUG=true`
- 使用 tmate 进行远程调试

## 性能优化

### 构建优化
- 使用多阶段构建
- 优化 Docker 层缓存
- 并行执行独立任务

### 测试优化
- 使用测试缓存
- 并行运行测试
- 智能测试选择

### 资源管理
- 合理设置超时时间
- 控制并发任务数量
- 及时清理临时资源
