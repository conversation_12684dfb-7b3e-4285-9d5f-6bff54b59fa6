{"datacenter": "dc1", "data_dir": "/consul/data", "log_level": "INFO", "node_name": "consul-server", "server": true, "bootstrap_expect": 1, "ui_config": {"enabled": true}, "connect": {"enabled": true}, "client_addr": "0.0.0.0", "bind_addr": "0.0.0.0", "retry_join": [], "ports": {"grpc": 8502}, "acl": {"enabled": false, "default_policy": "allow", "enable_token_persistence": true}, "performance": {"raft_multiplier": 1}, "limits": {"http_max_conns_per_client": 200}, "telemetry": {"prometheus_retention_time": "30s", "disable_hostname": false}, "services": [{"name": "consul", "tags": ["consul", "server"], "port": 8500, "check": {"http": "http://localhost:8500/v1/status/leader", "interval": "10s"}}]}