package client

import (
	"fmt"
	"time"

	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// VideoServiceClient 视频服务客户端接口
type VideoServiceClient interface {
	// 内容查询
	GetContentByKSUID(contentKSUID string) (*VideoContent, error)
	GetUserContents(userKSUID string, filters *types.ContentFilters) (*VideoContentList, error)
	GetContentsByStatus(status string, filters *types.ContentFilters) (*VideoContentList, error)
	BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]*VideoContent, error)
	GetContentsByFilters(filters *types.ContentFilters) (*VideoContentList, error)

	// 内容操作
	UpdateContentStatus(contentKSUID string, status string) error
	DeleteContent(contentKSUID string) error
	BatchUpdateStatus(contentKSUIDs []string, status string) error
	BatchDeleteContents(contentKSUIDs []string) error

	// 统计查询（不包含点赞和收藏，这些在interaction-service中）
	GetContentStats(contentKSUID string) (*VideoContentStats, error)
	GetUserStats(userKSUID string) (*VideoUserStats, error)
	GetOverallStats() (*VideoOverallStats, error)

	// 健康检查
	HealthCheck() error
}

// videoServiceClient 视频服务客户端实现
type videoServiceClient struct {
	baseClient *BaseClient
}

// NewVideoServiceClient 创建视频服务客户端
func NewVideoServiceClient(baseURL string, timeout time.Duration) VideoServiceClient {
	config := ClientConfig{
		BaseURL:    baseURL,
		Timeout:    timeout,
		RetryCount: 3,
		RetryDelay: 1 * time.Second,
	}

	return &videoServiceClient{
		baseClient: NewBaseClient(config),
	}
}

// VideoContent 视频内容结构（从video-service获取的数据结构）
type VideoContent struct {
	ContentKSUID string     `json:"content_ksuid"`
	Title        string     `json:"title"`
	Description  string     `json:"description"`
	UserKSUID    string     `json:"user_ksuid"`
	Status       string     `json:"status"`
	ViewCount    int64      `json:"view_count"`
	CommentCount int64      `json:"comment_count"`
	CategoryID   uint       `json:"category_id"`
	Category     Category   `json:"category"`
	Tags         []Tag      `json:"tags"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
	PublishedAt  *time.Time `json:"published_at,omitempty"`

	// 视频特有字段
	Duration     float64  `json:"duration"`
	Resolution   string   `json:"resolution"`
	FileSize     int64    `json:"file_size"`
	Format       string   `json:"format"`
	Orientation  string   `json:"orientation"`
	Language     string   `json:"language"`
	PlayURL      string   `json:"play_url"`
	CoverURL     string   `json:"cover_url"`
	PreviewURL   string   `json:"preview_url"`
	KeyFramesURL string   `json:"key_frames_url"`
	VideoID      string   `json:"video_id"`
	Director     string   `json:"director"`
	Actors       []string `json:"actors"`
	AuditTaskID  uint64   `json:"audit_task_id"`
	Level        string   `json:"level"`
}

// Category 分类结构
type Category struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

// Tag 标签结构
type Tag struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

// VideoContentList 视频内容列表
type VideoContentList struct {
	Contents   []*VideoContent `json:"contents"`
	Total      int             `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

// VideoContentStats 视频内容统计
type VideoContentStats struct {
	ContentKSUID string  `json:"content_ksuid"`
	ViewCount    int64   `json:"view_count"`
	CommentCount int64   `json:"comment_count"`
	ShareCount   int64   `json:"share_count"`
	Duration     float64 `json:"duration"`
	FileSize     int64   `json:"file_size"`
}

// VideoUserStats 视频用户统计
type VideoUserStats struct {
	UserKSUID       string  `json:"user_ksuid"`
	TotalVideos     int64   `json:"total_videos"`
	PublishedVideos int64   `json:"published_videos"`
	TotalViews      int64   `json:"total_views"`
	TotalComments   int64   `json:"total_comments"`
	TotalDuration   float64 `json:"total_duration"`
	TotalFileSize   int64   `json:"total_file_size"`
}

// VideoOverallStats 视频总体统计
type VideoOverallStats struct {
	TotalVideos     int64   `json:"total_videos"`
	PublishedVideos int64   `json:"published_videos"`
	TotalViews      int64   `json:"total_views"`
	TotalComments   int64   `json:"total_comments"`
	TotalDuration   float64 `json:"total_duration"`
	TotalFileSize   int64   `json:"total_file_size"`
	AvgDuration     float64 `json:"avg_duration"`
	AvgFileSize     float64 `json:"avg_file_size"`
}

// GetContentByKSUID 根据KSUID获取视频内容
func (c *videoServiceClient) GetContentByKSUID(contentKSUID string) (*VideoContent, error) {
	endpoint := fmt.Sprintf("/api/v1/intra/content/%s", contentKSUID)

	var response struct {
		Code int           `json:"code"`
		Data *VideoContent `json:"data"`
		Msg  string        `json:"msg"`
	}

	err := c.baseClient.Get(endpoint, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取视频内容失败")
		return nil, fmt.Errorf("failed to get video content: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	if response.Data == nil {
		return nil, types.ErrContentNotFound
	}

	return response.Data, nil
}

// GetUserContents 获取用户的视频内容
func (c *videoServiceClient) GetUserContents(userKSUID string, filters *types.ContentFilters) (*VideoContentList, error) {
	endpoint := fmt.Sprintf("/api/v1/intra/content/user/%s", userKSUID)

	// 构建查询参数
	params := make(map[string]interface{})
	if filters != nil {
		if filters.Status != "" {
			params["status"] = filters.Status
		}
		if filters.CategoryID != 0 {
			params["category_id"] = filters.CategoryID
		}
		if len(filters.Tags) > 0 {
			params["tags"] = filters.Tags
		}
		if filters.Page > 0 {
			params["page"] = filters.Page
		}
		if filters.Limit > 0 {
			params["limit"] = filters.Limit
		}
		if filters.SortBy != "" {
			params["sort_by"] = filters.SortBy
		}
		if filters.SortOrder != "" {
			params["sort_order"] = filters.SortOrder
		}
	}

	var response struct {
		Code int               `json:"code"`
		Data *VideoContentList `json:"data"`
		Msg  string            `json:"msg"`
	}

	err := c.baseClient.Post(endpoint, params, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取用户视频内容失败")
		return nil, fmt.Errorf("failed to get user video contents: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	if response.Data == nil {
		return &VideoContentList{Contents: []*VideoContent{}}, nil
	}

	return response.Data, nil
}

// GetContentsByStatus 根据状态获取视频内容
func (c *videoServiceClient) GetContentsByStatus(status string, filters *types.ContentFilters) (*VideoContentList, error) {
	endpoint := "/api/v1/intra/content/status"

	params := map[string]interface{}{
		"status": status,
	}

	if filters != nil {
		if filters.CategoryID != 0 {
			params["category_id"] = filters.CategoryID
		}
		if len(filters.Tags) > 0 {
			params["tags"] = filters.Tags
		}
		if filters.Page > 0 {
			params["page"] = filters.Page
		}
		if filters.Limit > 0 {
			params["limit"] = filters.Limit
		}
		if filters.SortBy != "" {
			params["sort_by"] = filters.SortBy
		}
		if filters.SortOrder != "" {
			params["sort_order"] = filters.SortOrder
		}
	}

	var response struct {
		Code int               `json:"code"`
		Data *VideoContentList `json:"data"`
		Msg  string            `json:"msg"`
	}

	err := c.baseClient.Post(endpoint, params, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("status", status).
			Msg("根据状态获取视频内容失败")
		return nil, fmt.Errorf("failed to get video contents by status: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	if response.Data == nil {
		return &VideoContentList{Contents: []*VideoContent{}}, nil
	}

	return response.Data, nil
}

// BatchGetContentsByKSUIDs 批量获取视频内容
func (c *videoServiceClient) BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]*VideoContent, error) {
	endpoint := "/api/v1/intra/content/batch"

	requestBody := map[string]interface{}{
		"content_ksuids": contentKSUIDs,
	}

	var response struct {
		Code int                      `json:"code"`
		Data map[string]*VideoContent `json:"data"`
		Msg  string                   `json:"msg"`
	}

	err := c.baseClient.Post(endpoint, requestBody, &response)
	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(contentKSUIDs)).
			Msg("批量获取视频内容失败")
		return nil, fmt.Errorf("failed to batch get video contents: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	if response.Data == nil {
		return make(map[string]*VideoContent), nil
	}

	return response.Data, nil
}

// GetContentsByFilters 根据过滤条件获取视频内容
func (c *videoServiceClient) GetContentsByFilters(filters *types.ContentFilters) (*VideoContentList, error) {
	endpoint := "/api/v1/intra/content/filter"

	params := make(map[string]interface{})
	if filters != nil {
		if filters.Status != "" {
			params["status"] = filters.Status
		}
		if filters.UserKSUID != "" {
			params["user_ksuid"] = filters.UserKSUID
		}
		if filters.CategoryID != 0 {
			params["category_id"] = filters.CategoryID
		}
		if len(filters.Tags) > 0 {
			params["tags"] = filters.Tags
		}
		if filters.CreatedAfter != nil {
			params["created_after"] = filters.CreatedAfter.Format(time.RFC3339)
		}
		if filters.CreatedBefore != nil {
			params["created_before"] = filters.CreatedBefore.Format(time.RFC3339)
		}
		if filters.Page > 0 {
			params["page"] = filters.Page
		}
		if filters.Limit > 0 {
			params["limit"] = filters.Limit
		}
		if filters.SortBy != "" {
			params["sort_by"] = filters.SortBy
		}
		if filters.SortOrder != "" {
			params["sort_order"] = filters.SortOrder
		}
	}

	var response struct {
		Code int               `json:"code"`
		Data *VideoContentList `json:"data"`
		Msg  string            `json:"msg"`
	}

	err := c.baseClient.Post(endpoint, params, &response)
	if err != nil {
		log.Error().
			Err(err).
			Msg("根据过滤条件获取视频内容失败")
		return nil, fmt.Errorf("failed to get video contents by filters: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	if response.Data == nil {
		return &VideoContentList{Contents: []*VideoContent{}}, nil
	}

	return response.Data, nil
}

// UpdateContentStatus 更新视频内容状态
func (c *videoServiceClient) UpdateContentStatus(contentKSUID string, status string) error {
	endpoint := "/api/v1/intra/content/updateStatus"

	requestBody := map[string]interface{}{
		"content_ksuid": contentKSUID,
		"status":        status,
	}

	var response struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	err := c.baseClient.Post(endpoint, requestBody, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Str("status", status).
			Msg("更新视频内容状态失败")
		return fmt.Errorf("failed to update video content status: %w", err)
	}

	if response.Code < 0 {
		return types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	return nil
}

// DeleteContent 删除视频内容
func (c *videoServiceClient) DeleteContent(contentKSUID string) error {
	endpoint := fmt.Sprintf("/api/v1/intra/content/%s", contentKSUID)

	err := c.baseClient.Delete(endpoint)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("删除视频内容失败")
		return fmt.Errorf("failed to delete video content: %w", err)
	}

	return nil
}

// BatchUpdateStatus 批量更新视频内容状态
func (c *videoServiceClient) BatchUpdateStatus(contentKSUIDs []string, status string) error {
	endpoint := "/api/v1/intra/content/batch/updateStatus"

	requestBody := map[string]interface{}{
		"content_ksuids": contentKSUIDs,
		"status":         status,
	}

	var response struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	err := c.baseClient.Post(endpoint, requestBody, &response)
	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(contentKSUIDs)).
			Str("status", status).
			Msg("批量更新视频内容状态失败")
		return fmt.Errorf("failed to batch update video content status: %w", err)
	}

	if response.Code < 0 {
		return types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	return nil
}

// BatchDeleteContents 批量删除视频内容
func (c *videoServiceClient) BatchDeleteContents(contentKSUIDs []string) error {
	endpoint := "/api/v1/intra/content/batch/delete"

	requestBody := map[string]interface{}{
		"content_ksuids": contentKSUIDs,
	}

	var response struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	err := c.baseClient.Post(endpoint, requestBody, &response)
	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(contentKSUIDs)).
			Msg("批量删除视频内容失败")
		return fmt.Errorf("failed to batch delete video contents: %w", err)
	}

	if response.Code < 0 {
		return types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	return nil
}

// GetContentStats 获取视频内容统计
func (c *videoServiceClient) GetContentStats(contentKSUID string) (*VideoContentStats, error) {
	endpoint := fmt.Sprintf("/api/v1/intra/content/%s/stats", contentKSUID)

	var response struct {
		Code int                `json:"code"`
		Data *VideoContentStats `json:"data"`
		Msg  string             `json:"msg"`
	}

	err := c.baseClient.Get(endpoint, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取视频内容统计失败")
		return nil, fmt.Errorf("failed to get video content stats: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	return response.Data, nil
}

// GetUserStats 获取用户视频统计
func (c *videoServiceClient) GetUserStats(userKSUID string) (*VideoUserStats, error) {
	endpoint := fmt.Sprintf("/api/v1/intra/user/%s/stats", userKSUID)

	var response struct {
		Code int             `json:"code"`
		Data *VideoUserStats `json:"data"`
		Msg  string          `json:"msg"`
	}

	err := c.baseClient.Get(endpoint, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取用户视频统计失败")
		return nil, fmt.Errorf("failed to get user video stats: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	return response.Data, nil
}

// GetOverallStats 获取视频总体统计
func (c *videoServiceClient) GetOverallStats() (*VideoOverallStats, error) {
	endpoint := "/api/v1/intra/stats/overall"

	var response struct {
		Code int                `json:"code"`
		Data *VideoOverallStats `json:"data"`
		Msg  string             `json:"msg"`
	}

	err := c.baseClient.Get(endpoint, &response)
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取视频总体统计失败")
		return nil, fmt.Errorf("failed to get video overall stats: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	return response.Data, nil
}

// HealthCheck 健康检查
func (c *videoServiceClient) HealthCheck() error {
	return c.baseClient.HealthCheck()
}
