package handler

import (
	serviceService "pxpat-backend/internal/content-cluster/audit-service/intra/service"

	"github.com/rs/zerolog/log"
)

// ProvideServiceAuditHandler 提供内部审核处理器
func ProvideServiceAuditHandler(
	serviceAuditService *serviceService.InternalAuditTasksService,
) *ServiceAuditHandler {
	serviceAuditHandler := NewServiceAuditHandler(serviceAuditService)
	log.Info().Msg("Internal audit handler initialized successfully")
	return serviceAuditHandler
}
