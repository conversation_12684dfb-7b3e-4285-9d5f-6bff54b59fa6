# 批量删除收藏项API文档

## 概述

批量删除收藏项API允许用户一次性删除指定收藏夹中的多个收藏项，提供高效的批量操作功能。

## API接口

### 批量删除收藏项

**接口地址**: `DELETE /api/v1/favorites/items/batch`

**请求方法**: DELETE

**需要认证**: 是

**请求参数**:

```json
{
  "favorite_folder_id": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "favorite_item_ids": [
    "01ARZ3NDEKTSV4RRFFQ69G5FAV",
    "01ARZ3NDEKTSV4RRFFQ69G5FAW",
    "01ARZ3NDEKTSV4RRFFQ69G5FAX"
  ]
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| favorite_folder_id | string | 是 | 收藏夹ID |
| favorite_item_ids | []string | 是 | 收藏项ID数组，最多100个 |

**参数验证**:
- `favorite_folder_id`: 必须是有效的收藏夹ID，且属于当前用户
- `favorite_item_ids`: 数组长度必须在1-100之间，每个ID必须是有效的收藏项ID

## 响应示例

### 成功响应

```json
{
  "code": 20001,
  "message": "success",
  "data": {
    "success": true,
    "total_requested": 3,
    "success_count": 2,
    "skipped_count": 1
  }
}
```

### 错误响应

```json
{
  "code": 40000,
  "message": "参数错误"
}
```

**响应字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 操作是否成功 |
| total_requested | int | 请求删除的总数 |
| success_count | int | 成功删除的数量 |
| skipped_count | int | 跳过的数量（不存在或不属于指定收藏夹） |

## 业务逻辑

### 验证逻辑
1. **收藏夹验证**: 验证收藏夹是否存在且属于当前用户
2. **收藏项验证**: 验证每个收藏项是否存在且属于指定收藏夹
3. **权限验证**: 确保用户只能删除自己的收藏项

### 删除逻辑
1. **批量删除**: 使用批量删除操作提高性能
2. **智能跳过**: 自动跳过不存在或不属于指定收藏夹的收藏项
3. **数量更新**: 自动更新收藏夹的收藏项数量
4. **统计维护**: 如果用户完全取消收藏某个内容，自动减少收藏统计

### 错误处理
- **部分成功**: 即使部分收藏项删除失败，成功的删除操作仍会执行
- **容错性**: 统计更新失败不影响删除操作的成功
- **详细日志**: 记录每个操作的详细信息便于问题排查

## 性能特性

### 批量操作优化
- **单次数据库操作**: 使用批量删除减少数据库访问次数
- **事务保证**: 确保数据一致性
- **分表支持**: 自动处理用户分表逻辑

### 限制说明
- **数量限制**: 单次最多删除100个收藏项
- **权限限制**: 只能删除属于自己且在指定收藏夹中的收藏项
- **收藏夹限制**: 必须指定有效的收藏夹ID

## 使用示例

### 示例1：删除收藏夹中的部分收藏项
```bash
curl -X DELETE "https://api.example.com/api/v1/favorites/items/batch" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "favorite_folder_id": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
    "favorite_item_ids": [
      "01ARZ3NDEKTSV4RRFFQ69G5FAW",
      "01ARZ3NDEKTSV4RRFFQ69G5FAX"
    ]
  }'
```

### 示例2：删除单个收藏项
```bash
curl -X DELETE "https://api.example.com/api/v1/favorites/items/batch" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "favorite_folder_id": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
    "favorite_item_ids": ["01ARZ3NDEKTSV4RRFFQ69G5FAW"]
  }'
```

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40000 | 参数错误 | 检查请求参数格式和必填字段 |
| 40001 | 收藏夹不存在 | 确认收藏夹ID是否正确 |
| 40003 | 无权限访问 | 确认收藏夹属于当前用户 |
| 50000 | 系统错误 | 联系技术支持 |

## 注意事项

### 1. 数据一致性
- 删除收藏项后会自动更新收藏夹的收藏项数量
- 如果用户完全取消收藏某个内容，会自动减少该内容的收藏统计

### 2. 性能考虑
- 建议单次删除不超过50个收藏项以获得最佳性能
- 大量删除操作建议分批进行

### 3. 错误处理
- API采用"部分成功"模式，部分失败不影响成功的操作
- 详细的响应信息帮助客户端了解具体的操作结果

### 4. 日志记录
- 所有删除操作都会记录详细日志
- 包括用户ID、收藏夹ID、收藏项ID等关键信息

## 与其他API的关系

### 相关API
- `GET /api/v1/favorites/items`: 获取收藏项列表（用于获取要删除的收藏项ID）
- `PUT /api/v1/favorites/items/manage`: 管理收藏（用于单个内容的收藏管理）
- `GET /api/v1/favorites/folders`: 获取收藏夹列表（用于获取收藏夹ID）

### 使用流程
1. 调用获取收藏项列表API获取收藏项ID
2. 选择要删除的收藏项
3. 调用批量删除API执行删除操作
4. 根据响应结果更新前端界面

## 最佳实践

### 1. 前端实现建议
- 提供批量选择功能让用户选择要删除的收藏项
- 在删除前显示确认对话框
- 根据响应结果显示操作结果

### 2. 错误处理建议
- 对于跳过的收藏项，可以向用户说明原因
- 对于失败的操作，提供重试机制
- 记录客户端日志便于问题排查

### 3. 性能优化建议
- 避免频繁的小批量删除操作
- 合理使用分页和批量操作
- 考虑用户体验，提供操作进度提示

## 安全考虑

### 1. 权限控制
- 严格验证收藏夹和收藏项的所有权
- 防止用户删除他人的收藏项
- 记录所有删除操作的审计日志

### 2. 参数验证
- 验证收藏夹ID和收藏项ID的格式
- 限制批量操作的数量防止滥用
- 防止SQL注入等安全问题

### 3. 频率限制
- 建议实施适当的频率限制
- 防止恶意的大量删除操作
- 监控异常的删除行为
