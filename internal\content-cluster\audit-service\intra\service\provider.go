package service

import (
	"pxpat-backend/internal/content-cluster/audit-service/client"
	publisher "pxpat-backend/internal/content-cluster/audit-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/audit-service/repository"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideInternalAuditTasksService 提供内部审核任务服务
func ProvideInternalAuditTasksService(
	auditTasksRepository *repository.AuditTasksRepository,
	contentServiceClient client.ContentServiceClient,
	mqPublisher *publisher.Publisher,
	db *gorm.DB,
) *InternalAuditTasksService {
	serviceAuditService := NewInternalAuditTasksService(
		auditTasksRepository,
		contentServiceClient,
		mqPublisher,
		db,
	)
	log.Info().Msg("Internal audit tasks service initialized successfully")
	return serviceAuditService
}
