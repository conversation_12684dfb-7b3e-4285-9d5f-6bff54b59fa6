package handler

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
	"pxpat-backend/pkg/response"
)

// RightsHandler 权益认证处理器
type RightsHandler struct {
	rightsService *service.RightsService
}

// NewRightsHandler 创建权益认证处理器实例
func NewRightsHandler(rightsService *service.RightsService) *RightsHandler {
	return &RightsHandler{
		rightsService: rightsService,
	}
}

// CreateRightsVerification 创建权益认证
// @Summary 创建权益认证
// @Description 用户提交权益认证申请
// @Tags 权益认证
// @Accept json
// @Produce json
// @Param request body dto.CreateRightsVerificationRequest true "创建权益认证请求"
// @Success 200 {object} response.Response{data=dto.RightsVerificationResponse} "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/rights/verification [post]
func (h *RightsHandler) CreateRightsVerification(c *gin.Context) {
	var req dto.CreateRightsVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("绑定创建权益认证请求参数失败")
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	result, gErr := h.rightsService.CreateRightsVerification(c.Request.Context(), userKSUID.(string), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Msg("创建权益认证失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// GetRightsVerification 获取权益认证详情
// @Summary 获取权益认证详情
// @Description 获取指定权益认证的详细信息
// @Tags 权益认证
// @Accept json
// @Produce json
// @Param rights_ksuid path string true "权益认证KSUID"
// @Success 200 {object} response.Response{data=dto.RightsVerificationResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "权益认证不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/rights/verification/{rights_ksuid} [get]
func (h *RightsHandler) GetRightsVerification(c *gin.Context) {
	rightsKSUID := c.Param("rights_ksuid")
	if rightsKSUID == "" {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	result, gErr := h.rightsService.GetRightsVerification(c.Request.Context(), userKSUID.(string), rightsKSUID)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Str("rights_ksuid", rightsKSUID).
			Msg("获取权益认证详情失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// GetMyRightsVerifications 获取我的权益认证列表
// @Summary 获取我的权益认证列表
// @Description 获取当前用户的权益认证列表
// @Tags 权益认证
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param type query string false "权益类型"
// @Param status query string false "认证状态"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} response.Response{data=dto.RightsVerificationListResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/rights/verification/my [get]
func (h *RightsHandler) GetMyRightsVerifications(c *gin.Context) {
	var req dto.GetRightsVerificationsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().Err(err).Msg("绑定获取我的权益认证列表请求参数失败")
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	result, gErr := h.rightsService.GetMyRightsVerifications(c.Request.Context(), userKSUID.(string), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Msg("获取我的权益认证列表失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}
