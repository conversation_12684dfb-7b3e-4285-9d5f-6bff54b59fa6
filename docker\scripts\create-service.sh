#!/bin/bash

# 新服务创建脚本
# 用于快速创建新服务的Docker配置和CI/CD工作流

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "新服务创建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -n, --name SERVICE_NAME     服务名称 (必需)"
    echo "  -c, --cluster CLUSTER_NAME  集群名称 (必需)"
    echo "  -p, --port PORT             服务端口 (必需)"
    echo "  -d, --description DESC      服务描述"
    echo "  -h, --help                  显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -n video-service -c video-cluster -p 12001 -d '视频处理服务'"
    echo ""
}

# 验证参数
validate_params() {
    if [ -z "$SERVICE_NAME" ]; then
        print_error "服务名称不能为空"
        show_help
        exit 1
    fi
    
    if [ -z "$CLUSTER_NAME" ]; then
        print_error "集群名称不能为空"
        show_help
        exit 1
    fi
    
    if [ -z "$SERVICE_PORT" ]; then
        print_error "服务端口不能为空"
        show_help
        exit 1
    fi
    
    # 验证端口号
    if ! [[ "$SERVICE_PORT" =~ ^[0-9]+$ ]] || [ "$SERVICE_PORT" -lt 1 ] || [ "$SERVICE_PORT" -gt 65535 ]; then
        print_error "无效的端口号: $SERVICE_PORT"
        exit 1
    fi
    
    # 验证服务名称格式
    if ! [[ "$SERVICE_NAME" =~ ^[a-z][a-z0-9-]*[a-z0-9]$ ]]; then
        print_error "服务名称格式无效。应该使用小写字母、数字和连字符，以字母开头和结尾"
        exit 1
    fi
    
    # 验证集群名称格式
    if ! [[ "$CLUSTER_NAME" =~ ^[a-z][a-z0-9-]*[a-z0-9]$ ]]; then
        print_error "集群名称格式无效。应该使用小写字母、数字和连字符，以字母开头和结尾"
        exit 1
    fi
}

# 检查服务是否已存在
check_service_exists() {
    local service_dir="$SCRIPT_DIR/../services/$SERVICE_NAME"
    if [ -d "$service_dir" ]; then
        print_error "服务 '$SERVICE_NAME' 已存在于 $service_dir"
        exit 1
    fi
    
    local workflow_file="$PROJECT_ROOT/.github/workflows/$SERVICE_NAME.yml"
    if [ -f "$workflow_file" ]; then
        print_error "工作流文件 '$SERVICE_NAME.yml' 已存在"
        exit 1
    fi
}

# 创建服务Docker配置
create_docker_config() {
    local service_dir="$SCRIPT_DIR/../services/$SERVICE_NAME"
    
    print_info "创建服务Docker配置..."
    
    # 创建服务目录
    mkdir -p "$service_dir"
    
    # 创建Dockerfile
    sed "s/{{SERVICE_NAME}}/$SERVICE_NAME/g; s/{{CLUSTER_NAME}}/$CLUSTER_NAME/g; s/{{SERVICE_PORT}}/$SERVICE_PORT/g; s/{{SERVICE_BINARY_NAME}}/$SERVICE_BINARY_NAME/g; s/{{SERVICE_DESCRIPTION}}/$SERVICE_DESCRIPTION/g" \
        "$SCRIPT_DIR/../services/templates/Dockerfile.template" > "$service_dir/Dockerfile"
    
    # 创建docker-compose.yml
    local service_name_upper=$(echo "$SERVICE_NAME" | tr '[:lower:]' '[:upper:]' | tr '-' '_')
    local network_name="${CLUSTER_NAME}-network"
    
    sed "s/{{SERVICE_NAME}}/$SERVICE_NAME/g; s/{{SERVICE_NAME_UPPER}}/$service_name_upper/g; s/{{SERVICE_PORT}}/$SERVICE_PORT/g; s/{{SERVICE_DESCRIPTION}}/$SERVICE_DESCRIPTION/g; s/{{NETWORK_NAME}}/$network_name/g" \
        "$SCRIPT_DIR/../services/templates/docker-compose.template.yml" > "$service_dir/docker-compose.yml"
    
    print_success "Docker配置创建完成: $service_dir"
}

# 创建CI/CD工作流
create_workflow() {
    local workflow_file="$PROJECT_ROOT/.github/workflows/$SERVICE_NAME.yml"
    
    print_info "创建CI/CD工作流..."
    
    sed "s/{{SERVICE_NAME}}/$SERVICE_NAME/g; s/{{CLUSTER_NAME}}/$CLUSTER_NAME/g; s/{{SERVICE_DESCRIPTION}}/$SERVICE_DESCRIPTION/g" \
        "$PROJECT_ROOT/.github/workflows/templates/service-ci-template.yml" > "$workflow_file"
    
    print_success "CI/CD工作流创建完成: $workflow_file"
}

# 更新多服务构建配置
update_multi_service_build() {
    local docker_build_file="$PROJECT_ROOT/.github/workflows/docker-build.yml"
    
    print_info "更新多服务构建配置..."
    
    # 创建新服务配置
    local new_service_config="          {
            \"name\": \"$SERVICE_NAME\",
            \"dockerfile\": \"docker/services/$SERVICE_NAME/Dockerfile\",
            \"context\": \".\",
            \"path\": \"internal/$CLUSTER_NAME/$SERVICE_NAME\",
            \"cluster\": \"$CLUSTER_NAME\"
          }"
    
    # 在services数组中添加新服务（在content-management-service之后）
    if grep -q "content-management-service" "$docker_build_file"; then
        # 使用sed在content-management-service配置后添加新服务
        sed -i "/\"cluster\": \"content-cluster\"/a\\
          },$new_service_config" "$docker_build_file"
        
        print_success "已将 $SERVICE_NAME 添加到多服务构建配置"
    else
        print_warning "未找到content-management-service配置，请手动添加服务到 $docker_build_file"
    fi
}

# 创建环境配置示例
create_env_examples() {
    local dev_env="$SCRIPT_DIR/../environments/development/.env.example"
    local prod_env="$SCRIPT_DIR/../environments/production/.env.example"
    
    print_info "更新环境配置示例..."
    
    # 添加到开发环境配置
    if [ -f "$dev_env" ]; then
        echo "" >> "$dev_env"
        echo "# $SERVICE_DESCRIPTION 配置" >> "$dev_env"
        echo "${SERVICE_NAME^^}_PORT=$SERVICE_PORT" >> "$dev_env"
        echo "${SERVICE_NAME^^}_URL=http://$SERVICE_NAME:$SERVICE_PORT" >> "$dev_env"
    fi
    
    # 添加到生产环境配置
    if [ -f "$prod_env" ]; then
        echo "" >> "$prod_env"
        echo "# $SERVICE_DESCRIPTION 配置" >> "$prod_env"
        echo "${SERVICE_NAME^^}_PORT=$SERVICE_PORT" >> "$prod_env"
        echo "${SERVICE_NAME^^}_URL=http://$SERVICE_NAME:$SERVICE_PORT" >> "$prod_env"
    fi
    
    print_success "环境配置示例已更新"
}

# 显示后续步骤
show_next_steps() {
    echo ""
    print_success "🎉 服务 '$SERVICE_NAME' 创建完成！"
    echo ""
    print_info "📋 后续步骤:"
    echo "1. 创建服务代码目录:"
    echo "   mkdir -p internal/$CLUSTER_NAME/$SERVICE_NAME"
    echo "   mkdir -p cmd/$CLUSTER_NAME/$SERVICE_NAME"
    echo ""
    echo "2. 实现服务代码"
    echo ""
    echo "3. 测试Docker构建:"
    echo "   cd docker/services/$SERVICE_NAME"
    echo "   docker build -t $SERVICE_NAME:latest -f Dockerfile ../../.."
    echo ""
    echo "4. 测试服务启动:"
    echo "   docker-compose up -d"
    echo ""
    echo "5. 提交代码触发CI/CD:"
    echo "   git add ."
    echo "   git commit -m \"Add $SERVICE_NAME service configuration\""
    echo "   git push"
    echo ""
    print_info "📁 创建的文件:"
    echo "   - docker/services/$SERVICE_NAME/Dockerfile"
    echo "   - docker/services/$SERVICE_NAME/docker-compose.yml"
    echo "   - .github/workflows/$SERVICE_NAME.yml"
    echo ""
}

# 主函数
main() {
    # 设置默认值
    SERVICE_DESCRIPTION=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--name)
                SERVICE_NAME="$2"
                shift 2
                ;;
            -c|--cluster)
                CLUSTER_NAME="$2"
                shift 2
                ;;
            -p|--port)
                SERVICE_PORT="$2"
                shift 2
                ;;
            -d|--description)
                SERVICE_DESCRIPTION="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置工作目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
    
    # 设置默认描述
    if [ -z "$SERVICE_DESCRIPTION" ]; then
        SERVICE_DESCRIPTION="$SERVICE_NAME"
    fi
    
    # 设置二进制文件名
    SERVICE_BINARY_NAME="$SERVICE_NAME"
    
    # 验证参数
    validate_params
    
    # 检查服务是否已存在
    check_service_exists
    
    print_info "开始创建服务 '$SERVICE_NAME'..."
    print_info "集群: $CLUSTER_NAME"
    print_info "端口: $SERVICE_PORT"
    print_info "描述: $SERVICE_DESCRIPTION"
    echo ""
    
    # 创建配置
    create_docker_config
    create_workflow
    update_multi_service_build
    create_env_examples
    
    # 显示后续步骤
    show_next_steps
}

# 运行主函数
main "$@"
