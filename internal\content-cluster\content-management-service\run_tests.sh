#!/bin/bash

# 内容管理服务测试运行脚本
# 用于运行所有单元测试并生成覆盖率报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_ROOT="$PROJECT_ROOT"

echo -e "${BLUE}=== 内容管理服务单元测试 ===${NC}"
echo "项目路径: $SERVICE_ROOT"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: Go未安装或不在PATH中${NC}"
    exit 1
fi

echo -e "${YELLOW}Go版本:${NC}"
go version
echo ""

# 进入服务目录
cd "$SERVICE_ROOT"

# 清理之前的测试结果
echo -e "${YELLOW}清理之前的测试结果...${NC}"
rm -f coverage.out coverage.html
rm -rf test_results/
mkdir -p test_results

# 下载依赖
echo -e "${YELLOW}下载测试依赖...${NC}"
go mod tidy
go mod download

# 运行测试前的准备
echo -e "${YELLOW}准备测试环境...${NC}"

# 检查Redis连接（可选）
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo -e "${GREEN}✓ Redis连接正常${NC}"
    else
        echo -e "${YELLOW}⚠ Redis未运行，将跳过需要Redis的测试${NC}"
    fi
else
    echo -e "${YELLOW}⚠ Redis CLI未安装，将跳过需要Redis的测试${NC}"
fi

# 设置测试环境变量
export GO_ENV=test
export GIN_MODE=test

# 运行单元测试
echo -e "${YELLOW}运行单元测试...${NC}"
echo ""

# 测试工具函数
echo -e "${BLUE}测试工具函数...${NC}"
go test -v -race -coverprofile=test_results/utils_coverage.out ./utils/... || {
    echo -e "${RED}✗ 工具函数测试失败${NC}"
    exit 1
}
echo -e "${GREEN}✓ 工具函数测试通过${NC}"
echo ""

# 测试Repository层
echo -e "${BLUE}测试Repository层...${NC}"
go test -v -race -coverprofile=test_results/repository_coverage.out ./repository/... || {
    echo -e "${RED}✗ Repository层测试失败${NC}"
    exit 1
}
echo -e "${GREEN}✓ Repository层测试通过${NC}"
echo ""

# 测试Service层
echo -e "${BLUE}测试Service层...${NC}"
go test -v -race -coverprofile=test_results/service_coverage.out ./external/service/... || {
    echo -e "${RED}✗ Service层测试失败${NC}"
    exit 1
}
echo -e "${GREEN}✓ Service层测试通过${NC}"
echo ""

# 测试Handler层
echo -e "${BLUE}测试Handler层...${NC}"
go test -v -race -coverprofile=test_results/handler_coverage.out ./external/handler/... || {
    echo -e "${RED}✗ Handler层测试失败${NC}"
    exit 1
}
echo -e "${GREEN}✓ Handler层测试通过${NC}"
echo ""

# 合并覆盖率报告
echo -e "${YELLOW}合并覆盖率报告...${NC}"
echo "mode: atomic" > coverage.out

# 合并所有覆盖率文件
for coverage_file in test_results/*_coverage.out; do
    if [ -f "$coverage_file" ]; then
        tail -n +2 "$coverage_file" >> coverage.out
    fi
done

# 生成覆盖率报告
echo -e "${YELLOW}生成覆盖率报告...${NC}"
go tool cover -html=coverage.out -o coverage.html

# 计算覆盖率
COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
echo ""
echo -e "${BLUE}=== 测试结果汇总 ===${NC}"
echo -e "总体覆盖率: ${GREEN}$COVERAGE${NC}"

# 检查覆盖率是否达到目标
COVERAGE_NUM=$(echo $COVERAGE | sed 's/%//')
TARGET_COVERAGE=80

if (( $(echo "$COVERAGE_NUM >= $TARGET_COVERAGE" | bc -l) )); then
    echo -e "${GREEN}✓ 覆盖率达到目标 ($TARGET_COVERAGE%)${NC}"
else
    echo -e "${YELLOW}⚠ 覆盖率未达到目标 ($TARGET_COVERAGE%)${NC}"
fi

# 显示详细覆盖率信息
echo ""
echo -e "${BLUE}=== 详细覆盖率信息 ===${NC}"
go tool cover -func=coverage.out | grep -E "(utils|repository|service|handler)" | head -20

echo ""
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo "覆盖率报告: coverage.html"
echo "详细结果: test_results/"
echo ""

# 可选：打开覆盖率报告
if command -v xdg-open &> /dev/null; then
    read -p "是否打开覆盖率报告? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open coverage.html
    fi
elif command -v open &> /dev/null; then
    read -p "是否打开覆盖率报告? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open coverage.html
    fi
fi

echo -e "${GREEN}测试脚本执行完成！${NC}"
