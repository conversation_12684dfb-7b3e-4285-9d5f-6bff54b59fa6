package dto

import (
	"time"

	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// BatchUpdateStatusRequest 批量更新状态请求DTO
type BatchUpdateStatusRequest struct {
	ContentKSUIDs []string `json:"content_ksuids" binding:"required"`
	Status        string   `json:"status" binding:"required"`
	Reason        string   `json:"reason,omitempty"`
}

// BatchDeleteRequest 批量删除请求DTO
type BatchDeleteRequest struct {
	ContentKSUIDs []string `json:"content_ksuids" binding:"required"`
	Reason        string   `json:"reason,omitempty"`
}

// BatchOperationResponse 批量操作响应DTO
type BatchOperationResponse struct {
	SuccessCount int                   `json:"success_count"`
	FailureCount int                   `json:"failure_count"`
	Failures     []BatchOperationError `json:"failures,omitempty"`
	TotalCount   int                   `json:"total_count"`
	Message      string                `json:"message"`
}

// BatchOperationError 批量操作错误DTO
type BatchOperationError struct {
	ContentKSUID string `json:"content_ksuid"`
	Error        string `json:"error"`
}

// ContentSearchRequest 内容搜索请求DTO
type ContentSearchRequest struct {
	Query         string     `json:"query" binding:"required"`
	ContentTypes  []string   `json:"content_types,omitempty"`
	Status        string     `json:"status,omitempty"`
	CategoryID    uint       `json:"category_id,omitempty"`
	Tags          []string   `json:"tags,omitempty"`
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`
	SortBy        string     `json:"sort_by,omitempty"`
	SortOrder     string     `json:"sort_order,omitempty"`
	Page          int        `json:"page,omitempty"`
	Limit         int        `json:"limit,omitempty"`
}

// ContentSearchResponse 内容搜索响应DTO
type ContentSearchResponse struct {
	Contents   []*ContentResponse `json:"contents"`
	Total      int                `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
	Query      string             `json:"query"`
	SearchTime int64              `json:"search_time_ms"`
}

// OperationLogQueryRequest 操作日志查询请求DTO
type OperationLogQueryRequest struct {
	OperatorKSUID string     `json:"operator_ksuid,omitempty" form:"operator_ksuid"`
	OperationType string     `json:"operation_type,omitempty" form:"operation_type"`
	TargetType    string     `json:"target_type,omitempty" form:"target_type"`
	TargetKSUID   string     `json:"target_ksuid,omitempty" form:"target_ksuid"`
	StartTime     *time.Time `json:"start_time,omitempty" form:"start_time"`
	EndTime       *time.Time `json:"end_time,omitempty" form:"end_time"`
	Page          int        `json:"page,omitempty" form:"page"`
	Limit         int        `json:"limit,omitempty" form:"limit"`
}

// OperationLogResponse 操作日志响应DTO
type OperationLogResponse struct {
	ID            uint      `json:"id"`
	OperatorKSUID string    `json:"operator_ksuid"`
	OperationType string    `json:"operation_type"`
	TargetType    string    `json:"target_type"`
	TargetKSUID   string    `json:"target_ksuid"`
	Description   string    `json:"description"`
	BeforeData    string    `json:"before_data,omitempty"`
	AfterData     string    `json:"after_data,omitempty"`
	IPAddress     string    `json:"ip_address,omitempty"`
	UserAgent     string    `json:"user_agent,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
}

// OperationLogListResponse 操作日志列表响应DTO
type OperationLogListResponse struct {
	Logs       []*OperationLogResponse `json:"logs"`
	Total      int64                   `json:"total"`
	Page       int                     `json:"page"`
	Limit      int                     `json:"limit"`
	TotalPages int                     `json:"total_pages"`
}

// OperationLogStatsResponse 操作日志统计响应DTO
type OperationLogStatsResponse struct {
	TotalOperations      int64            `json:"total_operations"`
	OperationsByType     map[string]int64 `json:"operations_by_type"`
	OperationsByTarget   map[string]int64 `json:"operations_by_target"`
	OperationsByOperator map[string]int64 `json:"operations_by_operator"`
	RecentOperations     int64            `json:"recent_operations"`
}

// ConfigQueryRequest 配置查询请求DTO
type ConfigQueryRequest struct {
	Category string `json:"category,omitempty" form:"category"`
	IsActive *bool  `json:"is_active,omitempty" form:"is_active"`
	Page     int    `json:"page,omitempty" form:"page"`
	Limit    int    `json:"limit,omitempty" form:"limit"`
}

// ConfigResponse 配置响应DTO
type ConfigResponse struct {
	ID          uint      `json:"id"`
	ConfigKey   string    `json:"config_key"`
	ConfigValue string    `json:"config_value"`
	ConfigType  string    `json:"config_type"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ConfigListResponse 配置列表响应DTO
type ConfigListResponse struct {
	Configs    []*ConfigResponse `json:"configs"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	Limit      int               `json:"limit"`
	TotalPages int               `json:"total_pages"`
}

// ConfigUpdateRequest 配置更新请求DTO
type ConfigUpdateRequest struct {
	ConfigValue string `json:"config_value" binding:"required"`
	Description string `json:"description,omitempty"`
	IsActive    *bool  `json:"is_active,omitempty"`
}

// ConfigStatsResponse 配置统计响应DTO
type ConfigStatsResponse struct {
	TotalConfigs      int64            `json:"total_configs"`
	ActiveConfigs     int64            `json:"active_configs"`
	InactiveConfigs   int64            `json:"inactive_configs"`
	ConfigsByCategory map[string]int64 `json:"configs_by_category"`
	ConfigsByType     map[string]int64 `json:"configs_by_type"`
	LastUpdated       *time.Time       `json:"last_updated,omitempty"`
}

// ServiceHealthResponse 服务健康状态响应DTO
type ServiceHealthResponse struct {
	ServiceName   string    `json:"service_name"`
	Status        string    `json:"status"`
	LastCheckTime time.Time `json:"last_check_time"`
	ResponseTime  int64     `json:"response_time_ms"`
	ErrorMessage  string    `json:"error_message,omitempty"`
	IsHealthy     bool      `json:"is_healthy"`
}

// SystemStatsResponse 系统统计响应DTO
type SystemStatsResponse struct {
	Services        []*ServiceHealthResponse `json:"services"`
	TotalContents   int64                    `json:"total_contents"`
	TotalUsers      int64                    `json:"total_users"`
	TotalOperations int64                    `json:"total_operations"`
	CacheStats      *CacheStatsResponse      `json:"cache_stats"`
	DatabaseStats   *DatabaseStatsResponse   `json:"database_stats"`
	Uptime          int64                    `json:"uptime_seconds"`
}

// CacheStatsResponse 缓存统计响应DTO
type CacheStatsResponse struct {
	TotalCaches   int64            `json:"total_caches"`
	CachesByType  map[string]int64 `json:"caches_by_type"`
	ExpiredCaches int64            `json:"expired_caches"`
	CacheHitRate  float64          `json:"cache_hit_rate"`
	AvgCacheAge   float64          `json:"avg_cache_age_hours"`
}

// DatabaseStatsResponse 数据库统计响应DTO
type DatabaseStatsResponse struct {
	TotalTables     int   `json:"total_tables"`
	TotalRecords    int64 `json:"total_records"`
	DatabaseSize    int64 `json:"database_size_bytes"`
	ConnectionCount int   `json:"connection_count"`
}

// 转换函数

// ToBatchUpdateStatusRequest 转换为批量更新状态请求
func (req *BatchUpdateStatusRequest) ToBatchUpdateStatusRequest() *types.BatchUpdateStatusRequest {
	return &types.BatchUpdateStatusRequest{
		ContentKSUIDs: req.ContentKSUIDs,
		Status:        req.Status,
		Reason:        req.Reason,
	}
}

// ToBatchDeleteRequest 转换为批量删除请求
func (req *BatchDeleteRequest) ToBatchDeleteRequest() *types.BatchDeleteRequest {
	return &types.BatchDeleteRequest{
		ContentKSUIDs: req.ContentKSUIDs,
		Reason:        req.Reason,
	}
}

// ToContentSearchRequest 转换为内容搜索请求
func (req *ContentSearchRequest) ToContentSearchRequest() *types.ContentSearchRequest {
	return &types.ContentSearchRequest{
		Query:         req.Query,
		ContentTypes:  req.ContentTypes,
		Status:        req.Status,
		CategoryID:    req.CategoryID,
		Tags:          req.Tags,
		CreatedAfter:  req.CreatedAfter,
		CreatedBefore: req.CreatedBefore,
		SortBy:        req.SortBy,
		SortOrder:     req.SortOrder,
		Page:          req.Page,
		Limit:         req.Limit,
	}
}

// ToOperationLogQuery 转换为操作日志查询
func (req *OperationLogQueryRequest) ToOperationLogQuery() *types.OperationLogQuery {
	return &types.OperationLogQuery{
		OperatorKSUID: req.OperatorKSUID,
		OperationType: req.OperationType,
		TargetType:    req.TargetType,
		TargetKSUID:   req.TargetKSUID,
		StartTime:     req.StartTime,
		EndTime:       req.EndTime,
		Page:          req.Page,
		Limit:         req.Limit,
	}
}

// FromBatchOperationResult 从批量操作结果转换
func FromBatchOperationResult(result *types.BatchOperationResult) *BatchOperationResponse {
	if result == nil {
		return nil
	}

	failures := make([]BatchOperationError, len(result.Failures))
	for i, failure := range result.Failures {
		failures[i] = BatchOperationError{
			ContentKSUID: failure.ContentKSUID,
			Error:        failure.Error,
		}
	}

	message := "操作完成"
	if result.FailureCount > 0 {
		message = "操作部分成功"
	}

	return &BatchOperationResponse{
		SuccessCount: result.SuccessCount,
		FailureCount: result.FailureCount,
		Failures:     failures,
		TotalCount:   result.TotalCount,
		Message:      message,
	}
}

// FromContentSearchResult 从内容搜索结果转换
func FromContentSearchResult(result *types.ContentSearchResult, searchTimeMs int64) *ContentSearchResponse {
	if result == nil {
		return &ContentSearchResponse{
			Contents: []*ContentResponse{},
		}
	}

	contents := make([]*ContentResponse, len(result.Contents))
	for i, content := range result.Contents {
		contents[i] = FromBaseContent(content)
	}

	return &ContentSearchResponse{
		Contents:   contents,
		Total:      result.Total,
		Page:       result.Page,
		Limit:      result.Limit,
		TotalPages: result.TotalPages,
		Query:      result.Query,
		SearchTime: searchTimeMs,
	}
}
