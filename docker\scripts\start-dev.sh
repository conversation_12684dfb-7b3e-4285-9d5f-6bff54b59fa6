#!/bin/bash

# 启动开发环境脚本
set -e

echo "🚀 启动 PXPAT Backend 开发环境..."

# 检查 Docker 和 Docker Compose 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/docker"
DEV_ENV_DIR="$DOCKER_DIR/environments/development"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "🐳 Docker 目录: $DOCKER_DIR"
echo "🔧 开发环境目录: $DEV_ENV_DIR"

# 检查环境文件
if [ ! -f "$DEV_ENV_DIR/.env" ]; then
    echo "📝 创建开发环境配置文件..."
    cp "$DEV_ENV_DIR/.env.example" "$DEV_ENV_DIR/.env"
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p "$DOCKER_DIR/logs"
mkdir -p "$DOCKER_DIR/configs/init-scripts"

# 检查网络是否存在，不存在则创建
echo "🌐 检查 Docker 网络..."
networks=("content-network" "database-network" "service-discovery-network" "monitoring-network")
for network in "${networks[@]}"; do
    if ! docker network ls | grep -q "$network"; then
        echo "🌐 创建网络: $network"
        docker network create "$network"
    fi
done

# 进入开发环境目录
cd "$DEV_ENV_DIR"

# 拉取最新镜像
echo "📥 拉取最新镜像..."
docker-compose pull

# 构建服务镜像
echo "🔨 构建服务镜像..."
docker-compose build

# 启动服务
echo "🚀 启动开发环境服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 显示服务访问地址
echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📊 服务访问地址:"
echo "  - 内容管理服务: http://localhost:12010"
echo "  - PostgreSQL: localhost:5432"
echo "  - Redis: localhost:6379"
echo "  - Consul UI: http://localhost:8500"
echo ""
echo "📝 查看日志:"
echo "  docker-compose logs -f [service_name]"
echo ""
echo "🛑 停止服务:"
echo "  docker-compose down"
echo ""
echo "🔄 重启服务:"
echo "  docker-compose restart [service_name]"
echo ""

# 检查服务健康状态
echo "🏥 检查服务健康状态..."
sleep 5
if curl -f http://localhost:12010/api/v1/health &> /dev/null; then
    echo "✅ 内容管理服务健康检查通过"
else
    echo "⚠️  内容管理服务健康检查失败，请检查日志"
fi
