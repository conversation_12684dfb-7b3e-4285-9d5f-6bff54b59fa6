#!/usr/bin/env python3
import re
import os

def fix_response_calls(file_path):
    """修复response调用"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 修复 response.Error(c, http.StatusXXX, "message", nil) -> response.ErrorWithCode(c, http.StatusXXX, errors.New("message"))
    content = re.sub(
        r'response\.Error\(c, (http\.Status\w+), "([^"]+)", nil\)',
        r'response.ErrorWithCode(c, \1, errors.New("\2"))',
        content
    )
    
    # 修复 response.Success(c, "message", data) -> response.Success(c, data)
    content = re.sub(
        r'response\.Success\(c, "[^"]+", ([^)]+)\)',
        r'response.Success(c, \1)',
        content
    )
    
    # 修复 response.ErrorWithGlobalError(c, gErr) -> response.Error(c, gErr)
    content = re.sub(
        r'response\.ErrorWithGlobalError\(c, ([^)]+)\)',
        r'response.Error(c, \1)',
        content
    )
    
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Fixed {file_path}")
        return True
    return False

def main():
    # 修复complaint_handler.go
    handler_files = [
        "internal/user-cluster/complaint-service/external/handler/complaint_handler.go",
        "internal/user-cluster/complaint-service/external/handler/identity_handler.go", 
        "internal/user-cluster/complaint-service/external/handler/rights_handler.go",
        "internal/user-cluster/complaint-service/external/handler/admin_handler.go"
    ]
    
    for file_path in handler_files:
        if os.path.exists(file_path):
            fix_response_calls(file_path)
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    main()
