#!/bin/bash

# CI/CD测试环境设置脚本
# 用于在CI环境中设置测试所需的服务和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo -e "${BLUE}=== 设置CI测试环境 ===${NC}"

# 检查环境变量
check_env_vars() {
    echo -e "${YELLOW}检查环境变量...${NC}"
    
    required_vars=(
        "DB_HOST"
        "DB_PORT" 
        "DB_USER"
        "DB_PASSWORD"
        "DB_NAME"
        "REDIS_HOST"
        "REDIS_PORT"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            echo -e "${RED}错误: 环境变量 $var 未设置${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✓ 环境变量检查通过${NC}"
}

# 等待数据库就绪
wait_for_postgres() {
    echo -e "${YELLOW}等待PostgreSQL就绪...${NC}"
    
    timeout=60
    while ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            echo -e "${RED}✗ PostgreSQL连接超时${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✓ PostgreSQL就绪${NC}"
}

# 等待Redis就绪
wait_for_redis() {
    echo -e "${YELLOW}等待Redis就绪...${NC}"
    
    timeout=60
    while ! redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            echo -e "${RED}✗ Redis连接超时${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✓ Redis就绪${NC}"
}

# 初始化测试数据库
init_test_database() {
    echo -e "${YELLOW}初始化测试数据库...${NC}"
    
    # 创建数据库（如果不存在）
    PGPASSWORD="$DB_PASSWORD" createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" 2>/dev/null || true
    
    # 运行初始化脚本
    if [[ -f "$PROJECT_ROOT/docker/init-scripts/01-init-database.sql" ]]; then
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            -f "$PROJECT_ROOT/docker/init-scripts/01-init-database.sql"
    fi
    
    echo -e "${GREEN}✓ 测试数据库初始化完成${NC}"
}

# 清理Redis测试数据
cleanup_redis() {
    echo -e "${YELLOW}清理Redis测试数据...${NC}"
    
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" FLUSHALL
    
    echo -e "${GREEN}✓ Redis清理完成${NC}"
}

# 设置测试配置
setup_test_config() {
    echo -e "${YELLOW}设置测试配置...${NC}"
    
    # 创建测试配置目录
    TEST_CONFIG_DIR="$PROJECT_ROOT/config/test"
    mkdir -p "$TEST_CONFIG_DIR"
    
    # 生成测试配置文件
    cat > "$TEST_CONFIG_DIR/config.yaml" << EOF
server:
  port: 12010
  debug: true
  enable_cors: true
  enable_swagger: false

database:
  host: ${DB_HOST}
  port: ${DB_PORT}
  user: ${DB_USER}
  password: ${DB_PASSWORD}
  database: ${DB_NAME}
  ssl_mode: disable
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 300s

redis:
  address: ${REDIS_HOST}:${REDIS_PORT}
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5

jwt:
  secret_key: "test-jwt-secret-key"
  token_duration: 1h
  refresh_duration: 24h

services:
  video_service:
    enabled: false
    base_url: "http://mock-video-service:12001"
    timeout: 30s
  
  interaction_service:
    enabled: false
    base_url: "http://mock-interaction-service:12002"
    timeout: 30s

log:
  level: "debug"
  format: "text"
  output: "stdout"

cache:
  content_ttl: 60s
  interaction_ttl: 30s
  stats_ttl: 120s

management:
  limits:
    max_batch_size: 100
    max_search_results: 1000
    max_page_size: 100
    default_page_size: 20

security:
  rate_limit:
    default_limit: 1000
    default_window: 60s
    batch_limit: 50
    batch_window: 60s
EOF
    
    echo -e "${GREEN}✓ 测试配置设置完成${NC}"
}

# 启动Mock服务
start_mock_services() {
    echo -e "${YELLOW}启动Mock服务...${NC}"
    
    # 这里可以启动Mock服务器
    # 例如使用mockserver或者简单的HTTP服务器
    
    echo -e "${GREEN}✓ Mock服务启动完成${NC}"
}

# 验证测试环境
verify_test_env() {
    echo -e "${YELLOW}验证测试环境...${NC}"
    
    # 检查数据库连接
    if ! PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &>/dev/null; then
        echo -e "${RED}✗ 数据库连接失败${NC}"
        exit 1
    fi
    
    # 检查Redis连接
    if ! redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping &>/dev/null; then
        echo -e "${RED}✗ Redis连接失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 测试环境验证通过${NC}"
}

# 生成测试数据
generate_test_data() {
    echo -e "${YELLOW}生成测试数据...${NC}"
    
    # 插入一些测试配置
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
INSERT INTO management_configs (config_key, config_value, category, description, created_by) VALUES
('test.enabled', 'true', 'test', '测试模式启用标志', 'system'),
('test.mock_services', 'true', 'test', '使用Mock服务', 'system'),
('test.data_retention', '1', 'test', '测试数据保留天数', 'system')
ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    updated_at = CURRENT_TIMESTAMP;
EOF
    
    echo -e "${GREEN}✓ 测试数据生成完成${NC}"
}

# 主函数
main() {
    check_env_vars
    wait_for_postgres
    wait_for_redis
    init_test_database
    cleanup_redis
    setup_test_config
    start_mock_services
    verify_test_env
    generate_test_data
    
    echo -e "${GREEN}🎉 CI测试环境设置完成！${NC}"
    echo ""
    echo -e "${BLUE}环境信息:${NC}"
    echo "数据库: $DB_HOST:$DB_PORT/$DB_NAME"
    echo "Redis: $REDIS_HOST:$REDIS_PORT"
    echo "配置文件: $PROJECT_ROOT/config/test/config.yaml"
}

# 执行主函数
main "$@"
