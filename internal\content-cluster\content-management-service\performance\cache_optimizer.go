package performance

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/types"
	"pxpat-backend/pkg/cache"
)

// CacheOptimizer 缓存优化器
type CacheOptimizer struct {
	redis        *redis.Client
	cacheManager cache.Manager

	// 缓存策略配置
	strategies map[string]*CacheStrategy

	// 性能监控
	metrics *CacheMetrics
	mu      sync.RWMutex
}

// CacheStrategy 缓存策略
type CacheStrategy struct {
	TTL                time.Duration // 缓存过期时间
	RefreshThreshold   time.Duration // 刷新阈值
	MaxSize            int64         // 最大缓存大小
	EvictionPolicy     string        // 淘汰策略
	WarmupEnabled      bool          // 是否启用预热
	CompressionEnabled bool          // 是否启用压缩
}

// CacheMetrics 缓存性能指标
type CacheMetrics struct {
	HitCount    int64
	MissCount   int64
	SetCount    int64
	DeleteCount int64
	ErrorCount  int64

	// 响应时间统计
	AvgGetTime time.Duration
	AvgSetTime time.Duration

	// 内存使用统计
	MemoryUsage int64
	KeyCount    int64

	mu sync.RWMutex
}

// NewCacheOptimizer 创建缓存优化器
func NewCacheOptimizer(redis *redis.Client, cacheManager cache.Manager) *CacheOptimizer {
	optimizer := &CacheOptimizer{
		redis:        redis,
		cacheManager: cacheManager,
		strategies:   make(map[string]*CacheStrategy),
		metrics:      &CacheMetrics{},
	}

	// 初始化默认缓存策略
	optimizer.initDefaultStrategies()

	return optimizer
}

// initDefaultStrategies 初始化默认缓存策略
func (co *CacheOptimizer) initDefaultStrategies() {
	// 内容缓存策略
	co.strategies["content"] = &CacheStrategy{
		TTL:                5 * time.Minute,
		RefreshThreshold:   1 * time.Minute,
		MaxSize:            10000,
		EvictionPolicy:     "LRU",
		WarmupEnabled:      true,
		CompressionEnabled: true,
	}

	// 统计数据缓存策略
	co.strategies["stats"] = &CacheStrategy{
		TTL:                10 * time.Minute,
		RefreshThreshold:   2 * time.Minute,
		MaxSize:            5000,
		EvictionPolicy:     "LRU",
		WarmupEnabled:      false,
		CompressionEnabled: false,
	}

	// 用户数据缓存策略
	co.strategies["user"] = &CacheStrategy{
		TTL:                15 * time.Minute,
		RefreshThreshold:   3 * time.Minute,
		MaxSize:            20000,
		EvictionPolicy:     "LFU",
		WarmupEnabled:      true,
		CompressionEnabled: false,
	}

	// 配置数据缓存策略
	co.strategies["config"] = &CacheStrategy{
		TTL:                30 * time.Minute,
		RefreshThreshold:   5 * time.Minute,
		MaxSize:            1000,
		EvictionPolicy:     "FIFO",
		WarmupEnabled:      true,
		CompressionEnabled: false,
	}
}

// OptimizedGet 优化的缓存获取
func (co *CacheOptimizer) OptimizedGet(ctx context.Context, key string, cacheType string, dest interface{}) error {
	startTime := time.Now()
	defer func() {
		co.updateGetMetrics(time.Since(startTime))
	}()

	strategy, exists := co.strategies[cacheType]
	if !exists {
		strategy = co.strategies["content"] // 默认策略
	}

	// 尝试从缓存获取
	err := co.cacheManager.Get(ctx, key, dest)
	if err == nil {
		co.recordHit()

		// 检查是否需要刷新
		if co.shouldRefresh(ctx, key, strategy) {
			go co.refreshCache(ctx, key, cacheType)
		}

		return nil
	}

	co.recordMiss()
	return err
}

// OptimizedSet 优化的缓存设置
func (co *CacheOptimizer) OptimizedSet(ctx context.Context, key string, value interface{}, cacheType string) error {
	startTime := time.Now()
	defer func() {
		co.updateSetMetrics(time.Since(startTime))
	}()

	strategy, exists := co.strategies[cacheType]
	if !exists {
		strategy = co.strategies["content"] // 默认策略
	}

	// 检查缓存大小限制
	if err := co.checkCacheSize(ctx, cacheType, strategy); err != nil {
		log.Warn().Err(err).Str("cache_type", cacheType).Msg("缓存大小检查失败")
	}

	// 设置缓存
	err := co.cacheManager.Set(ctx, key, value, strategy.TTL)
	if err == nil {
		co.recordSet()

		// 更新缓存元数据
		co.updateCacheMetadata(ctx, key, cacheType)
	} else {
		co.recordError()
	}

	return err
}

// BatchGet 批量获取缓存
func (co *CacheOptimizer) BatchGet(ctx context.Context, keys []string, cacheType string) (map[string]interface{}, []string) {
	results := make(map[string]interface{})
	missedKeys := make([]string, 0)

	// 使用Pipeline批量获取
	pipe := co.redis.Pipeline()
	cmds := make(map[string]*redis.StringCmd)

	for _, key := range keys {
		cmds[key] = pipe.Get(ctx, key)
	}

	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		log.Error().Err(err).Msg("批量获取缓存失败")
		return results, keys
	}

	for key, cmd := range cmds {
		val, err := cmd.Result()
		if err == redis.Nil {
			missedKeys = append(missedKeys, key)
			co.recordMiss()
		} else if err != nil {
			missedKeys = append(missedKeys, key)
			co.recordError()
		} else {
			results[key] = val
			co.recordHit()
		}
	}

	return results, missedKeys
}

// BatchSet 批量设置缓存
func (co *CacheOptimizer) BatchSet(ctx context.Context, data map[string]interface{}, cacheType string) error {
	strategy, exists := co.strategies[cacheType]
	if !exists {
		strategy = co.strategies["content"]
	}

	// 使用Pipeline批量设置
	pipe := co.redis.Pipeline()

	for key, value := range data {
		pipe.Set(ctx, key, value, strategy.TTL)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		co.recordError()
		return fmt.Errorf("批量设置缓存失败: %w", err)
	}

	co.metrics.mu.Lock()
	co.metrics.SetCount += int64(len(data))
	co.metrics.mu.Unlock()

	return nil
}

// WarmupCache 缓存预热
func (co *CacheOptimizer) WarmupCache(ctx context.Context, cacheType string, warmupFunc func(ctx context.Context) (map[string]interface{}, error)) error {
	strategy, exists := co.strategies[cacheType]
	if !exists || !strategy.WarmupEnabled {
		return nil
	}

	log.Info().Str("cache_type", cacheType).Msg("开始缓存预热")

	data, err := warmupFunc(ctx)
	if err != nil {
		return fmt.Errorf("缓存预热数据获取失败: %w", err)
	}

	return co.BatchSet(ctx, data, cacheType)
}

// shouldRefresh 检查是否需要刷新缓存
func (co *CacheOptimizer) shouldRefresh(ctx context.Context, key string, strategy *CacheStrategy) bool {
	// 获取缓存的TTL
	ttl, err := co.redis.TTL(ctx, key).Result()
	if err != nil {
		return false
	}

	// 如果剩余时间小于刷新阈值，则需要刷新
	return ttl < strategy.RefreshThreshold
}

// refreshCache 异步刷新缓存
func (co *CacheOptimizer) refreshCache(ctx context.Context, key string, cacheType string) {
	// 这里应该调用相应的数据获取函数来刷新缓存
	// 具体实现需要根据业务逻辑来定制
	log.Debug().Str("key", key).Str("cache_type", cacheType).Msg("异步刷新缓存")
}

// checkCacheSize 检查缓存大小
func (co *CacheOptimizer) checkCacheSize(ctx context.Context, cacheType string, strategy *CacheStrategy) error {
	// 获取当前缓存键数量
	pattern := fmt.Sprintf("%s:*", cacheType)
	keys, err := co.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}

	if int64(len(keys)) >= strategy.MaxSize {
		// 执行缓存淘汰
		return co.evictCache(ctx, cacheType, strategy, len(keys)-int(strategy.MaxSize)+1)
	}

	return nil
}

// evictCache 缓存淘汰
func (co *CacheOptimizer) evictCache(ctx context.Context, cacheType string, strategy *CacheStrategy, count int) error {
	pattern := fmt.Sprintf("%s:*", cacheType)

	switch strategy.EvictionPolicy {
	case "LRU":
		return co.evictLRU(ctx, pattern, count)
	case "LFU":
		return co.evictLFU(ctx, pattern, count)
	case "FIFO":
		return co.evictFIFO(ctx, pattern, count)
	default:
		return co.evictLRU(ctx, pattern, count)
	}
}

// evictLRU LRU淘汰策略
func (co *CacheOptimizer) evictLRU(ctx context.Context, pattern string, count int) error {
	// 简化实现：随机删除
	keys, err := co.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}

	if len(keys) <= count {
		return nil
	}

	// 删除最旧的键
	for i := 0; i < count && i < len(keys); i++ {
		co.redis.Del(ctx, keys[i])
	}

	return nil
}

// evictLFU LFU淘汰策略
func (co *CacheOptimizer) evictLFU(ctx context.Context, pattern string, count int) error {
	// 简化实现，实际应该根据访问频率来淘汰
	return co.evictLRU(ctx, pattern, count)
}

// evictFIFO FIFO淘汰策略
func (co *CacheOptimizer) evictFIFO(ctx context.Context, pattern string, count int) error {
	// 简化实现，实际应该根据创建时间来淘汰
	return co.evictLRU(ctx, pattern, count)
}

// updateCacheMetadata 更新缓存元数据
func (co *CacheOptimizer) updateCacheMetadata(ctx context.Context, key string, cacheType string) {
	// 记录缓存创建时间、访问次数等元数据
	metaKey := fmt.Sprintf("meta:%s", key)
	metadata := map[string]interface{}{
		"created_at":   time.Now().Unix(),
		"access_count": 1,
		"cache_type":   cacheType,
	}

	co.redis.HMSet(ctx, metaKey, metadata)
	co.redis.Expire(ctx, metaKey, 24*time.Hour) // 元数据保留24小时
}

// 性能指标记录方法
func (co *CacheOptimizer) recordHit() {
	co.metrics.mu.Lock()
	co.metrics.HitCount++
	co.metrics.mu.Unlock()
}

func (co *CacheOptimizer) recordMiss() {
	co.metrics.mu.Lock()
	co.metrics.MissCount++
	co.metrics.mu.Unlock()
}

func (co *CacheOptimizer) recordSet() {
	co.metrics.mu.Lock()
	co.metrics.SetCount++
	co.metrics.mu.Unlock()
}

func (co *CacheOptimizer) recordError() {
	co.metrics.mu.Lock()
	co.metrics.ErrorCount++
	co.metrics.mu.Unlock()
}

func (co *CacheOptimizer) updateGetMetrics(duration time.Duration) {
	co.metrics.mu.Lock()
	co.metrics.AvgGetTime = (co.metrics.AvgGetTime + duration) / 2
	co.metrics.mu.Unlock()
}

func (co *CacheOptimizer) updateSetMetrics(duration time.Duration) {
	co.metrics.mu.Lock()
	co.metrics.AvgSetTime = (co.metrics.AvgSetTime + duration) / 2
	co.metrics.mu.Unlock()
}

// GetMetrics 获取缓存性能指标
func (co *CacheOptimizer) GetMetrics() *CacheMetrics {
	co.metrics.mu.RLock()
	defer co.metrics.mu.RUnlock()

	return &CacheMetrics{
		HitCount:    co.metrics.HitCount,
		MissCount:   co.metrics.MissCount,
		SetCount:    co.metrics.SetCount,
		DeleteCount: co.metrics.DeleteCount,
		ErrorCount:  co.metrics.ErrorCount,
		AvgGetTime:  co.metrics.AvgGetTime,
		AvgSetTime:  co.metrics.AvgSetTime,
		MemoryUsage: co.metrics.MemoryUsage,
		KeyCount:    co.metrics.KeyCount,
	}
}

// GetHitRate 获取缓存命中率
func (co *CacheOptimizer) GetHitRate() float64 {
	co.metrics.mu.RLock()
	defer co.metrics.mu.RUnlock()

	total := co.metrics.HitCount + co.metrics.MissCount
	if total == 0 {
		return 0
	}

	return float64(co.metrics.HitCount) / float64(total)
}
