package consumer

import (
	"context"
	"encoding/json"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/streadway/amqp"

	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
)

// ComplaintConsumer 投诉消息消费者接口
type ComplaintConsumer interface {
	// 启动消费者
	Start(ctx context.Context) error

	// 停止消费者
	Stop() error
}

// complaintConsumer 投诉消息消费者实现
type complaintConsumer struct {
	conn              *amqp.Connection
	channel           *amqp.Channel
	exchange          string
	queueName         string
	complaintService  service.ComplaintService
	userServiceClient service.UserServiceClient
	done              chan bool
}

// NewComplaintConsumer 创建投诉消息消费者实例
func NewComplaintConsumer(
	conn *amqp.Connection,
	exchange string,
	queueName string,
	complaintService service.ComplaintService,
	userServiceClient service.UserServiceClient,
) (ComplaintConsumer, error) {
	channel, err := conn.Channel()
	if err != nil {
		return nil, err
	}

	// 声明交换机
	err = channel.ExchangeDeclare(
		exchange,
		"topic",
		true,  // durable
		false, // auto-deleted
		false, // internal
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		return nil, err
	}

	// 声明队列
	_, err = channel.QueueDeclare(
		queueName,
		true,  // durable
		false, // delete when unused
		false, // exclusive
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		return nil, err
	}

	// 绑定队列到交换机
	err = channel.QueueBind(
		queueName,
		"user.blocked", // routing key
		exchange,
		false,
		nil,
	)
	if err != nil {
		return nil, err
	}

	err = channel.QueueBind(
		queueName,
		"content.deleted", // routing key
		exchange,
		false,
		nil,
	)
	if err != nil {
		return nil, err
	}

	return &complaintConsumer{
		conn:              conn,
		channel:           channel,
		exchange:          exchange,
		queueName:         queueName,
		complaintService:  complaintService,
		userServiceClient: userServiceClient,
		done:              make(chan bool),
	}, nil
}

// UserBlockedEvent 用户被封禁事件
type UserBlockedEvent struct {
	UserKSUID   string    `json:"user_ksuid"`
	BlockReason string    `json:"block_reason"`
	BlockedBy   string    `json:"blocked_by"`
	BlockedAt   time.Time `json:"blocked_at"`
	EventType   string    `json:"event_type"`
	Timestamp   time.Time `json:"timestamp"`
}

// ContentDeletedEvent 内容删除事件
type ContentDeletedEvent struct {
	ContentKSUID string    `json:"content_ksuid"`
	ContentType  string    `json:"content_type"`
	DeleteReason string    `json:"delete_reason"`
	DeletedBy    string    `json:"deleted_by"`
	DeletedAt    time.Time `json:"deleted_at"`
	EventType    string    `json:"event_type"`
	Timestamp    time.Time `json:"timestamp"`
}

// Start 启动消费者
func (c *complaintConsumer) Start(ctx context.Context) error {
	// 设置QoS
	err := c.channel.Qos(
		10,    // prefetch count
		0,     // prefetch size
		false, // global
	)
	if err != nil {
		return err
	}

	// 开始消费消息
	msgs, err := c.channel.Consume(
		c.queueName,
		"",    // consumer
		false, // auto-ack
		false, // exclusive
		false, // no-local
		false, // no-wait
		nil,   // args
	)
	if err != nil {
		return err
	}

	log.Info().Str("queue", c.queueName).Msg("投诉消息消费者启动成功")

	go func() {
		for {
			select {
			case msg := <-msgs:
				if msg.Body != nil {
					c.handleMessage(ctx, msg)
				}
			case <-c.done:
				log.Info().Msg("投诉消息消费者停止")
				return
			case <-ctx.Done():
				log.Info().Msg("投诉消息消费者上下文取消")
				return
			}
		}
	}()

	return nil
}

// Stop 停止消费者
func (c *complaintConsumer) Stop() error {
	close(c.done)
	if c.channel != nil {
		return c.channel.Close()
	}
	return nil
}

// handleMessage 处理消息
func (c *complaintConsumer) handleMessage(ctx context.Context, msg amqp.Delivery) {
	log.Debug().
		Str("routing_key", msg.RoutingKey).
		Str("body", string(msg.Body)).
		Msg("收到消息")

	var err error
	switch msg.RoutingKey {
	case "user.blocked":
		err = c.handleUserBlockedEvent(ctx, msg.Body)
	case "content.deleted":
		err = c.handleContentDeletedEvent(ctx, msg.Body)
	default:
		log.Warn().Str("routing_key", msg.RoutingKey).Msg("未知的消息类型")
		err = msg.Ack(false)
		return
	}

	if err != nil {
		log.Error().Err(err).Str("routing_key", msg.RoutingKey).Msg("处理消息失败")
		// 拒绝消息并重新入队
		msg.Nack(false, true)
	} else {
		// 确认消息
		msg.Ack(false)
	}
}

// handleUserBlockedEvent 处理用户被封禁事件
func (c *complaintConsumer) handleUserBlockedEvent(ctx context.Context, body []byte) error {
	var event UserBlockedEvent
	if err := json.Unmarshal(body, &event); err != nil {
		log.Error().Err(err).Msg("解析用户封禁事件失败")
		return err
	}

	log.Info().
		Str("user_ksuid", event.UserKSUID).
		Str("block_reason", event.BlockReason).
		Msg("处理用户封禁事件")

	// 这里可以添加业务逻辑，比如：
	// 1. 自动处理该用户的待处理投诉
	// 2. 通知相关投诉人
	// 3. 更新投诉统计数据

	// 示例：自动拒绝该用户的所有待处理投诉
	// TODO: 实现自动处理逻辑

	return nil
}

// handleContentDeletedEvent 处理内容删除事件
func (c *complaintConsumer) handleContentDeletedEvent(ctx context.Context, body []byte) error {
	var event ContentDeletedEvent
	if err := json.Unmarshal(body, &event); err != nil {
		log.Error().Err(err).Msg("解析内容删除事件失败")
		return err
	}

	log.Info().
		Str("content_ksuid", event.ContentKSUID).
		Str("content_type", event.ContentType).
		Str("delete_reason", event.DeleteReason).
		Msg("处理内容删除事件")

	// 这里可以添加业务逻辑，比如：
	// 1. 自动通过针对该内容的投诉
	// 2. 通知投诉人内容已被删除
	// 3. 更新投诉统计数据

	// 示例：自动通过针对该内容的所有待处理投诉
	// TODO: 实现自动处理逻辑

	return nil
}
