package handler

import (
	"pxpat-backend/internal/storage-cluster/content-storage-service/external/service"
	"pxpat-backend/internal/storage-cluster/content-storage-service/types"

	"github.com/rs/zerolog/log"
)

// ProvideVideoHandler 提供视频处理器
func ProvideVideoHandler(cfg *types.Config, videoService *service.VideoService) *VideoHandler {
	handler := NewVideoHandler(cfg, videoService)
	log.Info().Msg("外部视频处理器初始化成功")
	return handler
}

// ProvideCoverHandler 提供封面处理器
func ProvideCoverHandler(cfg *types.Config, coverService *service.CoverService) *CoverHandler {
	handler := NewCoverHandler(cfg, coverService)
	log.Info().Msg("外部封面处理器初始化成功")
	return handler
}
