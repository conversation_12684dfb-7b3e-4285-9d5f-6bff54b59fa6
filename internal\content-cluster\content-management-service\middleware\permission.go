package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/middleware/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

// PermissionMiddleware 权限控制中间件
type PermissionMiddleware struct {
	// 这里可以添加权限服务客户端或权限配置
}

// NewPermissionMiddleware 创建权限控制中间件实例
func NewPermissionMiddleware() *PermissionMiddleware {
	return &PermissionMiddleware{}
}

// RequirePermission 需要特定权限的中间件
func (m *PermissionMiddleware) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 链路追踪
		ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "middleware", "RequirePermission")
		defer span.End()
		c.Request = c.Request.WithContext(ctx)

		// 2. 获取trace信息
		traceID := tracing.GetTraceID(c)
		userKSUID := ksuid.GetKSUID(c)

		// 3. 添加span属性
		opentelemetry.AddAttribute(span, "method", c.Request.Method)
		opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
		opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
		opentelemetry.AddAttribute(span, "required_permission", permission)

		// 4. 检查是否已认证
		if userKSUID == "" {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Str("required_permission", permission).
				Msg("未认证用户尝试访问需要权限的接口")

			opentelemetry.AddError(span, errors.NewPermissionError("需要认证"))
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "需要认证",
			})
			c.Abort()
			return
		}

		// 5. 检查用户权限
		hasPermission := m.checkUserPermission(userKSUID, permission)
		if !hasPermission {
			log.Warn().
				Str("trace_id", traceID).
				Str("user_ksuid", userKSUID).
				Str("path", c.Request.URL.Path).
				Str("required_permission", permission).
				Msg("用户权限不足")

			opentelemetry.AddError(span, errors.NewPermissionError("权限不足"))
			c.JSON(http.StatusForbidden, globalTypes.GlobalResponse{
				Code: errors.PERMISSION_DENIED,
				Msg:  "权限不足",
			})
			c.Abort()
			return
		}

		// 6. 记录权限检查成功日志
		log.Debug().
			Str("trace_id", traceID).
			Str("user_ksuid", userKSUID).
			Str("path", c.Request.URL.Path).
			Str("required_permission", permission).
			Msg("权限检查通过")

		// 7. 继续处理请求
		c.Next()
	}
}

// RequireContentOwnership 需要内容所有权的中间件
func (m *PermissionMiddleware) RequireContentOwnership() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 链路追踪
		ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "middleware", "RequireContentOwnership")
		defer span.End()
		c.Request = c.Request.WithContext(ctx)

		// 2. 获取trace信息
		traceID := tracing.GetTraceID(c)
		userKSUID := ksuid.GetKSUID(c)

		// 3. 获取内容KSUID
		contentKSUID := c.Param("content_ksuid")
		if contentKSUID == "" {
			// 如果没有content_ksuid参数，跳过所有权检查
			c.Next()
			return
		}

		// 4. 添加span属性
		opentelemetry.AddAttribute(span, "method", c.Request.Method)
		opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
		opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
		opentelemetry.AddAttribute(span, "content_ksuid", contentKSUID)

		// 5. 检查是否已认证
		if userKSUID == "" {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Str("content_ksuid", contentKSUID).
				Msg("未认证用户尝试访问内容")

			opentelemetry.AddError(span, errors.NewPermissionError("需要认证"))
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "需要认证",
			})
			c.Abort()
			return
		}

		// 6. 检查内容所有权
		isOwner := m.checkContentOwnership(userKSUID, contentKSUID)
		if !isOwner {
			log.Warn().
				Str("trace_id", traceID).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", contentKSUID).
				Str("path", c.Request.URL.Path).
				Msg("用户不是内容所有者")

			opentelemetry.AddError(span, errors.NewPermissionError("无权限操作此内容"))
			c.JSON(http.StatusForbidden, globalTypes.GlobalResponse{
				Code: errors.PERMISSION_DENIED,
				Msg:  "无权限操作此内容",
			})
			c.Abort()
			return
		}

		// 7. 记录所有权检查成功日志
		log.Debug().
			Str("trace_id", traceID).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("path", c.Request.URL.Path).
			Msg("内容所有权检查通过")

		// 8. 继续处理请求
		c.Next()
	}
}

// RequireAnyPermission 需要任意一个权限的中间件
func (m *PermissionMiddleware) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 链路追踪
		ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "middleware", "RequireAnyPermission")
		defer span.End()
		c.Request = c.Request.WithContext(ctx)

		// 2. 获取trace信息
		traceID := tracing.GetTraceID(c)
		userKSUID := ksuid.GetKSUID(c)

		// 3. 添加span属性
		opentelemetry.AddAttribute(span, "method", c.Request.Method)
		opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
		opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
		opentelemetry.AddAttribute(span, "required_permissions", strings.Join(permissions, ","))

		// 4. 检查是否已认证
		if userKSUID == "" {
			log.Warn().
				Str("trace_id", traceID).
				Str("path", c.Request.URL.Path).
				Strs("required_permissions", permissions).
				Msg("未认证用户尝试访问需要权限的接口")

			opentelemetry.AddError(span, errors.NewPermissionError("需要认证"))
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.UNAUTHORIZED,
				Msg:  "需要认证",
			})
			c.Abort()
			return
		}

		// 5. 检查用户是否拥有任意一个权限
		hasAnyPermission := false
		for _, permission := range permissions {
			if m.checkUserPermission(userKSUID, permission) {
				hasAnyPermission = true
				opentelemetry.AddAttribute(span, "granted_permission", permission)
				break
			}
		}

		if !hasAnyPermission {
			log.Warn().
				Str("trace_id", traceID).
				Str("user_ksuid", userKSUID).
				Str("path", c.Request.URL.Path).
				Strs("required_permissions", permissions).
				Msg("用户权限不足")

			opentelemetry.AddError(span, errors.NewPermissionError("权限不足"))
			c.JSON(http.StatusForbidden, globalTypes.GlobalResponse{
				Code: errors.PERMISSION_DENIED,
				Msg:  "权限不足",
			})
			c.Abort()
			return
		}

		// 6. 记录权限检查成功日志
		log.Debug().
			Str("trace_id", traceID).
			Str("user_ksuid", userKSUID).
			Str("path", c.Request.URL.Path).
			Strs("required_permissions", permissions).
			Msg("权限检查通过")

		// 7. 继续处理请求
		c.Next()
	}
}

// checkUserPermission 检查用户权限（简化实现）
func (m *PermissionMiddleware) checkUserPermission(userKSUID string, permission string) bool {
	// TODO: 实现真实的权限检查逻辑
	// 这里应该调用用户服务或权限服务来检查用户权限
	// 目前简化实现，假设所有认证用户都有基础权限

	switch permission {
	case "content:read":
		return true // 所有认证用户都可以读取内容
	case "content:write":
		return true // 所有认证用户都可以写入内容
	case "content:delete":
		return true // 所有认证用户都可以删除自己的内容
	case "content:manage":
		return true // 管理员权限，这里简化为所有用户都有
	case "stats:read":
		return true // 所有认证用户都可以查看统计
	case "batch:operate":
		return true // 批量操作权限
	default:
		return false
	}
}

// checkContentOwnership 检查内容所有权（简化实现）
func (m *PermissionMiddleware) checkContentOwnership(userKSUID string, contentKSUID string) bool {
	// TODO: 实现真实的所有权检查逻辑
	// 这里应该调用内容服务来检查内容的所有者
	// 目前简化实现，假设所有用户都是内容所有者
	return true
}
