package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/client"
	"pxpat-backend/internal/content-cluster/content-management-service/dto"
	"pxpat-backend/internal/content-cluster/content-management-service/repository"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
	"pxpat-backend/internal/content-cluster/content-management-service/utils"
	"pxpat-backend/pkg/errors"
)

// StatsAnalysisService 统计分析服务接口
type StatsAnalysisService interface {
	// 总体统计
	GetOverviewStats(ctx context.Context) (*dto.ContentOverviewResponse, *errors.Errors)
	GetContentTypeStats(ctx context.Context, contentType string) (*dto.ContentTypeStatsResponse, *errors.Errors)
	GetAllContentTypeStats(ctx context.Context) ([]*dto.ContentTypeStatsResponse, *errors.Errors)

	// 用户统计
	GetUserStats(ctx context.Context, userKSUID string) (*dto.UserInteractionStatsResponse, *errors.Errors)
	GetActiveUsers(ctx context.Context, period string, limit int) (*dto.ActiveUserListResponse, *errors.Errors)

	// 趋势分析
	GetContentTrends(ctx context.Context, period string, contentType string) (*dto.ContentTrendsStatsResponse, *errors.Errors)
	GetPopularContents(ctx context.Context, period string, limit int) (*dto.PopularContentListResponse, *errors.Errors)

	// 分类和标签统计
	GetCategoryStats(ctx context.Context) ([]*dto.CategoryStatsResponse, *errors.Errors)
	GetTagStats(ctx context.Context, limit int) ([]*dto.TagStatsResponse, *errors.Errors)

	// 交互统计
	GetInteractionStats(ctx context.Context, contentKSUID string) (*dto.InteractionStatsResponse, *errors.Errors)
	GetOverallInteractionStats(ctx context.Context) (*dto.OverallInteractionStatsResponse, *errors.Errors)

	// 仪表板统计
	GetDashboardStats(ctx context.Context) (*dto.DashboardStatsResponse, *errors.Errors)

	// 报告生成
	GenerateReport(ctx context.Context, request *dto.ReportRequest) (*dto.ReportResponse, *errors.Errors)
	GetReport(ctx context.Context, reportID string) (*dto.ReportResponse, *errors.Errors)

	// 健康检查
	HealthCheck(ctx context.Context) *errors.Errors
}

// statsAnalysisService 统计分析服务实现
type statsAnalysisService struct {
	// 服务客户端
	videoClient       client.VideoServiceClient
	novelClient       client.NovelServiceClient // 预留
	musicClient       client.MusicServiceClient // 预留
	interactionClient client.InteractionServiceClient

	// 数据访问层
	cacheRepo     repository.ContentCacheRepository
	operationRepo repository.OperationLogRepository
	configRepo    repository.ConfigRepository

	// 工具组件
	aggregator utils.DataAggregator
	validator  utils.ContentValidator

	// 基础设施
	redis  *redis.Client
	logger *log.Logger
}

// NewStatsAnalysisService 创建统计分析服务实例
func NewStatsAnalysisService(
	videoClient client.VideoServiceClient,
	interactionClient client.InteractionServiceClient,
	cacheRepo repository.ContentCacheRepository,
	operationRepo repository.OperationLogRepository,
	configRepo repository.ConfigRepository,
	aggregator utils.DataAggregator,
	validator utils.ContentValidator,
	redis *redis.Client,
) StatsAnalysisService {
	return &statsAnalysisService{
		videoClient:       videoClient,
		interactionClient: interactionClient,
		cacheRepo:         cacheRepo,
		operationRepo:     operationRepo,
		configRepo:        configRepo,
		aggregator:        aggregator,
		validator:         validator,
		redis:             redis,
		logger:            &log.Logger,
	}
}

// GetOverviewStats 获取总体统计概览
func (s *statsAnalysisService) GetOverviewStats(ctx context.Context) (*dto.ContentOverviewResponse, *errors.Errors) {
	log.Info().Msg("开始获取总体统计概览")

	// 尝试从缓存获取
	cacheKey := "stats:overview"
	var cachedResult dto.ContentOverviewResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().Msg("从缓存获取总体统计成功")
		return &cachedResult, nil
	}

	// 缓存未命中，从各服务聚合数据
	var wg sync.WaitGroup
	var mu sync.Mutex
	var totalContents, publishedContents, draftContents, archivedContents int64
	var totalViews, totalComments int64
	var totalLikes, totalFavorites int64
	var serviceErrors []error

	// 获取视频服务统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		videoStats, err := s.videoClient.GetOverallStats()
		if err != nil {
			mu.Lock()
			serviceErrors = append(serviceErrors, fmt.Errorf("video service error: %w", err))
			mu.Unlock()
			return
		}

		mu.Lock()
		totalContents += videoStats.TotalCount
		publishedContents += videoStats.PublishedCount
		draftContents += videoStats.DraftCount
		archivedContents += videoStats.ArchivedCount
		totalViews += videoStats.TotalViews
		totalComments += videoStats.TotalComments
		mu.Unlock()
	}()

	// 获取交互服务统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		interactionStats, err := s.interactionClient.GetOverallInteractionStats()
		if err != nil {
			mu.Lock()
			serviceErrors = append(serviceErrors, fmt.Errorf("interaction service error: %w", err))
			mu.Unlock()
			return
		}

		mu.Lock()
		totalLikes += interactionStats.TotalLikes
		totalFavorites += interactionStats.TotalFavorites
		mu.Unlock()
	}()

	// 如果启用了小说服务
	if s.novelClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留小说服务统计
			log.Debug().Msg("小说服务统计暂未实现")
		}()
	}

	// 如果启用了音乐服务
	if s.musicClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留音乐服务统计
			log.Debug().Msg("音乐服务统计暂未实现")
		}()
	}

	wg.Wait()

	// 检查服务错误
	if len(serviceErrors) > 0 {
		log.Error().
			Interface("errors", serviceErrors).
			Msg("获取统计数据时发生服务错误")
		// 如果所有服务都失败，返回错误
		if totalContents == 0 && len(serviceErrors) > 1 {
			return nil, errors.NewInternalError("统计服务不可用")
		}
	}

	// 计算平均值
	var avgViewsPerContent, avgLikesPerContent float64
	if totalContents > 0 {
		avgViewsPerContent = float64(totalViews) / float64(totalContents)
		avgLikesPerContent = float64(totalLikes) / float64(totalContents)
	}

	result := &dto.ContentOverviewResponse{
		TotalContents:      totalContents,
		PublishedContents:  publishedContents,
		DraftContents:      draftContents,
		ArchivedContents:   archivedContents,
		TotalViews:         totalViews,
		TotalLikes:         totalLikes,
		TotalComments:      totalComments,
		TotalFavorites:     totalFavorites,
		AvgViewsPerContent: avgViewsPerContent,
		AvgLikesPerContent: avgLikesPerContent,
	}

	// 缓存结果（10分钟）
	s.redis.Set(ctx, cacheKey, result, 10*time.Minute)

	log.Info().
		Int64("total_contents", totalContents).
		Int64("total_views", totalViews).
		Int64("total_likes", totalLikes).
		Msg("获取总体统计概览成功")

	return result, nil
}

// GetContentTypeStats 获取指定内容类型统计
func (s *statsAnalysisService) GetContentTypeStats(ctx context.Context, contentType string) (*dto.ContentTypeStatsResponse, *errors.Errors) {
	log.Info().
		Str("content_type", contentType).
		Msg("开始获取内容类型统计")

	// 参数验证
	if contentType == "" {
		return nil, errors.NewValidationError("内容类型不能为空")
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("stats:content_type:%s", contentType)
	var cachedResult dto.ContentTypeStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Str("content_type", contentType).
			Msg("从缓存获取内容类型统计成功")
		return &cachedResult, nil
	}

	var result *dto.ContentTypeStatsResponse

	switch contentType {
	case "video":
		videoStats, err := s.videoClient.GetOverallStats()
		if err != nil {
			log.Error().
				Err(err).
				Str("content_type", contentType).
				Msg("获取视频统计失败")
			return nil, errors.NewInternalError("获取视频统计失败")
		}

		// 获取交互统计
		interactionStats, err := s.interactionClient.GetOverallInteractionStats()
		if err != nil {
			log.Warn().
				Err(err).
				Msg("获取交互统计失败，使用默认值")
			interactionStats = &types.OverallInteractionStats{}
		}

		result = &dto.ContentTypeStatsResponse{
			ContentType:    contentType,
			TotalCount:     videoStats.TotalCount,
			PublishedCount: videoStats.PublishedCount,
			DraftCount:     videoStats.DraftCount,
			ArchivedCount:  videoStats.ArchivedCount,
			DeletedCount:   videoStats.DeletedCount,
			TotalViews:     videoStats.TotalViews,
			TotalLikes:     interactionStats.TotalLikes,
			TotalComments:  videoStats.TotalComments,
			TotalFavorites: interactionStats.TotalFavorites,
			AvgViews:       float64(videoStats.TotalViews) / float64(videoStats.TotalCount),
			AvgLikes:       float64(interactionStats.TotalLikes) / float64(videoStats.TotalCount),
		}

	case "novel":
		if s.novelClient == nil {
			return nil, errors.NewServiceUnavailableError("小说服务不可用")
		}
		// 预留小说服务统计
		return nil, errors.NewNotImplementedError("小说服务统计暂未实现")

	case "music":
		if s.musicClient == nil {
			return nil, errors.NewServiceUnavailableError("音乐服务不可用")
		}
		// 预留音乐服务统计
		return nil, errors.NewNotImplementedError("音乐服务统计暂未实现")

	default:
		return nil, errors.NewValidationError("不支持的内容类型")
	}

	// 缓存结果（10分钟）
	s.redis.Set(ctx, cacheKey, result, 10*time.Minute)

	log.Info().
		Str("content_type", contentType).
		Int64("total_count", result.TotalCount).
		Msg("获取内容类型统计成功")

	return result, nil
}

// GetAllContentTypeStats 获取所有内容类型统计
func (s *statsAnalysisService) GetAllContentTypeStats(ctx context.Context) ([]*dto.ContentTypeStatsResponse, *errors.Errors) {
	log.Info().Msg("开始获取所有内容类型统计")

	// 尝试从缓存获取
	cacheKey := "stats:all_content_types"
	var cachedResult []*dto.ContentTypeStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().Msg("从缓存获取所有内容类型统计成功")
		return cachedResult, nil
	}

	var results []*dto.ContentTypeStatsResponse
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 获取视频统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		videoStats, gErr := s.GetContentTypeStats(ctx, "video")
		if gErr == nil {
			mu.Lock()
			results = append(results, videoStats)
			mu.Unlock()
		}
	}()

	// 获取小说统计（如果启用）
	if s.novelClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留小说统计
			log.Debug().Msg("小说统计暂未实现")
		}()
	}

	// 获取音乐统计（如果启用）
	if s.musicClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留音乐统计
			log.Debug().Msg("音乐统计暂未实现")
		}()
	}

	wg.Wait()

	// 缓存结果（10分钟）
	s.redis.Set(ctx, cacheKey, results, 10*time.Minute)

	log.Info().
		Int("content_type_count", len(results)).
		Msg("获取所有内容类型统计成功")

	return results, nil
}

// GetUserStats 获取用户统计
func (s *statsAnalysisService) GetUserStats(ctx context.Context, userKSUID string) (*dto.UserInteractionStatsResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Msg("开始获取用户统计")

	// 参数验证
	if userKSUID == "" {
		return nil, errors.NewValidationError("用户KSUID不能为空")
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("stats:user:%s", userKSUID)
	var cachedResult dto.UserInteractionStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Str("user_ksuid", userKSUID).
			Msg("从缓存获取用户统计成功")
		return &cachedResult, nil
	}

	// 从交互服务获取用户统计
	userStats, err := s.interactionClient.GetUserInteractionStats(userKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取用户交互统计失败")
		return nil, errors.NewInternalError("获取用户统计失败")
	}

	result := dto.FromUserInteractionStats(userStats)

	// 缓存结果（5分钟）
	s.redis.Set(ctx, cacheKey, result, 5*time.Minute)

	log.Info().
		Str("user_ksuid", userKSUID).
		Int64("total_likes_given", result.TotalLikesGiven).
		Int64("total_likes_received", result.TotalLikesReceived).
		Msg("获取用户统计成功")

	return result, nil
}

// GetActiveUsers 获取活跃用户列表
func (s *statsAnalysisService) GetActiveUsers(ctx context.Context, period string, limit int) (*dto.ActiveUserListResponse, *errors.Errors) {
	log.Info().
		Str("period", period).
		Int("limit", limit).
		Msg("开始获取活跃用户列表")

	// 参数验证
	if period == "" {
		period = "weekly"
	}
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("stats:active_users:%s:%d", period, limit)
	var cachedResult dto.ActiveUserListResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Str("period", period).
			Int("limit", limit).
			Msg("从缓存获取活跃用户列表成功")
		return &cachedResult, nil
	}

	// 这里需要实现活跃用户计算逻辑
	// 由于需要跨服务聚合数据，暂时返回空列表
	result := &dto.ActiveUserListResponse{
		Users:  []*dto.ActiveUserResponse{},
		Period: period,
		Limit:  limit,
	}

	// 缓存结果（30分钟）
	s.redis.Set(ctx, cacheKey, result, 30*time.Minute)

	log.Info().
		Str("period", period).
		Int("limit", limit).
		Int("user_count", len(result.Users)).
		Msg("获取活跃用户列表成功")

	return result, nil
}

// GetInteractionStats 获取内容交互统计
func (s *statsAnalysisService) GetInteractionStats(ctx context.Context, contentKSUID string) (*dto.InteractionStatsResponse, *errors.Errors) {
	log.Info().
		Str("content_ksuid", contentKSUID).
		Msg("开始获取内容交互统计")

	// 参数验证
	if contentKSUID == "" {
		return nil, errors.NewValidationError("内容KSUID不能为空")
	}

	// 从交互服务获取统计
	interactionStats, err := s.interactionClient.GetContentInteractionStats(contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容交互统计失败")
		return nil, errors.NewInternalError("获取内容交互统计失败")
	}

	result := dto.FromInteractionStats(interactionStats)

	log.Info().
		Str("content_ksuid", contentKSUID).
		Int64("like_count", result.LikeCount).
		Int64("favorite_count", result.FavoriteCount).
		Msg("获取内容交互统计成功")

	return result, nil
}

// GetOverallInteractionStats 获取总体交互统计
func (s *statsAnalysisService) GetOverallInteractionStats(ctx context.Context) (*dto.OverallInteractionStatsResponse, *errors.Errors) {
	log.Info().Msg("开始获取总体交互统计")

	// 尝试从缓存获取
	cacheKey := "stats:overall_interaction"
	var cachedResult dto.OverallInteractionStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().Msg("从缓存获取总体交互统计成功")
		return &cachedResult, nil
	}

	// 从交互服务获取统计
	overallStats, err := s.interactionClient.GetOverallInteractionStats()
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取总体交互统计失败")
		return nil, errors.NewInternalError("获取总体交互统计失败")
	}

	result := dto.FromOverallInteractionStats(overallStats)

	// 缓存结果（10分钟）
	s.redis.Set(ctx, cacheKey, result, 10*time.Minute)

	log.Info().
		Int64("total_likes", result.TotalLikes).
		Int64("total_favorites", result.TotalFavorites).
		Msg("获取总体交互统计成功")

	return result, nil
}

// GetContentTrends 获取内容趋势分析
func (s *statsAnalysisService) GetContentTrends(ctx context.Context, period string, contentType string) (*dto.ContentTrendsStatsResponse, *errors.Errors) {
	log.Info().
		Str("period", period).
		Str("content_type", contentType).
		Msg("开始获取内容趋势分析")

	// 参数验证
	if period == "" {
		period = "weekly"
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("stats:trends:%s:%s", period, contentType)
	var cachedResult dto.ContentTrendsStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Str("period", period).
			Str("content_type", contentType).
			Msg("从缓存获取内容趋势成功")
		return &cachedResult, nil
	}

	// 这里需要实现趋势分析逻辑
	// 由于需要复杂的时间序列分析，暂时返回空数据
	result := &dto.ContentTrendsStatsResponse{
		Period:        period,
		ContentCounts: []dto.ContentTrendData{},
		ViewCounts:    []dto.ContentTrendData{},
		LikeCounts:    []dto.ContentTrendData{},
		CommentCounts: []dto.ContentTrendData{},
	}

	// 缓存结果（1小时）
	s.redis.Set(ctx, cacheKey, result, 1*time.Hour)

	log.Info().
		Str("period", period).
		Str("content_type", contentType).
		Msg("获取内容趋势分析成功")

	return result, nil
}

// GetPopularContents 获取热门内容
func (s *statsAnalysisService) GetPopularContents(ctx context.Context, period string, limit int) (*dto.PopularContentListResponse, *errors.Errors) {
	log.Info().
		Str("period", period).
		Int("limit", limit).
		Msg("开始获取热门内容")

	// 参数验证
	if period == "" {
		period = "weekly"
	}
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("stats:popular_contents:%s:%d", period, limit)
	var cachedResult dto.PopularContentListResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Str("period", period).
			Int("limit", limit).
			Msg("从缓存获取热门内容成功")
		return &cachedResult, nil
	}

	// 这里需要实现热门内容计算逻辑
	// 需要综合考虑观看数、点赞数、评论数等指标
	result := &dto.PopularContentListResponse{
		Contents: []*dto.PopularContentResponse{},
		Period:   period,
		Limit:    limit,
	}

	// 缓存结果（30分钟）
	s.redis.Set(ctx, cacheKey, result, 30*time.Minute)

	log.Info().
		Str("period", period).
		Int("limit", limit).
		Int("content_count", len(result.Contents)).
		Msg("获取热门内容成功")

	return result, nil
}

// GetCategoryStats 获取分类统计
func (s *statsAnalysisService) GetCategoryStats(ctx context.Context) ([]*dto.CategoryStatsResponse, *errors.Errors) {
	log.Info().Msg("开始获取分类统计")

	// 尝试从缓存获取
	cacheKey := "stats:categories"
	var cachedResult []*dto.CategoryStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().Msg("从缓存获取分类统计成功")
		return cachedResult, nil
	}

	// 这里需要实现分类统计逻辑
	// 需要从各个内容服务获取分类数据并聚合
	result := []*dto.CategoryStatsResponse{}

	// 缓存结果（1小时）
	s.redis.Set(ctx, cacheKey, result, 1*time.Hour)

	log.Info().
		Int("category_count", len(result)).
		Msg("获取分类统计成功")

	return result, nil
}

// GetTagStats 获取标签统计
func (s *statsAnalysisService) GetTagStats(ctx context.Context, limit int) ([]*dto.TagStatsResponse, *errors.Errors) {
	log.Info().
		Int("limit", limit).
		Msg("开始获取标签统计")

	// 参数验证
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("stats:tags:%d", limit)
	var cachedResult []*dto.TagStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Int("limit", limit).
			Msg("从缓存获取标签统计成功")
		return cachedResult, nil
	}

	// 这里需要实现标签统计逻辑
	// 需要从各个内容服务获取标签数据并聚合
	result := []*dto.TagStatsResponse{}

	// 缓存结果（1小时）
	s.redis.Set(ctx, cacheKey, result, 1*time.Hour)

	log.Info().
		Int("limit", limit).
		Int("tag_count", len(result)).
		Msg("获取标签统计成功")

	return result, nil
}

// GetDashboardStats 获取仪表板统计
func (s *statsAnalysisService) GetDashboardStats(ctx context.Context) (*dto.DashboardStatsResponse, *errors.Errors) {
	log.Info().Msg("开始获取仪表板统计")

	// 尝试从缓存获取
	cacheKey := "stats:dashboard"
	var cachedResult dto.DashboardStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().Msg("从缓存获取仪表板统计成功")
		return &cachedResult, nil
	}

	// 并发获取各种统计数据
	var wg sync.WaitGroup
	var mu sync.Mutex
	var overview *dto.ContentOverviewResponse
	var contentTypes []*dto.ContentTypeStatsResponse
	var recentTrends *dto.ContentTrendsStatsResponse
	var popularContents *dto.PopularContentListResponse
	var activeUsers *dto.ActiveUserListResponse
	var systemStats *dto.SystemStatsResponse

	// 获取概览统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		if result, gErr := s.GetOverviewStats(ctx); gErr == nil {
			mu.Lock()
			overview = result
			mu.Unlock()
		}
	}()

	// 获取内容类型统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		if result, gErr := s.GetAllContentTypeStats(ctx); gErr == nil {
			mu.Lock()
			contentTypes = result
			mu.Unlock()
		}
	}()

	// 获取最近趋势
	wg.Add(1)
	go func() {
		defer wg.Done()
		if result, gErr := s.GetContentTrends(ctx, "weekly", ""); gErr == nil {
			mu.Lock()
			recentTrends = result
			mu.Unlock()
		}
	}()

	// 获取热门内容
	wg.Add(1)
	go func() {
		defer wg.Done()
		if result, gErr := s.GetPopularContents(ctx, "weekly", 10); gErr == nil {
			mu.Lock()
			popularContents = result
			mu.Unlock()
		}
	}()

	// 获取活跃用户
	wg.Add(1)
	go func() {
		defer wg.Done()
		if result, gErr := s.GetActiveUsers(ctx, "weekly", 10); gErr == nil {
			mu.Lock()
			activeUsers = result
			mu.Unlock()
		}
	}()

	// 获取系统统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 这里需要实现系统统计逻辑
		mu.Lock()
		systemStats = &dto.SystemStatsResponse{
			ServiceHealth:   make(map[string]bool),
			LastHealthCheck: time.Now(),
		}
		mu.Unlock()
	}()

	wg.Wait()

	result := &dto.DashboardStatsResponse{
		Overview:        overview,
		ContentTypes:    contentTypes,
		RecentTrends:    recentTrends,
		PopularContents: popularContents,
		ActiveUsers:     activeUsers,
		SystemStats:     systemStats,
		LastUpdated:     time.Now(),
	}

	// 缓存结果（5分钟）
	s.redis.Set(ctx, cacheKey, result, 5*time.Minute)

	log.Info().Msg("获取仪表板统计成功")

	return result, nil
}

// GenerateReport 生成报告
func (s *statsAnalysisService) GenerateReport(ctx context.Context, request *dto.ReportRequest) (*dto.ReportResponse, *errors.Errors) {
	log.Info().
		Interface("request", request).
		Msg("开始生成报告")

	// 参数验证
	if gErr := s.validator.ValidateReportRequest(request); gErr != nil {
		return nil, gErr
	}

	// 生成报告ID
	reportID := fmt.Sprintf("report_%d", time.Now().Unix())

	// 这里需要实现报告生成逻辑
	// 根据报告类型和参数生成相应的报告数据
	result := &dto.ReportResponse{
		ReportID:   reportID,
		ReportType: request.ReportType,
		Status:     "completed",
		Data:       map[string]interface{}{},
		CreatedAt:  time.Now(),
	}

	log.Info().
		Str("report_id", reportID).
		Str("report_type", request.ReportType).
		Msg("生成报告成功")

	return result, nil
}

// GetReport 获取报告
func (s *statsAnalysisService) GetReport(ctx context.Context, reportID string) (*dto.ReportResponse, *errors.Errors) {
	log.Info().
		Str("report_id", reportID).
		Msg("开始获取报告")

	// 参数验证
	if reportID == "" {
		return nil, errors.NewValidationError("报告ID不能为空")
	}

	// 这里需要实现报告获取逻辑
	// 从存储中获取报告数据
	result := &dto.ReportResponse{
		ReportID:   reportID,
		ReportType: "unknown",
		Status:     "not_found",
		CreatedAt:  time.Now(),
	}

	log.Info().
		Str("report_id", reportID).
		Str("status", result.Status).
		Msg("获取报告完成")

	return result, nil
}

// HealthCheck 健康检查
func (s *statsAnalysisService) HealthCheck(ctx context.Context) *errors.Errors {
	log.Debug().Msg("开始统计服务健康检查")

	var healthErrors []error

	// 检查视频服务
	if err := s.videoClient.HealthCheck(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("video service unhealthy: %w", err))
	}

	// 检查交互服务
	if err := s.interactionClient.HealthCheck(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("interaction service unhealthy: %w", err))
	}

	// 检查小说服务（如果启用）
	if s.novelClient != nil {
		// 预留小说服务健康检查
		log.Debug().Msg("小说服务健康检查暂未实现")
	}

	// 检查音乐服务（如果启用）
	if s.musicClient != nil {
		// 预留音乐服务健康检查
		log.Debug().Msg("音乐服务健康检查暂未实现")
	}

	// 检查Redis连接
	if err := s.redis.Ping(ctx).Err(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("redis unhealthy: %w", err))
	}

	if len(healthErrors) > 0 {
		log.Error().
			Interface("errors", healthErrors).
			Msg("统计服务健康检查失败")
		return errors.NewServiceUnavailableError("部分服务不健康")
	}

	log.Debug().Msg("统计服务健康检查通过")
	return nil
}
