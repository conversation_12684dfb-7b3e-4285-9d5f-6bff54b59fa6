package handler

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
	"pxpat-backend/pkg/response"
)

// ComplaintHandler 投诉处理器
type ComplaintHandler struct {
	complaintService *service.ComplaintService
}

// NewComplaintHandler 创建投诉处理器实例
func NewComplaintHandler(complaintService *service.ComplaintService) *ComplaintHandler {
	return &ComplaintHandler{
		complaintService: complaintService,
	}
}

// CreateComplaint 创建投诉
// @Summary 创建投诉
// @Description 用户创建投诉
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Param request body dto.CreateComplaintRequest true "创建投诉请求"
// @Success 200 {object} response.Response{data=dto.ComplaintResponse} "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/complaints [post]
func (h *ComplaintHandler) CreateComplaint(c *gin.Context) {
	var req dto.CreateComplaintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("绑定创建投诉请求参数失败")
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数格式错误"))
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusUnauthorized, errors.New("用户未登录"))
		return
	}

	result, gErr := h.complaintService.CreateComplaint(c.Request.Context(), userKSUID.(string), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Msg("创建投诉失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// GetComplaint 获取投诉详情
// @Summary 获取投诉详情
// @Description 获取指定投诉的详细信息
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Param complaint_ksuid path string true "投诉KSUID"
// @Success 200 {object} response.Response{data=dto.ComplaintResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "投诉不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/complaints/{complaint_ksuid} [get]
func (h *ComplaintHandler) GetComplaint(c *gin.Context) {
	complaintKSUID := c.Param("complaint_ksuid")
	if complaintKSUID == "" {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	result, gErr := h.complaintService.GetComplaint(c.Request.Context(), userKSUID.(string), complaintKSUID)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Str("complaint_ksuid", complaintKSUID).
			Msg("获取投诉详情失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// UpdateComplaint 更新投诉
// @Summary 更新投诉
// @Description 更新投诉信息
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Param complaint_ksuid path string true "投诉KSUID"
// @Param request body dto.UpdateComplaintRequest true "更新投诉请求"
// @Success 200 {object} response.Response{data=dto.ComplaintResponse} "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "投诉不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/complaints/{complaint_ksuid} [put]
func (h *ComplaintHandler) UpdateComplaint(c *gin.Context) {
	complaintKSUID := c.Param("complaint_ksuid")
	if complaintKSUID == "" {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("投诉KSUID不能为空"))
		return
	}

	var req dto.UpdateComplaintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("绑定更新投诉请求参数失败")
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数格式错误"))
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusUnauthorized, errors.New("用户未登录"))
		return
	}

	result, gErr := h.complaintService.UpdateComplaint(c.Request.Context(), userKSUID.(string), complaintKSUID, &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Str("complaint_ksuid", complaintKSUID).
			Msg("更新投诉失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// DeleteComplaint 删除投诉
// @Summary 删除投诉
// @Description 删除投诉
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Param complaint_ksuid path string true "投诉KSUID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "投诉不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/complaints/{complaint_ksuid} [delete]
func (h *ComplaintHandler) DeleteComplaint(c *gin.Context) {
	complaintKSUID := c.Param("complaint_ksuid")
	if complaintKSUID == "" {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	gErr := h.complaintService.DeleteComplaint(c.Request.Context(), userKSUID.(string), complaintKSUID)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Str("complaint_ksuid", complaintKSUID).
			Msg("删除投诉失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, nil)
}

// GetMyComplaints 获取我的投诉列表
// @Summary 获取我的投诉列表
// @Description 获取当前用户的投诉列表
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param type query string false "投诉类型"
// @Param status query string false "投诉状态"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} response.Response{data=dto.ComplaintListResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/complaints/my [get]
func (h *ComplaintHandler) GetMyComplaints(c *gin.Context) {
	var req dto.GetMyComplaintsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().Err(err).Msg("绑定获取我的投诉列表请求参数失败")
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	result, gErr := h.complaintService.GetMyComplaints(c.Request.Context(), userKSUID.(string), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Msg("获取我的投诉列表失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// GetComplaintsAgainstMe 获取针对我的投诉列表
// @Summary 获取针对我的投诉列表
// @Description 获取针对当前用户内容的投诉列表
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param status query string false "投诉状态"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} response.Response{data=dto.ComplaintListResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/complaints/against-me [get]
func (h *ComplaintHandler) GetComplaintsAgainstMe(c *gin.Context) {
	var req dto.GetComplaintsAgainstMeRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().Err(err).Msg("绑定获取针对我的投诉列表请求参数失败")
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	result, gErr := h.complaintService.GetComplaintsAgainstMe(c.Request.Context(), userKSUID.(string), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Msg("获取针对我的投诉列表失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// GetComplaintStats 获取投诉统计信息
// @Summary 获取投诉统计信息
// @Description 获取投诉相关的统计数据
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=dto.ComplaintStatsResponse} "获取成功"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/complaints/stats [get]
func (h *ComplaintHandler) GetComplaintStats(c *gin.Context) {
	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	result, gErr := h.complaintService.GetComplaintStats(c.Request.Context(), userKSUID.(string))
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Msg("获取投诉统计信息失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// GetViolationCategories 获取违规类别
// @Summary 获取违规类别
// @Description 获取违规类别树形结构
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]dto.ViolationCategoryResponse} "获取成功"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/complaints/violation-categories [get]
func (h *ComplaintHandler) GetViolationCategories(c *gin.Context) {
	result, gErr := h.complaintService.GetViolationCategories(c.Request.Context())
	if gErr != nil {
		log.Error().
			Err(gErr).
			Msg("获取违规类别失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}

// ProcessComplaint 处理投诉（管理员接口）
// @Summary 处理投诉
// @Description 管理员处理投诉
// @Tags 投诉管理
// @Accept json
// @Produce json
// @Param complaint_ksuid path string true "投诉KSUID"
// @Param request body dto.ProcessComplaintRequest true "处理投诉请求"
// @Success 200 {object} response.Response{data=dto.ComplaintResponse} "处理成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "投诉不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/admin/complaints/{complaint_ksuid}/process [post]
func (h *ComplaintHandler) ProcessComplaint(c *gin.Context) {
	complaintKSUID := c.Param("complaint_ksuid")
	if complaintKSUID == "" {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	var req dto.ProcessComplaintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("绑定处理投诉请求参数失败")
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	// 从上下文获取处理人KSUID（管理员）
	processorKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.ErrorWithCode(c, http.StatusBadRequest, errors.New("参数错误"))
		return
	}

	result, gErr := h.complaintService.ProcessComplaint(c.Request.Context(), processorKSUID.(string), complaintKSUID, &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("processor_ksuid", processorKSUID.(string)).
			Str("complaint_ksuid", complaintKSUID).
			Msg("处理投诉失败")
		response.Error(c, gErr)
		return
	}

	response.Success(c, result)
}
