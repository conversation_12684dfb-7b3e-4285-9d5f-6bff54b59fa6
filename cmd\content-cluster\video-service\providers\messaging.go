package providers

import (
	"pxpat-backend/internal/content-cluster/video-service/client"
	intraService "pxpat-backend/internal/content-cluster/video-service/intra/service"
	"pxpat-backend/internal/content-cluster/video-service/messaging"
	"pxpat-backend/internal/content-cluster/video-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/video-service/repository"
	"pxpat-backend/internal/content-cluster/video-service/types"
	pkgMessaging "pxpat-backend/pkg/messaging"

	"github.com/rs/zerolog/log"
)

// ProvideMQPublisher 提供MQ发布器
func ProvideMQPublisher(cfg *types.Config) *publisher.Publisher {
	var mqPublisher *publisher.Publisher
	if cfg.RabbitMQ.URL != "" {
		var err error
		mqPublisher, err = publisher.NewPublisher(cfg.RabbitMQ.URL)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ publisher")
			log.Info().Msg("Continuing without MQ message publishing...")
		} else {
			log.Info().Msg("MQ publisher initialized successfully")
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ publisher initialization")
	}
	return mqPublisher
}

// ProvideMQMultiConsumer 提供MQ多消费者
func ProvideMQMultiConsumer(
	cfg *types.Config,
	contentRepo *repository.ContentRepository,
	auditServiceClient client.AuditServiceClient,
	contentService *intraService.ContentService,
) *pkgMessaging.MultiConsumer {
	var multiConsumer *pkgMessaging.MultiConsumer
	if cfg.RabbitMQ.URL != "" {
		var err error
		multiConsumer, err = messaging.CreateContentServiceMultiConsumer(
			cfg.RabbitMQ.URL,
			contentRepo,
			auditServiceClient,
			contentService,
		)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ multi consumer")
			log.Info().Msg("Continuing without MQ message consumption...")
		} else {
			log.Info().Msg("MQ multi consumer initialized successfully")
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ consumer initialization")
	}
	return multiConsumer
}
