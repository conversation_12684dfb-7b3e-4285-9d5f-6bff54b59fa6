package handler

import (
	"pxpat-backend/internal/content-cluster/audit-service/client"
	externalService "pxpat-backend/internal/content-cluster/audit-service/external/service"

	"github.com/rs/zerolog/log"
)

// ProvideAuditTasksHandler 提供外部审核任务处理器
func ProvideAuditTasksHandler(
	externalAuditService *externalService.ExternalAuditTasksService,
	financeServiceClient client.FinanceServiceClient,
) *AuditTasksHandler {
	externalAuditHandler := NewAuditTasksHandler(externalAuditService, financeServiceClient)
	log.Info().Msg("External audit tasks handler initialized successfully")
	return externalAuditHandler
}
