# 内容管理服务部署指南

## 概述

本文档详细介绍了内容管理服务的部署流程，包括环境准备、配置说明、部署步骤和运维管理。

## 系统要求

### 硬件要求

**最低配置**:
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 20GB 可用空间
- 网络: 100Mbps

**推荐配置**:
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 100GB SSD
- 网络: 1Gbps

### 软件要求

- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+ / RHEL 8+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Go**: 1.21+ (开发环境)

## 环境准备

### 1. 安装Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. 安装Docker Compose

```bash
# 下载Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 3. 系统优化

```bash
# 调整系统参数
echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf
echo 'fs.file-max=65536' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 调整ulimit
echo '* soft nofile 65536' | sudo tee -a /etc/security/limits.conf
echo '* hard nofile 65536' | sudo tee -a /etc/security/limits.conf
```

## 配置说明

### 1. 环境变量配置

创建 `.env` 文件：

```bash
# 服务配置
GO_ENV=production
GIN_MODE=release

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_USER=content_user
DB_PASSWORD=your-secure-password
DB_NAME=content_management
DB_SSL_MODE=require

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_TOKEN_DURATION=1h
JWT_REFRESH_DURATION=24h

# 外部服务配置
VIDEO_SERVICE_URL=http://video-service:12001
INTERACTION_SERVICE_URL=http://interaction-service:12002

# Consul配置
CONSUL_HOST=consul
CONSUL_PORT=8500
CONSUL_DATACENTER=dc1

# 监控配置
PROMETHEUS_ENABLED=true
JAEGER_ENABLED=true

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
```

### 2. 数据库配置

确保PostgreSQL数据库已创建并配置正确的权限：

```sql
-- 创建数据库和用户
CREATE DATABASE content_management;
CREATE USER content_user WITH PASSWORD 'your-secure-password';
GRANT ALL PRIVILEGES ON DATABASE content_management TO content_user;

-- 创建必要的扩展
\c content_management;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
```

### 3. SSL/TLS配置

生产环境建议启用HTTPS：

```bash
# 生成SSL证书（使用Let's Encrypt）
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 或使用自签名证书（仅用于测试）
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout server.key -out server.crt
```

## 部署步骤

### 1. 克隆代码

```bash
git clone https://github.com/your-org/pxpat-backend.git
cd pxpat-backend
```

### 2. 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env

# 设置文件权限
chmod 600 .env
```

### 3. 构建和部署

#### 生产环境部署

```bash
# 进入Docker目录
cd docker

# 使用部署脚本
chmod +x deploy.sh
./deploy.sh --env production --backup

# 或手动部署
docker-compose up -d
```

#### 开发环境部署

```bash
# 使用开发环境配置
./deploy.sh --env dev

# 或手动启动开发环境
docker-compose -f docker-compose.dev.yml up -d
```

### 4. 验证部署

```bash
# 检查服务状态
docker-compose ps

# 检查服务健康
curl http://localhost:12010/api/v1/health

# 查看日志
docker-compose logs -f content-management-service
```

## 监控部署

### 1. 启动监控服务

```bash
# 使用监控设置脚本
chmod +x scripts/monitoring/setup-monitoring.sh
./scripts/monitoring/setup-monitoring.sh

# 或手动启动
docker-compose -f docker-compose.monitoring.yml up -d
```

### 2. 配置Grafana

1. 访问 http://localhost:3000
2. 使用 admin/admin123 登录
3. 修改默认密码
4. 导入预配置的仪表板

### 3. 配置告警

1. 访问 http://localhost:9093 (Alertmanager)
2. 配置Slack Webhook URL
3. 测试告警通知

## 数据备份

### 1. 数据库备份

```bash
# 创建备份脚本
cat > backup-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份PostgreSQL
docker exec content-postgres pg_dump -U content_user content_management > $BACKUP_DIR/postgres_backup.sql

# 备份Redis
docker exec content-redis redis-cli --rdb - > $BACKUP_DIR/redis_backup.rdb

# 压缩备份文件
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

echo "备份完成: $BACKUP_DIR.tar.gz"
EOF

chmod +x backup-db.sh
```

### 2. 自动备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backup-db.sh
```

## 扩容和负载均衡

### 1. 水平扩容

```bash
# 扩容服务实例
docker-compose up -d --scale content-management-service=3

# 使用Nginx负载均衡
cat > nginx.conf << 'EOF'
upstream content_service {
    server localhost:12010;
    server localhost:12011;
    server localhost:12012;
}

server {
    listen 80;
    location / {
        proxy_pass http://content_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF
```

### 2. 数据库读写分离

```yaml
# docker-compose.yml 添加只读副本
postgres-replica:
  image: postgres:15-alpine
  environment:
    - POSTGRES_DB=content_management
    - POSTGRES_USER=content_user
    - POSTGRES_PASSWORD=your-password
    - POSTGRES_MASTER_SERVICE=postgres
  command: |
    bash -c "
    until pg_basebackup -h postgres -D /var/lib/postgresql/data -U replicator -v -P -W; do
      echo 'Waiting for master to connect...'
      sleep 1s
    done
    echo 'Backup done, starting replica...'
    chmod 0700 /var/lib/postgresql/data
    postgres
    "
```

## 故障排查

### 1. 常见问题

**服务无法启动**:
```bash
# 检查端口占用
netstat -tlnp | grep :12010

# 检查Docker日志
docker logs content-management-service

# 检查配置文件
docker exec content-management-service cat /app/config/config.yaml
```

**数据库连接失败**:
```bash
# 检查数据库状态
docker exec content-postgres pg_isready

# 测试连接
docker exec content-management-service pg_isready -h postgres -p 5432
```

**Redis连接失败**:
```bash
# 检查Redis状态
docker exec content-redis redis-cli ping

# 检查网络连接
docker exec content-management-service redis-cli -h redis ping
```

### 2. 性能调优

**数据库优化**:
```sql
-- 分析慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

-- 创建索引
CREATE INDEX CONCURRENTLY idx_contents_user_status 
ON contents(user_ksuid, status);
```

**缓存优化**:
```bash
# 监控Redis内存使用
docker exec content-redis redis-cli info memory

# 调整缓存策略
docker exec content-redis redis-cli config set maxmemory-policy allkeys-lru
```

## 安全配置

### 1. 网络安全

```bash
# 配置防火墙
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 5432/tcp  # 禁止外部访问数据库
sudo ufw enable
```

### 2. 容器安全

```yaml
# docker-compose.yml 安全配置
services:
  content-management-service:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    user: "1001:1001"
```

### 3. 密钥管理

```bash
# 使用Docker Secrets
echo "your-secret-key" | docker secret create jwt_secret -

# 在compose文件中引用
secrets:
  - jwt_secret
```

## 更新和回滚

### 1. 滚动更新

```bash
# 拉取最新镜像
docker-compose pull

# 滚动更新
docker-compose up -d --no-deps content-management-service
```

### 2. 版本回滚

```bash
# 回滚到指定版本
docker-compose down
docker-compose up -d
```

## 联系支持

如遇到部署问题，请联系：
- 技术支持: <EMAIL>
- 文档更新: <EMAIL>
- 紧急联系: +86-xxx-xxxx-xxxx
