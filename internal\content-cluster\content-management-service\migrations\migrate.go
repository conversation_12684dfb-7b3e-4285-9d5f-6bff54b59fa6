package migrations

import (
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/content-cluster/content-management-service/model"
)

// Migrator 数据库迁移器
type Migrator struct {
	db *gorm.DB
}

// NewMigrator 创建新的迁移器
func NewMigrator(db *gorm.DB) *Migrator {
	return &Migrator{db: db}
}

// AutoMigrate 自动迁移所有模型
func (m *Migrator) AutoMigrate() error {
	log.Info().Msg("开始数据库自动迁移")

	// 按依赖关系排序迁移
	models := []interface{}{
		&model.OperationLog{},
		&model.ContentCache{},
		&model.ManagementConfig{},
		&model.ConfigChangeLog{},
	}

	for _, model := range models {
		if err := m.db.AutoMigrate(model); err != nil {
			log.Error().
				Err(err).
				Str("model", fmt.Sprintf("%T", model)).
				Msg("模型迁移失败")
			return fmt.Errorf("failed to migrate model %T: %w", model, err)
		}

		log.Debug().
			Str("model", fmt.Sprintf("%T", model)).
			Msg("模型迁移成功")
	}

	// 创建索引
	if err := m.createIndexes(); err != nil {
		log.Error().Err(err).Msg("创建索引失败")
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	// 初始化默认数据
	if err := m.initializeDefaultData(); err != nil {
		log.Error().Err(err).Msg("初始化默认数据失败")
		return fmt.Errorf("failed to initialize default data: %w", err)
	}

	log.Info().Msg("数据库自动迁移完成")
	return nil
}

// createIndexes 创建索引
func (m *Migrator) createIndexes() error {
	log.Info().Msg("开始创建数据库索引")

	indexes := []struct {
		table string
		sql   string
		desc  string
	}{
		{
			table: "operation_logs",
			sql:   "CREATE INDEX IF NOT EXISTS idx_operation_logs_operator_time ON operation_logs(operator_ksuid, created_at DESC)",
			desc:  "操作日志操作者和时间复合索引",
		},
		{
			table: "operation_logs",
			sql:   "CREATE INDEX IF NOT EXISTS idx_operation_logs_target_time ON operation_logs(target_ksuid, created_at DESC)",
			desc:  "操作日志目标和时间复合索引",
		},
		{
			table: "operation_logs",
			sql:   "CREATE INDEX IF NOT EXISTS idx_operation_logs_type_time ON operation_logs(operation_type, created_at DESC)",
			desc:  "操作日志类型和时间复合索引",
		},
		{
			table: "content_caches",
			sql:   "CREATE INDEX IF NOT EXISTS idx_content_caches_type_expires ON content_caches(content_type, expires_at)",
			desc:  "内容缓存类型和过期时间复合索引",
		},
		{
			table: "content_caches",
			sql:   "CREATE INDEX IF NOT EXISTS idx_content_caches_expires_at ON content_caches(expires_at) WHERE expires_at < NOW()",
			desc:  "内容缓存过期时间部分索引",
		},
		{
			table: "management_configs",
			sql:   "CREATE INDEX IF NOT EXISTS idx_management_configs_category_active ON management_configs(category, is_active)",
			desc:  "管理配置分类和状态复合索引",
		},
		{
			table: "config_change_logs",
			sql:   "CREATE INDEX IF NOT EXISTS idx_config_change_logs_key_time ON config_change_logs(config_key, created_at DESC)",
			desc:  "配置变更日志键和时间复合索引",
		},
		{
			table: "config_change_logs",
			sql:   "CREATE INDEX IF NOT EXISTS idx_config_change_logs_changed_by ON config_change_logs(changed_by, created_at DESC)",
			desc:  "配置变更日志变更人索引",
		},
	}

	for _, index := range indexes {
		if err := m.db.Exec(index.sql).Error; err != nil {
			log.Error().
				Err(err).
				Str("table", index.table).
				Str("description", index.desc).
				Msg("创建索引失败")
			return fmt.Errorf("failed to create index for %s: %w", index.table, err)
		}

		log.Debug().
			Str("table", index.table).
			Str("description", index.desc).
			Msg("索引创建成功")
	}

	log.Info().Msg("数据库索引创建完成")
	return nil
}

// initializeDefaultData 初始化默认数据
func (m *Migrator) initializeDefaultData() error {
	log.Info().Msg("开始初始化默认数据")

	// 初始化默认配置
	if err := m.initializeDefaultConfigs(); err != nil {
		return fmt.Errorf("failed to initialize default configs: %w", err)
	}

	log.Info().Msg("默认数据初始化完成")
	return nil
}

// initializeDefaultConfigs 初始化默认配置
func (m *Migrator) initializeDefaultConfigs() error {
	log.Info().Msg("开始初始化默认配置")

	// 检查是否已有配置数据
	var count int64
	if err := m.db.Model(&model.ManagementConfig{}).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to count existing configs: %w", err)
	}

	if count > 0 {
		log.Info().Int64("count", count).Msg("配置数据已存在，跳过初始化")
		return nil
	}

	// 批量插入默认配置
	defaultConfigs := model.DefaultConfigs
	if len(defaultConfigs) == 0 {
		log.Warn().Msg("没有默认配置需要初始化")
		return nil
	}

	// 分批插入，避免单次插入过多数据
	batchSize := 10
	for i := 0; i < len(defaultConfigs); i += batchSize {
		end := i + batchSize
		if end > len(defaultConfigs) {
			end = len(defaultConfigs)
		}

		batch := defaultConfigs[i:end]
		if err := m.db.Create(&batch).Error; err != nil {
			log.Error().
				Err(err).
				Int("batch_start", i).
				Int("batch_end", end).
				Msg("批量插入默认配置失败")
			return fmt.Errorf("failed to create default configs batch %d-%d: %w", i, end, err)
		}

		log.Debug().
			Int("batch_start", i).
			Int("batch_end", end).
			Msg("默认配置批次插入成功")
	}

	log.Info().
		Int("total", len(defaultConfigs)).
		Msg("默认配置初始化完成")

	return nil
}

// DropAllTables 删除所有表（危险操作，仅用于开发环境）
func (m *Migrator) DropAllTables() error {
	log.Warn().Msg("开始删除所有表")

	tables := []string{
		"config_change_logs",
		"management_configs",
		"content_caches",
		"operation_logs",
	}

	for _, table := range tables {
		if err := m.db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", table)).Error; err != nil {
			log.Error().
				Err(err).
				Str("table", table).
				Msg("删除表失败")
			return fmt.Errorf("failed to drop table %s: %w", table, err)
		}

		log.Debug().
			Str("table", table).
			Msg("表删除成功")
	}

	log.Warn().Msg("所有表删除完成")
	return nil
}

// CheckMigrationStatus 检查迁移状态
func (m *Migrator) CheckMigrationStatus() (*MigrationStatus, error) {
	status := &MigrationStatus{
		CheckTime: time.Now(),
		Tables:    make(map[string]TableStatus),
	}

	// 检查表是否存在
	tables := []string{
		"operation_logs",
		"content_caches",
		"management_configs",
		"config_change_logs",
	}

	for _, table := range tables {
		var exists bool
		err := m.db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", table).Scan(&exists).Error
		if err != nil {
			return nil, fmt.Errorf("failed to check table %s existence: %w", table, err)
		}

		tableStatus := TableStatus{
			Exists: exists,
		}

		if exists {
			// 获取表记录数
			var count int64
			if err := m.db.Table(table).Count(&count).Error; err != nil {
				log.Warn().
					Err(err).
					Str("table", table).
					Msg("获取表记录数失败")
			} else {
				tableStatus.RecordCount = count
			}
		}

		status.Tables[table] = tableStatus
	}

	// 检查整体状态
	allExists := true
	for _, tableStatus := range status.Tables {
		if !tableStatus.Exists {
			allExists = false
			break
		}
	}

	if allExists {
		status.Status = "completed"
	} else {
		status.Status = "incomplete"
	}

	return status, nil
}

// MigrationStatus 迁移状态
type MigrationStatus struct {
	Status    string                 `json:"status"` // completed, incomplete, error
	CheckTime time.Time              `json:"check_time"`
	Tables    map[string]TableStatus `json:"tables"`
}

// TableStatus 表状态
type TableStatus struct {
	Exists      bool  `json:"exists"`
	RecordCount int64 `json:"record_count"`
}

// CleanupExpiredCaches 清理过期缓存
func (m *Migrator) CleanupExpiredCaches() error {
	log.Info().Msg("开始清理过期缓存")

	result := m.db.Where("expires_at < ?", time.Now()).Delete(&model.ContentCache{})
	if result.Error != nil {
		log.Error().Err(result.Error).Msg("清理过期缓存失败")
		return fmt.Errorf("failed to cleanup expired caches: %w", result.Error)
	}

	log.Info().
		Int64("deleted_count", result.RowsAffected).
		Msg("过期缓存清理完成")

	return nil
}

// CleanupOldLogs 清理旧日志
func (m *Migrator) CleanupOldLogs(retentionDays int) error {
	log.Info().
		Int("retention_days", retentionDays).
		Msg("开始清理旧日志")

	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)

	result := m.db.Where("created_at < ?", cutoffTime).Delete(&model.OperationLog{})
	if result.Error != nil {
		log.Error().Err(result.Error).Msg("清理旧操作日志失败")
		return fmt.Errorf("failed to cleanup old operation logs: %w", result.Error)
	}

	log.Info().
		Int64("deleted_count", result.RowsAffected).
		Int("retention_days", retentionDays).
		Msg("旧操作日志清理完成")

	// 清理配置变更日志
	result = m.db.Where("created_at < ?", cutoffTime).Delete(&model.ConfigChangeLog{})
	if result.Error != nil {
		log.Error().Err(result.Error).Msg("清理旧配置变更日志失败")
		return fmt.Errorf("failed to cleanup old config change logs: %w", result.Error)
	}

	log.Info().
		Int64("deleted_count", result.RowsAffected).
		Int("retention_days", retentionDays).
		Msg("旧配置变更日志清理完成")

	return nil
}
