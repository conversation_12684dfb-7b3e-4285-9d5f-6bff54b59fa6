package dto

import (
	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/pkg/errors"
	"time"
)

// CreateRightsVerificationRequest 创建权益认证请求
type CreateRightsVerificationRequest struct {
	Type model.RightsType `json:"type" binding:"required"` // 权益类型

	// 代理信息
	IsAgent                bool            `json:"is_agent"`                 // 是否代理
	AgentType              model.AgentType `json:"agent_type"`               // 代理类型
	RightOwnerName         string          `json:"right_owner_name"`         // 权利人/代理人姓名
	AuthorizationStartDate string          `json:"authorization_start_date"` // 授权期限起始时间(YYYY-MM-DD)
	AuthorizationEndDate   string          `json:"authorization_end_date"`   // 授权期限结束时间(YYYY-MM-DD)

	// 著作权信息
	Copyrights []CreateCopyrightRequest `json:"copyrights"` // 著作权列表

	// 商标权信息
	Trademarks []CreateTrademarkRequest `json:"trademarks"` // 商标权列表

	// 人格权信息
	PersonalityRights []CreatePersonalityRightRequest `json:"personality_rights"` // 人格权列表
}

// CreateCopyrightRequest 创建著作权请求
type CreateCopyrightRequest struct {
	CopyrightType model.CopyrightType `json:"copyright_type" binding:"required"` // 著作类型
	WorkName      string              `json:"work_name" binding:"required"`      // 著作名称
	CountryCode   string              `json:"country_code" binding:"required"`   // 地区代码
	ValidFromDate string              `json:"valid_from_date"`                   // 期限起始时间(YYYY-MM-DD)
	ValidToDate   string              `json:"valid_to_date"`                     // 期限结束时间(YYYY-MM-DD)
}

// CreateTrademarkRequest 创建商标权请求
type CreateTrademarkRequest struct {
	TrademarkName  string `json:"trademark_name" binding:"required"`  // 商标名称
	CategoryNumber int    `json:"category_number" binding:"required"` // 商标类别号
	CountryCode    string `json:"country_code" binding:"required"`    // 地区代码
	ValidFromDate  string `json:"valid_from_date"`                    // 期限起始时间(YYYY-MM-DD)
	ValidToDate    string `json:"valid_to_date"`                      // 期限结束时间(YYYY-MM-DD)
}

// CreatePersonalityRightRequest 创建人格权请求
type CreatePersonalityRightRequest struct {
	PersonalityRightsType model.PersonalityRightsType `json:"personality_rights_type" binding:"required"` // 人格权类型
}

// UpdateRightsVerificationRequest 更新权益认证请求
type UpdateRightsVerificationRequest struct {
	// 代理信息
	IsAgent                bool            `json:"is_agent"`                 // 是否代理
	AgentType              model.AgentType `json:"agent_type"`               // 代理类型
	RightOwnerName         string          `json:"right_owner_name"`         // 权利人/代理人姓名
	AuthorizationStartDate string          `json:"authorization_start_date"` // 授权期限起始时间
	AuthorizationEndDate   string          `json:"authorization_end_date"`   // 授权期限结束时间
}

// ReviewRightsVerificationRequest 审核权益认证请求
type ReviewRightsVerificationRequest struct {
	Status       model.RightsStatus `json:"status" binding:"required"` // 审核状态
	ReviewNote   string             `json:"review_note"`               // 审核备注
	RejectReason string             `json:"reject_reason"`             // 拒绝原因
}

// GetRightsVerificationsRequest 获取权益认证列表请求
type GetRightsVerificationsRequest struct {
	Page      int                `form:"page" binding:"min=1"`             // 页码
	PageSize  int                `form:"page_size" binding:"min=1,max=50"` // 每页数量
	Type      model.RightsType   `form:"type"`                             // 权益类型过滤
	Status    model.RightsStatus `form:"status"`                           // 状态过滤
	StartDate string             `form:"start_date"`                       // 开始日期
	EndDate   string             `form:"end_date"`                         // 结束日期
}

// RightsVerificationResponse 权益认证响应
type RightsVerificationResponse struct {
	RightsKSUID          string                     `json:"rights_ksuid"`           // 权益认证KSUID
	UserKSUID            string                     `json:"user_ksuid"`             // 用户KSUID
	Type                 model.RightsType           `json:"type"`                   // 权益类型
	Status               model.RightsStatus         `json:"status"`                 // 认证状态
	IsAgent              bool                       `json:"is_agent"`               // 是否代理
	AgentType            model.AgentType            `json:"agent_type"`             // 代理类型
	RightOwnerName       string                     `json:"right_owner_name"`       // 权利人/代理人姓名
	AuthorizationStartAt *time.Time                 `json:"authorization_start_at"` // 授权期限起始时间
	AuthorizationEndAt   *time.Time                 `json:"authorization_end_at"`   // 授权期限结束时间
	AuthorizationFiles   []RightsEvidenceResponse   `json:"authorization_files"`    // 授权证明文件
	Copyrights           []CopyrightResponse        `json:"copyrights"`             // 著作权列表
	Trademarks           []TrademarkResponse        `json:"trademarks"`             // 商标权列表
	PersonalityRights    []PersonalityRightResponse `json:"personality_rights"`     // 人格权列表
	ReviewerKSUID        string                     `json:"reviewer_ksuid"`         // 审核人KSUID
	ReviewedAt           *time.Time                 `json:"reviewed_at"`            // 审核时间
	ReviewNote           string                     `json:"review_note"`            // 审核备注
	RejectReason         string                     `json:"reject_reason"`          // 拒绝原因
	CreatedAt            time.Time                  `json:"created_at"`             // 创建时间
	UpdatedAt            time.Time                  `json:"updated_at"`             // 更新时间
}

// CopyrightResponse 著作权响应
type CopyrightResponse struct {
	ID            uint                     `json:"id"`             // ID
	CopyrightType model.CopyrightType      `json:"copyright_type"` // 著作类型
	WorkName      string                   `json:"work_name"`      // 著作名称
	CountryCode   string                   `json:"country_code"`   // 地区代码
	CountryName   string                   `json:"country_name"`   // 地区名称
	ValidFrom     *time.Time               `json:"valid_from"`     // 期限起始时间
	ValidTo       *time.Time               `json:"valid_to"`       // 期限结束时间
	EvidenceFiles []RightsEvidenceResponse `json:"evidence_files"` // 证明文件
	CreatedAt     time.Time                `json:"created_at"`     // 创建时间
}

// TrademarkResponse 商标权响应
type TrademarkResponse struct {
	ID             uint                     `json:"id"`              // ID
	TrademarkName  string                   `json:"trademark_name"`  // 商标名称
	CategoryNumber int                      `json:"category_number"` // 商标类别号
	CategoryName   string                   `json:"category_name"`   // 商标类别名称
	CountryCode    string                   `json:"country_code"`    // 地区代码
	CountryName    string                   `json:"country_name"`    // 地区名称
	ValidFrom      *time.Time               `json:"valid_from"`      // 期限起始时间
	ValidTo        *time.Time               `json:"valid_to"`        // 期限结束时间
	EvidenceFiles  []RightsEvidenceResponse `json:"evidence_files"`  // 证明文件
	CreatedAt      time.Time                `json:"created_at"`      // 创建时间
}

// PersonalityRightResponse 人格权响应
type PersonalityRightResponse struct {
	ID                    uint                        `json:"id"`                      // ID
	PersonalityRightsType model.PersonalityRightsType `json:"personality_rights_type"` // 人格权类型
	EvidenceFiles         []RightsEvidenceResponse    `json:"evidence_files"`          // 证明文件
	CreatedAt             time.Time                   `json:"created_at"`              // 创建时间
}

// RightsEvidenceResponse 权益证明文件响应
type RightsEvidenceResponse struct {
	ID           uint      `json:"id"`            // 文件ID
	EvidenceType string    `json:"evidence_type"` // 证明类型
	FileName     string    `json:"file_name"`     // 文件名
	FileURL      string    `json:"file_url"`      // 文件URL
	FileSize     int64     `json:"file_size"`     // 文件大小
	ContentType  string    `json:"content_type"`  // 文件类型
	CreatedAt    time.Time `json:"created_at"`    // 上传时间
}

// UploadRightsEvidenceRequest 上传权益证明请求
type UploadRightsEvidenceRequest struct {
	RightsKSUID  string `form:"rights_ksuid" binding:"required"`  // 权益认证KSUID
	EvidenceType string `form:"evidence_type" binding:"required"` // 证明类型
	RelatedKSUID string `form:"related_ksuid"`                    // 关联的具体权益KSUID
}

// UploadRightsEvidenceResponse 上传权益证明响应
type UploadRightsEvidenceResponse struct {
	Files []RightsEvidenceResponse `json:"files"` // 上传的文件列表
}

// 验证函数
func ValidateCreateRightsVerificationRequest(req *CreateRightsVerificationRequest) *errors.Errors {
	if req.Type == "" {
		return errors.NewValidationError("权益类型不能为空")
	}

	if req.IsAgent {
		if req.AgentType == "" {
			return errors.NewValidationError("代理类型不能为空")
		}
		if req.RightOwnerName == "" {
			return errors.NewValidationError("权利人/代理人姓名不能为空")
		}
	}

	// 根据权益类型验证具体信息
	switch req.Type {
	case model.RightsTypeCopyright:
		if len(req.Copyrights) == 0 {
			return errors.NewValidationError("著作权信息不能为空")
		}
		for _, copyright := range req.Copyrights {
			if copyright.CopyrightType == "" {
				return errors.NewValidationError("著作类型不能为空")
			}
			if copyright.WorkName == "" {
				return errors.NewValidationError("著作名称不能为空")
			}
			if copyright.CountryCode == "" {
				return errors.NewValidationError("地区代码不能为空")
			}
		}
	case model.RightsTypeTrademark:
		if len(req.Trademarks) == 0 {
			return errors.NewValidationError("商标权信息不能为空")
		}
		for _, trademark := range req.Trademarks {
			if trademark.TrademarkName == "" {
				return errors.NewValidationError("商标名称不能为空")
			}
			if trademark.CategoryNumber <= 0 {
				return errors.NewValidationError("商标类别号必须大于0")
			}
			if trademark.CountryCode == "" {
				return errors.NewValidationError("地区代码不能为空")
			}
		}
	case model.RightsTypePersonality:
		if len(req.PersonalityRights) == 0 {
			return errors.NewValidationError("人格权信息不能为空")
		}
		for _, personalityRight := range req.PersonalityRights {
			if personalityRight.PersonalityRightsType == "" {
				return errors.NewValidationError("人格权类型不能为空")
			}
		}
	}

	return nil
}

// 转换函数
func RightsVerificationToResponse(verification *model.RightsVerification) *RightsVerificationResponse {
	resp := &RightsVerificationResponse{
		RightsKSUID:          verification.RightsKSUID,
		UserKSUID:            verification.UserKSUID,
		Type:                 verification.Type,
		Status:               verification.Status,
		IsAgent:              verification.IsAgent,
		AgentType:            verification.AgentType,
		RightOwnerName:       verification.RightOwnerName,
		AuthorizationStartAt: verification.AuthorizationStartAt,
		AuthorizationEndAt:   verification.AuthorizationEndAt,
		ReviewerKSUID:        verification.ReviewerKSUID,
		ReviewedAt:           verification.ReviewedAt,
		ReviewNote:           verification.ReviewNote,
		RejectReason:         verification.RejectReason,
		CreatedAt:            verification.CreatedAt,
		UpdatedAt:            verification.UpdatedAt,
	}

	// 转换授权证明文件
	for _, evidence := range verification.AuthorizationFiles {
		resp.AuthorizationFiles = append(resp.AuthorizationFiles, RightsEvidenceResponse{
			ID:           evidence.ID,
			EvidenceType: evidence.EvidenceType,
			FileName:     evidence.FileName,
			FileURL:      evidence.FileURL,
			FileSize:     evidence.FileSize,
			ContentType:  evidence.ContentType,
			CreatedAt:    evidence.CreatedAt,
		})
	}

	// 转换著作权
	for _, copyright := range verification.Copyrights {
		copyrightResp := CopyrightResponse{
			ID:            copyright.ID,
			CopyrightType: copyright.CopyrightType,
			WorkName:      copyright.WorkName,
			CountryCode:   copyright.CountryCode,
			ValidFrom:     copyright.ValidFrom,
			ValidTo:       copyright.ValidTo,
			CreatedAt:     copyright.CreatedAt,
		}
		if copyright.Country != nil {
			copyrightResp.CountryName = copyright.Country.Name
		}
		for _, evidence := range copyright.EvidenceFiles {
			copyrightResp.EvidenceFiles = append(copyrightResp.EvidenceFiles, RightsEvidenceResponse{
				ID:           evidence.ID,
				EvidenceType: evidence.EvidenceType,
				FileName:     evidence.FileName,
				FileURL:      evidence.FileURL,
				FileSize:     evidence.FileSize,
				ContentType:  evidence.ContentType,
				CreatedAt:    evidence.CreatedAt,
			})
		}
		resp.Copyrights = append(resp.Copyrights, copyrightResp)
	}

	// 转换商标权
	for _, trademark := range verification.Trademarks {
		trademarkResp := TrademarkResponse{
			ID:             trademark.ID,
			TrademarkName:  trademark.TrademarkName,
			CategoryNumber: trademark.CategoryNumber,
			CountryCode:    trademark.CountryCode,
			ValidFrom:      trademark.ValidFrom,
			ValidTo:        trademark.ValidTo,
			CreatedAt:      trademark.CreatedAt,
		}
		if trademark.Country != nil {
			trademarkResp.CountryName = trademark.Country.Name
		}
		if trademark.Category != nil {
			trademarkResp.CategoryName = trademark.Category.Name
		}
		for _, evidence := range trademark.EvidenceFiles {
			trademarkResp.EvidenceFiles = append(trademarkResp.EvidenceFiles, RightsEvidenceResponse{
				ID:           evidence.ID,
				EvidenceType: evidence.EvidenceType,
				FileName:     evidence.FileName,
				FileURL:      evidence.FileURL,
				FileSize:     evidence.FileSize,
				ContentType:  evidence.ContentType,
				CreatedAt:    evidence.CreatedAt,
			})
		}
		resp.Trademarks = append(resp.Trademarks, trademarkResp)
	}

	// 转换人格权
	for _, personalityRight := range verification.PersonalityRights {
		personalityRightResp := PersonalityRightResponse{
			ID:                    personalityRight.ID,
			PersonalityRightsType: personalityRight.PersonalityRightsType,
			CreatedAt:             personalityRight.CreatedAt,
		}
		for _, evidence := range personalityRight.EvidenceFiles {
			personalityRightResp.EvidenceFiles = append(personalityRightResp.EvidenceFiles, RightsEvidenceResponse{
				ID:           evidence.ID,
				EvidenceType: evidence.EvidenceType,
				FileName:     evidence.FileName,
				FileURL:      evidence.FileURL,
				FileSize:     evidence.FileSize,
				ContentType:  evidence.ContentType,
				CreatedAt:    evidence.CreatedAt,
			})
		}
		resp.PersonalityRights = append(resp.PersonalityRights, personalityRightResp)
	}

	return resp
}

// ReviewRightsRequest 审核权益认证请求
type ReviewRightsRequest struct {
	Status       model.RightsStatus `json:"status" binding:"required" example:"approved"`  // 审核状态
	ReviewNote   string             `json:"review_note" binding:"required" example:"审核通过"` // 审核说明
	RejectReason string             `json:"reject_reason,omitempty" example:"材料不清晰"`       // 拒绝原因
}

// RightsFilters 权益认证过滤条件
type RightsFilters struct {
	Page          int                `json:"page" example:"1"`
	PageSize      int                `json:"page_size" example:"20"`
	Status        model.RightsStatus `json:"status,omitempty" example:"pending"`
	Type          model.RightsType   `json:"type,omitempty" example:"copyright"`
	UserKSUID     string             `json:"user_ksuid,omitempty" example:"user123"`
	ReviewerKSUID string             `json:"reviewer_ksuid,omitempty" example:"admin123"`
	IsAgent       *bool              `json:"is_agent,omitempty" example:"false"`
	StartDate     string             `json:"start_date,omitempty" example:"2024-01-01"`
	EndDate       string             `json:"end_date,omitempty" example:"2024-12-31"`
	SortBy        string             `json:"sort_by,omitempty" example:"created_at"`
	SortOrder     string             `json:"sort_order,omitempty" example:"desc"`
}

// RightsVerificationListResponse 权益认证列表响应
type RightsVerificationListResponse struct {
	List       []RightsVerificationResponse `json:"list"`        // 权益认证列表
	Total      int64                        `json:"total"`       // 总数
	Page       int                          `json:"page"`        // 当前页
	PageSize   int                          `json:"page_size"`   // 每页数量
	TotalPages int                          `json:"total_pages"` // 总页数
}
