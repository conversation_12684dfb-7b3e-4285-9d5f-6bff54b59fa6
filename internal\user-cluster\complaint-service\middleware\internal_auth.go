package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// InternalAPIAuth 内部API认证中间件
func InternalAPIAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查请求来源
		if !isInternalRequest(c) {
			log.Warn().
				Str("remote_addr", c.ClientIP()).
				Str("user_agent", c.<PERSON>("User-Agent")).
				Str("path", c.Request.URL.Path).
				Msg("非内部请求访问内部API")

			c.JSON(http.StatusForbidden, gin.H{
				"error": "访问被拒绝",
			})
			c.Abort()
			return
		}

		// 检查API密钥
		apiKey := c.GetHeader("X-Internal-API-Key")
		if apiKey == "" {
			apiKey = c.GetHeader("X-API-Key")
		}

		if !isValidInternalAPIKey(apiKey) {
			log.Warn().
				Str("remote_addr", c.ClientIP()).
				Str("api_key", maskAPIKey(apiKey)).
				Str("path", c.Request.URL.Path).
				Msg("无效的内部API密钥")

			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的API密钥",
			})
			c.Abort()
			return
		}

		// 记录内部API访问日志
		log.Debug().
			Str("remote_addr", c.ClientIP()).
			Str("method", c.Request.Method).
			Str("path", c.Request.URL.Path).
			Msg("内部API访问")

		c.Next()
	}
}

// isInternalRequest 检查是否为内部请求
func isInternalRequest(c *gin.Context) bool {
	clientIP := c.ClientIP()

	// 检查是否为本地请求
	if isLocalIP(clientIP) {
		return true
	}

	// 检查是否为内网IP
	if isPrivateIP(clientIP) {
		return true
	}

	// 检查User-Agent是否为内部服务
	userAgent := c.GetHeader("User-Agent")
	if isInternalUserAgent(userAgent) {
		return true
	}

	return false
}

// isLocalIP 检查是否为本地IP
func isLocalIP(ip string) bool {
	localIPs := []string{
		"127.0.0.1",
		"::1",
		"localhost",
	}

	for _, localIP := range localIPs {
		if ip == localIP {
			return true
		}
	}

	return false
}

// isPrivateIP 检查是否为内网IP
func isPrivateIP(ip string) bool {
	// 简化实现，实际应该使用net包进行更精确的判断
	privateIPPrefixes := []string{
		"10.",
		"172.16.",
		"172.17.",
		"172.18.",
		"172.19.",
		"172.20.",
		"172.21.",
		"172.22.",
		"172.23.",
		"172.24.",
		"172.25.",
		"172.26.",
		"172.27.",
		"172.28.",
		"172.29.",
		"172.30.",
		"172.31.",
		"192.168.",
	}

	for _, prefix := range privateIPPrefixes {
		if strings.HasPrefix(ip, prefix) {
			return true
		}
	}

	return false
}

// isInternalUserAgent 检查是否为内部服务的User-Agent
func isInternalUserAgent(userAgent string) bool {
	internalUserAgents := []string{
		"pxpat-internal-service",
		"pxpat-user-service",
		"pxpat-content-service",
		"pxpat-video-service",
		"pxpat-novel-service",
		"pxpat-music-service",
		"pxpat-complaint-service",
	}

	userAgent = strings.ToLower(userAgent)
	for _, internalUA := range internalUserAgents {
		if strings.Contains(userAgent, strings.ToLower(internalUA)) {
			return true
		}
	}

	return false
}

// isValidInternalAPIKey 验证内部API密钥
func isValidInternalAPIKey(apiKey string) bool {
	if apiKey == "" {
		return false
	}

	// 这里应该从配置或数据库中获取有效的API密钥列表
	// 简化实现，使用硬编码的密钥
	validAPIKeys := []string{
		"internal-api-key-2024",
		"pxpat-internal-service-key",
		"dev-internal-key-123456",
	}

	for _, validKey := range validAPIKeys {
		if apiKey == validKey {
			return true
		}
	}

	return false
}

// maskAPIKey 脱敏API密钥
func maskAPIKey(apiKey string) string {
	if len(apiKey) <= 8 {
		return "***"
	}

	return apiKey[:4] + "***" + apiKey[len(apiKey)-4:]
}

// ServiceAuth 服务间认证中间件
func ServiceAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查服务认证令牌
		serviceToken := c.GetHeader("X-Service-Token")
		if serviceToken == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "缺少服务认证令牌",
			})
			c.Abort()
			return
		}

		// 验证服务令牌
		if !isValidServiceToken(serviceToken) {
			log.Warn().
				Str("remote_addr", c.ClientIP()).
				Str("service_token", maskAPIKey(serviceToken)).
				Str("path", c.Request.URL.Path).
				Msg("无效的服务认证令牌")

			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的服务认证令牌",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// isValidServiceToken 验证服务令牌
func isValidServiceToken(token string) bool {
	// 这里应该实现JWT令牌验证或其他服务认证机制
	// 简化实现
	validTokens := []string{
		"service-token-user-cluster",
		"service-token-content-cluster",
		"service-token-admin-cluster",
	}

	for _, validToken := range validTokens {
		if token == validToken {
			return true
		}
	}

	return false
}

// RateLimitByService 按服务限流中间件
func RateLimitByService() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取服务标识
		serviceID := c.GetHeader("X-Service-ID")
		if serviceID == "" {
			serviceID = c.ClientIP()
		}

		// 这里应该实现基于服务的限流逻辑
		// 简化实现，直接通过

		c.Next()
	}
}
