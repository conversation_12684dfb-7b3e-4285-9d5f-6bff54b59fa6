package service

import (
	"pxpat-backend/internal/storage-cluster/content-storage-service/client"
	"pxpat-backend/internal/storage-cluster/content-storage-service/repository"

	"github.com/rs/zerolog/log"
)

// ProvideInternalCoverService 提供内部封面服务
func ProvideInternalCoverService(coverRepo repository.CoverRepository, coverStorageClient client.CoverStorageClient) *InternalCoverService {
	service := NewInternalCoverService(coverRepo, coverStorageClient.StorageClient)
	log.Info().Msg("内部封面服务初始化成功")
	return service
}

// ProvideInternalVideoService 提供内部视频服务
func ProvideInternalVideoService(videoRepo repository.VideoRepository, contentStorageClient client.ContentStorageClient) *InternalVideoService {
	service := NewInternalVideoService(videoRepo, contentStorageClient.StorageClient)
	log.Info().Msg("内部视频服务初始化成功")
	return service
}
