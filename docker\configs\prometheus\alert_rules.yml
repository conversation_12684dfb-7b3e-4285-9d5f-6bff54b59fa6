# Prometheus告警规则
# 定义内容管理服务的监控告警规则

groups:
  # 服务可用性告警
  - name: service_availability
    rules:
      - alert: ServiceDown
        expr: up{job="content-management-service"} == 0
        for: 1m
        labels:
          severity: critical
          service: content-management-service
        annotations:
          summary: "内容管理服务不可用"
          description: "内容管理服务 {{ $labels.instance }} 已经停止响应超过1分钟"

      - alert: HighErrorRate
        expr: rate(http_requests_total{job="content-management-service",status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: content-management-service
        annotations:
          summary: "服务错误率过高"
          description: "内容管理服务 {{ $labels.instance }} 的5xx错误率超过10%，当前值: {{ $value }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="content-management-service"}[5m])) > 1
        for: 3m
        labels:
          severity: warning
          service: content-management-service
        annotations:
          summary: "服务响应时间过长"
          description: "内容管理服务 {{ $labels.instance }} 的95%响应时间超过1秒，当前值: {{ $value }}s"

  # 资源使用告警
  - name: resource_usage
    rules:
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total{job="content-management-service"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: content-management-service
        annotations:
          summary: "CPU使用率过高"
          description: "内容管理服务 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes{job="content-management-service"} / 1024 / 1024 > 512
        for: 5m
        labels:
          severity: warning
          service: content-management-service
        annotations:
          summary: "内存使用过高"
          description: "内容管理服务 {{ $labels.instance }} 内存使用超过512MB，当前值: {{ $value }}MB"

      - alert: TooManyGoroutines
        expr: go_goroutines{job="content-management-service"} > 1000
        for: 5m
        labels:
          severity: warning
          service: content-management-service
        annotations:
          summary: "Goroutine数量过多"
          description: "内容管理服务 {{ $labels.instance }} Goroutine数量超过1000，当前值: {{ $value }}"

  # 数据库告警
  - name: database_alerts
    rules:
      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgres
        annotations:
          summary: "PostgreSQL数据库不可用"
          description: "PostgreSQL数据库 {{ $labels.instance }} 已经停止响应超过1分钟"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends{job="postgres"} > 80
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "数据库连接数过高"
          description: "PostgreSQL数据库 {{ $labels.instance }} 连接数超过80，当前值: {{ $value }}"

  # Redis告警
  - name: redis_alerts
    rules:
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis服务不可用"
          description: "Redis服务 {{ $labels.instance }} 已经停止响应超过1分钟"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes{job="redis"} / redis_memory_max_bytes{job="redis"} > 0.8
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis服务 {{ $labels.instance }} 内存使用率超过80%，当前值: {{ $value }}%"

  # 业务指标告警
  - name: business_metrics
    rules:
      - alert: HighCacheMissRate
        expr: rate(cache_misses_total{job="content-management-service"}[5m]) / rate(cache_requests_total{job="content-management-service"}[5m]) > 0.5
        for: 5m
        labels:
          severity: warning
          service: content-management-service
        annotations:
          summary: "缓存命中率过低"
          description: "内容管理服务 {{ $labels.instance }} 缓存命中率低于50%，当前值: {{ $value }}%"

      - alert: QueueSizeHigh
        expr: queue_size{job="content-management-service"} > 1000
        for: 5m
        labels:
          severity: warning
          service: content-management-service
        annotations:
          summary: "队列积压过多"
          description: "内容管理服务 {{ $labels.instance }} 队列大小超过1000，当前值: {{ $value }}"
