package model

import (
	"time"
)

// RightsType 权益类型
type RightsType string

const (
	RightsTypeCopyright   RightsType = "copyright"   // 著作权
	RightsTypeTrademark   RightsType = "trademark"   // 商标权
	RightsTypeReputation  RightsType = "reputation"  // 商誉权
	RightsTypePersonality RightsType = "personality" // 人格权
	RightsTypeNickname    RightsType = "nickname"    // 昵称权
)

// RightsStatus 权益认证状态
type RightsStatus string

const (
	RightsStatusPending  RightsStatus = "pending"  // 待审核
	RightsStatusApproved RightsStatus = "approved" // 已通过
	RightsStatusRejected RightsStatus = "rejected" // 已拒绝
	RightsStatusExpired  RightsStatus = "expired"  // 已过期
)

// AgentType 代理类型
type AgentType string

const (
	AgentTypeAgent      AgentType = "agent"       // 代理人
	AgentTypeRightOwner AgentType = "right_owner" // 权利人
)

// CopyrightType 著作类型
type CopyrightType string

const (
	CopyrightTypeVideo CopyrightType = "video" // 视频
	CopyrightTypeAnime CopyrightType = "anime" // 动漫
	CopyrightTypeComic CopyrightType = "comic" // 漫画
	CopyrightTypeNovel CopyrightType = "novel" // 小说
)

// PersonalityRightsType 人格权类型
type PersonalityRightsType string

const (
	PersonalityRightsTypeName     PersonalityRightsType = "name"     // 姓名权
	PersonalityRightsTypePortrait PersonalityRightsType = "portrait" // 肖像权
	PersonalityRightsTypeHonor    PersonalityRightsType = "honor"    // 名誉权
	PersonalityRightsTypePrivacy  PersonalityRightsType = "privacy"  // 隐私权
	PersonalityRightsTypeOther    PersonalityRightsType = "other"    // 其他
)

// RightsVerification 权益认证模型
type RightsVerification struct {
	ID        uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"-"`

	// 基础字段
	RightsKSUID string       `gorm:"size:27;uniqueIndex;not null" json:"rights_ksuid"` // 权益认证唯一标识
	UserKSUID   string       `gorm:"size:27;not null;index" json:"user_ksuid"`         // 用户KSUID
	Type        RightsType   `gorm:"size:20;not null" json:"type"`                     // 权益类型
	Status      RightsStatus `gorm:"size:20;default:'pending'" json:"status"`          // 认证状态

	// 代理信息
	IsAgent              bool       `gorm:"default:false" json:"is_agent"`    // 是否代理
	AgentType            AgentType  `gorm:"size:20" json:"agent_type"`        // 代理类型
	RightOwnerName       string     `gorm:"size:100" json:"right_owner_name"` // 权利人/代理人姓名
	AuthorizationStartAt *time.Time `json:"authorization_start_at"`           // 授权期限起始时间
	AuthorizationEndAt   *time.Time `json:"authorization_end_at"`             // 授权期限结束时间

	// 授权证明文件
	AuthorizationFiles []RightsEvidence `gorm:"foreignKey:RightsKSUID;references:RightsKSUID;constraint:OnDelete:CASCADE" json:"authorization_files,omitempty"`

	// 审核信息
	ReviewerKSUID string     `gorm:"size:27" json:"reviewer_ksuid"`  // 审核人KSUID
	ReviewedAt    *time.Time `json:"reviewed_at"`                    // 审核时间
	ReviewNote    string     `gorm:"type:text" json:"review_note"`   // 审核备注
	RejectReason  string     `gorm:"type:text" json:"reject_reason"` // 拒绝原因

	// 关联的具体权益信息
	Copyrights        []Copyright        `gorm:"foreignKey:RightsKSUID;references:RightsKSUID;constraint:OnDelete:CASCADE" json:"copyrights,omitempty"`
	Trademarks        []Trademark        `gorm:"foreignKey:RightsKSUID;references:RightsKSUID;constraint:OnDelete:CASCADE" json:"trademarks,omitempty"`
	PersonalityRights []PersonalityRight `gorm:"foreignKey:RightsKSUID;references:RightsKSUID;constraint:OnDelete:CASCADE" json:"personality_rights,omitempty"`
}

// TableName 指定表名
func (RightsVerification) TableName() string {
	return "complaint_rights_verifications"
}

// Copyright 著作权模型
type Copyright struct {
	ID        uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"-"`

	RightsKSUID   string        `gorm:"size:27;not null;index" json:"rights_ksuid"` // 关联的权益认证KSUID
	CopyrightType CopyrightType `gorm:"size:20;not null" json:"copyright_type"`     // 著作类型
	WorkName      string        `gorm:"size:255;not null" json:"work_name"`         // 著作名称
	CountryCode   string        `gorm:"size:10;not null" json:"country_code"`       // 地区代码
	ValidFrom     *time.Time    `json:"valid_from"`                                 // 期限起始时间
	ValidTo       *time.Time    `json:"valid_to"`                                   // 期限结束时间

	// 权利人证明文件
	EvidenceFiles []RightsEvidence `gorm:"foreignKey:RelatedKSUID;references:RightsKSUID;constraint:OnDelete:CASCADE" json:"evidence_files,omitempty"`

	// 关联关系
	Country *Country `gorm:"foreignKey:CountryCode;references:Code" json:"country,omitempty"`
}

// TableName 指定表名
func (Copyright) TableName() string {
	return "complaint_copyrights"
}

// Trademark 商标权模型
type Trademark struct {
	ID        uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"-"`

	RightsKSUID    string     `gorm:"size:27;not null;index" json:"rights_ksuid"` // 关联的权益认证KSUID
	TrademarkName  string     `gorm:"size:255;not null" json:"trademark_name"`    // 商标名称
	CategoryNumber int        `gorm:"not null" json:"category_number"`            // 商标类别号
	CountryCode    string     `gorm:"size:10;not null" json:"country_code"`       // 地区代码
	ValidFrom      *time.Time `json:"valid_from"`                                 // 期限起始时间
	ValidTo        *time.Time `json:"valid_to"`                                   // 期限结束时间

	// 证明文件
	EvidenceFiles []RightsEvidence `gorm:"foreignKey:RelatedKSUID;references:RightsKSUID;constraint:OnDelete:CASCADE" json:"evidence_files,omitempty"`

	// 关联关系
	Country  *Country           `gorm:"foreignKey:CountryCode;references:Code" json:"country,omitempty"`
	Category *TrademarkCategory `gorm:"foreignKey:CategoryNumber;references:CategoryNumber" json:"category,omitempty"`
}

// TableName 指定表名
func (Trademark) TableName() string {
	return "complaint_trademarks"
}

// PersonalityRight 人格权模型
type PersonalityRight struct {
	ID        uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"-"`

	RightsKSUID           string                `gorm:"size:27;not null;index" json:"rights_ksuid"`      // 关联的权益认证KSUID
	PersonalityRightsType PersonalityRightsType `gorm:"size:20;not null" json:"personality_rights_type"` // 人格权类型

	// 证明文件
	EvidenceFiles []RightsEvidence `gorm:"foreignKey:RelatedKSUID;references:RightsKSUID;constraint:OnDelete:CASCADE" json:"evidence_files,omitempty"`
}

// TableName 指定表名
func (PersonalityRight) TableName() string {
	return "complaint_personality_rights"
}

// RightsEvidence 权益证明文件模型
type RightsEvidence struct {
	ID        uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"-"`

	RightsKSUID  string `gorm:"size:27;not null;index" json:"rights_ksuid"` // 关联的权益认证KSUID
	RelatedKSUID string `gorm:"size:27;index" json:"related_ksuid"`         // 关联的具体权益KSUID(如著作权、商标权等)
	EvidenceType string `gorm:"size:50;not null" json:"evidence_type"`      // 证明类型(authorization, copyright, trademark, personality, nickname)
	FileName     string `gorm:"size:255;not null" json:"file_name"`         // 文件名
	FileURL      string `gorm:"size:1024;not null" json:"file_url"`         // 文件URL
	FileSize     int64  `json:"file_size"`                                  // 文件大小
	ContentType  string `gorm:"size:100" json:"content_type"`               // 文件类型
	BucketName   string `gorm:"size:100" json:"bucket_name"`                // 存储桶名称
	ObjectName   string `gorm:"size:512" json:"object_name"`                // 对象名称
}

// TableName 指定表名
func (RightsEvidence) TableName() string {
	return "complaint_rights_evidences"
}
