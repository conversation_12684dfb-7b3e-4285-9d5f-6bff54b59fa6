package model

import (
	"fmt"
	"gorm.io/gorm"
	"time"
)

// OperationLog 操作日志模型
type OperationLog struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	OperatorKSUID string    `gorm:"type:varchar(32);not null;index" json:"operator_ksuid"`
	OperationType string    `gorm:"type:varchar(50);not null" json:"operation_type"`
	TargetType    string    `gorm:"type:varchar(50);not null" json:"target_type"`
	TargetKSUID   string    `gorm:"type:varchar(32);not null;index" json:"target_ksuid"`
	Description   string    `gorm:"type:text" json:"description"`
	BeforeData    string    `gorm:"type:jsonb" json:"before_data"`
	AfterData     string    `gorm:"type:jsonb" json:"after_data"`
	IPAddress     string    `gorm:"type:varchar(45)" json:"ip_address"`
	UserAgent     string    `gorm:"type:text" json:"user_agent"`
	CreatedAt     time.Time `json:"created_at"`
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operation_logs"
}

// BeforeCreate GORM钩子，创建前执行
func (ol *OperationLog) BeforeCreate(tx *gorm.DB) error {
	if ol.CreatedAt.IsZero() {
		ol.CreatedAt = time.Now()
	}
	return nil
}

// OperationLogList 操作日志列表
type OperationLogList struct {
	Logs       []*OperationLog `json:"logs"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

// OperationLogQuery 操作日志查询条件
type OperationLogQuery struct {
	OperatorKSUID string     `json:"operator_ksuid,omitempty"`
	OperationType string     `json:"operation_type,omitempty"`
	TargetType    string     `json:"target_type,omitempty"`
	TargetKSUID   string     `json:"target_ksuid,omitempty"`
	StartTime     *time.Time `json:"start_time,omitempty"`
	EndTime       *time.Time `json:"end_time,omitempty"`
	Page          int        `json:"page,omitempty"`
	Limit         int        `json:"limit,omitempty"`
}

// OperationLogStats 操作日志统计
type OperationLogStats struct {
	TotalOperations      int64            `json:"total_operations"`
	OperationsByType     map[string]int64 `json:"operations_by_type"`
	OperationsByTarget   map[string]int64 `json:"operations_by_target"`
	OperationsByOperator map[string]int64 `json:"operations_by_operator"`
	RecentOperations     int64            `json:"recent_operations"` // 最近24小时
}

// 操作类型常量
const (
	OperationTypeCreate       = "create"
	OperationTypeUpdate       = "update"
	OperationTypeDelete       = "delete"
	OperationTypeBatchUpdate  = "batch_update"
	OperationTypeBatchDelete  = "batch_delete"
	OperationTypeStatusChange = "status_change"
	OperationTypeView         = "view"
	OperationTypeExport       = "export"
	OperationTypeImport       = "import"
)

// 目标类型常量
const (
	TargetTypeContent  = "content"
	TargetTypeVideo    = "video"
	TargetTypeNovel    = "novel"
	TargetTypeMusic    = "music"
	TargetTypeUser     = "user"
	TargetTypeCategory = "category"
	TargetTypeTag      = "tag"
	TargetTypeSystem   = "system"
)

// NewOperationLog 创建新的操作日志
func NewOperationLog(operatorKSUID, operationType, targetType, targetKSUID, description string) *OperationLog {
	return &OperationLog{
		OperatorKSUID: operatorKSUID,
		OperationType: operationType,
		TargetType:    targetType,
		TargetKSUID:   targetKSUID,
		Description:   description,
		CreatedAt:     time.Now(),
	}
}

// SetBeforeData 设置操作前数据
func (ol *OperationLog) SetBeforeData(data string) {
	ol.BeforeData = data
}

// SetAfterData 设置操作后数据
func (ol *OperationLog) SetAfterData(data string) {
	ol.AfterData = data
}

// SetRequestInfo 设置请求信息
func (ol *OperationLog) SetRequestInfo(ipAddress, userAgent string) {
	ol.IPAddress = ipAddress
	ol.UserAgent = userAgent
}

// IsValidOperationType 检查操作类型是否有效
func IsValidOperationType(operationType string) bool {
	validTypes := []string{
		OperationTypeCreate,
		OperationTypeUpdate,
		OperationTypeDelete,
		OperationTypeBatchUpdate,
		OperationTypeBatchDelete,
		OperationTypeStatusChange,
		OperationTypeView,
		OperationTypeExport,
		OperationTypeImport,
	}

	for _, validType := range validTypes {
		if operationType == validType {
			return true
		}
	}
	return false
}

// IsValidTargetType 检查目标类型是否有效
func IsValidTargetType(targetType string) bool {
	validTypes := []string{
		TargetTypeContent,
		TargetTypeVideo,
		TargetTypeNovel,
		TargetTypeMusic,
		TargetTypeUser,
		TargetTypeCategory,
		TargetTypeTag,
		TargetTypeSystem,
	}

	for _, validType := range validTypes {
		if targetType == validType {
			return true
		}
	}
	return false
}

// GetOperationDescription 获取操作描述
func GetOperationDescription(operationType, targetType string, targetCount int) string {
	switch operationType {
	case OperationTypeCreate:
		return "创建" + getTargetTypeName(targetType)
	case OperationTypeUpdate:
		return "更新" + getTargetTypeName(targetType)
	case OperationTypeDelete:
		return "删除" + getTargetTypeName(targetType)
	case OperationTypeBatchUpdate:
		if targetCount > 0 {
			return fmt.Sprintf("批量更新%d个%s", targetCount, getTargetTypeName(targetType))
		}
		return "批量更新" + getTargetTypeName(targetType)
	case OperationTypeBatchDelete:
		if targetCount > 0 {
			return fmt.Sprintf("批量删除%d个%s", targetCount, getTargetTypeName(targetType))
		}
		return "批量删除" + getTargetTypeName(targetType)
	case OperationTypeStatusChange:
		return "更改" + getTargetTypeName(targetType) + "状态"
	case OperationTypeView:
		return "查看" + getTargetTypeName(targetType)
	case OperationTypeExport:
		return "导出" + getTargetTypeName(targetType)
	case OperationTypeImport:
		return "导入" + getTargetTypeName(targetType)
	default:
		return "操作" + getTargetTypeName(targetType)
	}
}

// getTargetTypeName 获取目标类型中文名称
func getTargetTypeName(targetType string) string {
	switch targetType {
	case TargetTypeContent:
		return "内容"
	case TargetTypeVideo:
		return "视频"
	case TargetTypeNovel:
		return "小说"
	case TargetTypeMusic:
		return "音乐"
	case TargetTypeUser:
		return "用户"
	case TargetTypeCategory:
		return "分类"
	case TargetTypeTag:
		return "标签"
	case TargetTypeSystem:
		return "系统"
	default:
		return "对象"
	}
}
