<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="通知网关" type="GoApplicationRunConfiguration" factoryName="Go Application" folderName="通知组">
    <module name="pxpat-backend" />
    <working_directory value="$PROJECT_DIR$" />
    <kind value="PACKAGE" />
    <package value="pxpat-backend/cmd/notify-cluster/gateway" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/notify-cluster/gateway/control_gateway.go" />
    <method v="2" />
  </configuration>
</component>