# Logstash 管道配置 - 用于处理 pxpat 微服务集群日志

input {
  # 从 Filebeat 接收日志
  beats {
    port => 5044
  }
  
  # 直接从文件读取日志（备用方案）
  file {
    path => "/var/log/pxpat/**/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => "json"
    tags => ["file-input"]
  }
  
  # TCP 输入（用于应用程序直接发送日志）
  tcp {
    port => 5001
    codec => json_lines
    tags => ["tcp-input"]
  }
  
  # UDP 输入（用于高频日志）
  udp {
    port => 5002
    codec => json_lines
    tags => ["udp-input"]
  }
}

filter {
  # 解析时间戳
  if [timestamp] {
    date {
      match => [ "timestamp", "ISO8601" ]
    }
  }
  
  # 解析服务名称和集群信息
  if [service] {
    mutate {
      add_field => { "service_name" => "%{service}" }
    }
  }
  
  # 根据日志级别添加标签
  if [level] {
    if [level] == "ERROR" or [level] == "error" {
      mutate {
        add_tag => ["error"]
      }
    } else if [level] == "WARN" or [level] == "warn" {
      mutate {
        add_tag => ["warning"]
      }
    } else if [level] == "INFO" or [level] == "info" {
      mutate {
        add_tag => ["info"]
      }
    } else if [level] == "DEBUG" or [level] == "debug" {
      mutate {
        add_tag => ["debug"]
      }
    }
  }
  
  # 解析用户集群日志
  if [service] =~ /user-/ {
    mutate {
      add_field => { "cluster" => "user-cluster" }
    }
  }
  
  # 解析管理集群日志
  if [service] =~ /admin-/ {
    mutate {
      add_field => { "cluster" => "admin-cluster" }
    }
  }
  
  # 解析内容集群日志
  if [service] =~ /content-/ {
    mutate {
      add_field => { "cluster" => "content-cluster" }
    }
  }
  
  # 解析存储集群日志
  if [service] =~ /storage-/ {
    mutate {
      add_field => { "cluster" => "storage-cluster" }
    }
  }
  
  # 解析通知集群日志
  if [service] =~ /notify-/ {
    mutate {
      add_field => { "cluster" => "notify-cluster" }
    }
  }
  
  # 解析财务集群日志
  if [service] =~ /finance-/ {
    mutate {
      add_field => { "cluster" => "finance-cluster" }
    }
  }
  
  # 添加环境标识
  mutate {
    add_field => { "environment" => "development" }
    add_field => { "project" => "pxpat-backend" }
  }
  
  # 清理不需要的字段
  mutate {
    remove_field => ["host", "agent", "ecs", "input"]
  }
}

output {
  # 输出到 Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "pxpat-logs-%{+YYYY.MM.dd}"
    template_name => "pxpat-logs"
    template_pattern => "pxpat-logs-*"
    template => {
      "index_patterns" => ["pxpat-logs-*"]
      "settings" => {
        "number_of_shards" => 1
        "number_of_replicas" => 0
        "index.refresh_interval" => "5s"
      }
      "mappings" => {
        "properties" => {
          "@timestamp" => { "type" => "date" }
          "level" => { "type" => "keyword" }
          "service" => { "type" => "keyword" }
          "cluster" => { "type" => "keyword" }
          "message" => { "type" => "text" }
          "environment" => { "type" => "keyword" }
          "project" => { "type" => "keyword" }
        }
      }
    }
  }
  
  # 调试输出（可选，用于开发调试）
  # stdout { codec => rubydebug }
} 