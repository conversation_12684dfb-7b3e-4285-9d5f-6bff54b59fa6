package complaint

import (
	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/user-cluster/complaint-service/external/handler"
)

// RegisterComplaintRoutes 注册投诉相关路由
func RegisterComplaintRoutes(r *gin.RouterGroup, complaintHandler *handler.ComplaintHandler, authMiddleware gin.HandlerFunc) {
	// 投诉管理路由组
	complaintGroup := r.Group("/complaints")
	{
		// 需要认证的路由
		complaintGroup.Use(authMiddleware)
		{
			// 投诉CRUD操作
			complaintGroup.POST("", complaintHandler.CreateComplaint)                    // 创建投诉
			complaintGroup.GET("/:complaint_ksuid", complaintHandler.GetComplaint)       // 获取投诉详情
			complaintGroup.PUT("/:complaint_ksuid", complaintHandler.UpdateComplaint)    // 更新投诉
			complaintGroup.DELETE("/:complaint_ksuid", complaintHandler.DeleteComplaint) // 删除投诉

			// 投诉列表查询
			complaintGroup.GET("/my", complaintHandler.GetMyComplaints)                // 获取我的投诉列表
			complaintGroup.GET("/against-me", complaintHandler.GetComplaintsAgainstMe) // 获取针对我的投诉列表

			// 投诉统计
			complaintGroup.GET("/stats", complaintHandler.GetComplaintStats) // 获取投诉统计信息
		}

		// 公开路由（不需要认证）
		complaintGroup.GET("/violation-categories", complaintHandler.GetViolationCategories) // 获取违规类别
	}

}
