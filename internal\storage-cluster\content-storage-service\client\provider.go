package client

import (
	"pxpat-backend/internal/storage-cluster/content-storage-service/types"
	"pxpat-backend/pkg/storage"

	"github.com/rs/zerolog/log"
)

// ContentStorageClient 内容存储客户端类型
type ContentStorageClient struct {
	storage.StorageClient
}

// CoverStorageClient 封面存储客户端类型
type CoverStorageClient struct {
	storage.StorageClient
}

// ProvideContentStorageClient 提供内容存储客户端
func ProvideContentStorageClient(cfg *types.Config) (ContentStorageClient, error) {
	contentStorageClient, err := storage.NewStorageClient(cfg.ContentStorage)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize content storage provider")
		return ContentStorageClient{}, err
	}
	log.Info().Str("provider", cfg.ContentStorage.Provider).Str("bucket", cfg.ContentStorage.Bucket).Msg("Content storage provider initialized successfully")
	return ContentStorageClient{contentStorageClient}, nil
}

// ProvideCoverStorageClient 提供封面存储客户端
func ProvideCoverStorageClient(cfg *types.Config) (CoverStorageClient, error) {
	coverStorageClient, err := storage.NewStorageClient(cfg.CoverStorage)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cover storage provider")
		return CoverStorageClient{}, err
	}
	log.Info().Str("provider", cfg.CoverStorage.Provider).Str("bucket", cfg.CoverStorage.Bucket).Msg("CoverURL storage provider initialized successfully")
	return CoverStorageClient{coverStorageClient}, nil
}
