package integration_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"pxpat-backend/internal/content-cluster/content-management-service/client"
	"pxpat-backend/internal/content-cluster/content-management-service/external/handler"
	"pxpat-backend/internal/content-cluster/content-management-service/external/service"
	"pxpat-backend/internal/content-cluster/content-management-service/migrations"
	"pxpat-backend/internal/content-cluster/content-management-service/repository"
	"pxpat-backend/internal/content-cluster/content-management-service/routes"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
	"pxpat-backend/internal/content-cluster/content-management-service/utils"
	"pxpat-backend/pkg/cache"
	"pxpat-backend/pkg/jwt"
)

// TestServer 集成测试服务器
type TestServer struct {
	Server     *httptest.Server
	DB         *gorm.DB
	Redis      *redis.Client
	JWTManager jwt.Manager

	// 服务组件
	ContentHandler *handler.ContentHandler
	StatsHandler   *handler.StatsHandler
	BatchHandler   *handler.BatchHandler

	// Mock服务器
	VideoServiceMock       *httptest.Server
	InteractionServiceMock *httptest.Server
}

// SetupTestServer 设置集成测试服务器
func SetupTestServer(t *testing.T) *TestServer {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	// 运行数据库迁移
	err = migrations.AutoMigrate(db)
	require.NoError(t, err)

	// 创建测试Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15, // 测试专用DB
	})

	// 测试Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		t.Skipf("Redis不可用，跳过集成测试: %v", err)
	}

	// 清空测试数据库
	rdb.FlushDB(ctx)

	// 创建缓存管理器
	cacheManager := cache.NewRedisManager(rdb)

	// 创建JWT管理器
	jwtManager := jwt.NewJWTManager(&jwt.Config{
		SecretKey:       "test-secret-key",
		TokenDuration:   time.Hour,
		RefreshDuration: 24 * time.Hour,
	})

	// 创建Mock外部服务
	videoServiceMock := createVideoServiceMock()
	interactionServiceMock := createInteractionServiceMock()

	// 创建服务客户端
	videoClient := client.NewVideoServiceClient(videoServiceMock.URL, 30*time.Second)
	interactionClient := client.NewInteractionServiceClient(interactionServiceMock.URL, 30*time.Second)

	// 创建Repository层
	operationRepo := repository.NewOperationLogRepository(db, cacheManager)
	cacheRepo := repository.NewContentCacheRepository(db, rdb, cacheManager)
	configRepo := repository.NewConfigRepository(db, cacheManager)

	// 创建工具函数
	converter := utils.NewContentConverter()
	aggregator := utils.NewDataAggregator(videoClient, interactionClient)
	validator := utils.NewContentValidator()

	// 创建Service层
	contentService := service.NewContentManagementService(
		videoClient, interactionClient, cacheRepo, operationRepo, configRepo,
		converter, aggregator, validator, rdb,
	)
	statsService := service.NewStatsAnalysisService(
		videoClient, interactionClient, cacheRepo, operationRepo, configRepo,
		aggregator, rdb,
	)
	batchService := service.NewBatchOperationService(
		videoClient, interactionClient, cacheRepo, operationRepo, configRepo,
		converter, aggregator, validator, rdb,
	)

	// 创建Handler层
	contentHandler := handler.NewContentHandler(contentService)
	statsHandler := handler.NewStatsHandler(statsService)
	batchHandler := handler.NewBatchHandler(batchService)

	// 创建路由
	routerConfig := routes.RouterConfig{
		ContentHandler: contentHandler,
		StatsHandler:   statsHandler,
		BatchHandler:   batchHandler,
		JWTManager:     jwtManager,
		Redis:          rdb,
		EnableSwagger:  false,
		EnableCORS:     true,
		Debug:          true,
	}

	router := routes.SetupRouter(routerConfig)

	// 创建测试服务器
	server := httptest.NewServer(router)

	return &TestServer{
		Server:                 server,
		DB:                     db,
		Redis:                  rdb,
		JWTManager:             jwtManager,
		ContentHandler:         contentHandler,
		StatsHandler:           statsHandler,
		BatchHandler:           batchHandler,
		VideoServiceMock:       videoServiceMock,
		InteractionServiceMock: interactionServiceMock,
	}
}

// TeardownTestServer 清理测试服务器
func (ts *TestServer) TeardownTestServer() {
	if ts.Server != nil {
		ts.Server.Close()
	}
	if ts.VideoServiceMock != nil {
		ts.VideoServiceMock.Close()
	}
	if ts.InteractionServiceMock != nil {
		ts.InteractionServiceMock.Close()
	}
	if ts.Redis != nil {
		ts.Redis.FlushDB(context.Background())
		ts.Redis.Close()
	}
	if ts.DB != nil {
		sqlDB, _ := ts.DB.DB()
		if sqlDB != nil {
			sqlDB.Close()
		}
	}
}

// GenerateTestJWT 生成测试JWT Token
func (ts *TestServer) GenerateTestJWT(userKSUID, role string) (string, error) {
	claims := &jwt.Claims{
		UserKSUID: userKSUID,
		Role:      role,
	}
	return ts.JWTManager.GenerateToken(claims)
}

// createVideoServiceMock 创建视频服务Mock
func createVideoServiceMock() *httptest.Server {
	mux := http.NewServeMux()

	// Mock视频列表接口
	mux.HandleFunc("/api/v1/videos", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "GET" {
			response := `{
				"code": 200,
				"data": {
					"videos": [
						{
							"ksuid": "video_1",
							"title": "测试视频1",
							"description": "测试描述1",
							"user_ksuid": "user_1",
							"status": "published",
							"category_id": 1,
							"tags": ["测试", "视频"],
							"duration": 300,
							"resolution": "1920x1080",
							"file_size": 1024000,
							"created_at": "2024-01-01T00:00:00Z",
							"updated_at": "2024-01-01T00:00:00Z"
						}
					],
					"total": 1
				}
			}`
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(response))
		}
	})

	// Mock单个视频接口
	mux.HandleFunc("/api/v1/videos/", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "GET" {
			response := `{
				"code": 200,
				"data": {
					"ksuid": "video_1",
					"title": "测试视频1",
					"description": "测试描述1",
					"user_ksuid": "user_1",
					"status": "published",
					"category_id": 1,
					"tags": ["测试", "视频"],
					"duration": 300,
					"resolution": "1920x1080",
					"file_size": 1024000,
					"created_at": "2024-01-01T00:00:00Z",
					"updated_at": "2024-01-01T00:00:00Z"
				}
			}`
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(response))
		}
	})

	// Mock视频状态更新接口
	mux.HandleFunc("/api/v1/videos/status", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "PUT" {
			response := `{"code": 200, "msg": "状态更新成功"}`
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(response))
		}
	})

	return httptest.NewServer(mux)
}

// createInteractionServiceMock 创建交互服务Mock
func createInteractionServiceMock() *httptest.Server {
	mux := http.NewServeMux()

	// Mock交互统计接口
	mux.HandleFunc("/api/v1/interactions/stats", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "GET" {
			response := `{
				"code": 200,
				"data": {
					"views": 1000,
					"likes": 50,
					"favorites": 20,
					"comments": 10,
					"shares": 5
				}
			}`
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(response))
		}
	})

	// Mock批量交互统计接口
	mux.HandleFunc("/api/v1/interactions/batch/stats", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "POST" {
			response := `{
				"code": 200,
				"data": {
					"video_1": {
						"views": 1000,
						"likes": 50,
						"favorites": 20,
						"comments": 10,
						"shares": 5
					}
				}
			}`
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(response))
		}
	})

	return httptest.NewServer(mux)
}

// CreateTestData 创建测试数据
func (ts *TestServer) CreateTestData(t *testing.T) {
	// 这里可以创建一些测试需要的基础数据
	// 比如操作日志、缓存数据、配置数据等
}

// GetBaseURL 获取测试服务器基础URL
func (ts *TestServer) GetBaseURL() string {
	return ts.Server.URL
}
