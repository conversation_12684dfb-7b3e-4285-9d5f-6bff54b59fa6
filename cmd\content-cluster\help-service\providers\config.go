package providers

import (
	"pxpat-backend/internal/content-cluster/help-service/types"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/logger"

	"github.com/rs/zerolog/log"
)

// ProvideConfig 提供配置
func ProvideConfig() *types.Config {
	clusterName := "content"
	serviceName := "help"

	config := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})

	log.Info().Msg("配置加载成功")
	return config
}

// ProvideLogger 提供日志器
func ProvideLogger(cfg *types.Config) error {
	serviceName := "help"

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Msgf("日志系统初始化失败: %v", err)
		return err
	}

	log.Info().Msg("日志系统初始化成功")
	return nil
}
