package model

import (
	"time"
)

// IdentityType 身份类型
type IdentityType string

const (
	IdentityTypeNatural     IdentityType = "natural"      // 自然人
	IdentityTypeLegalEntity IdentityType = "legal_entity" // 法人/非法人组织
)

// IdentityStatus 身份认证状态
type IdentityStatus string

const (
	IdentityStatusPending  IdentityStatus = "pending"  // 待审核
	IdentityStatusApproved IdentityStatus = "approved" // 已通过
	IdentityStatusRejected IdentityStatus = "rejected" // 已拒绝
	IdentityStatusExpired  IdentityStatus = "expired"  // 已过期
)

// IdentityVerification 身份认证模型
type IdentityVerification struct {
	ID        uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"-"`

	// 基础字段
	VerificationKSUID string         `gorm:"size:27;uniqueIndex;not null" json:"verification_ksuid"` // 认证唯一标识
	UserKSUID         string         `gorm:"size:27;not null;index" json:"user_ksuid"`               // 用户KSUID
	Type              IdentityType   `gorm:"size:20;not null" json:"type"`                           // 身份类型
	Status            IdentityStatus `gorm:"size:20;default:'pending'" json:"status"`                // 认证状态

	// 自然人信息
	RealName       string `gorm:"size:100" json:"real_name"`      // 真实姓名
	PhoneNumber    string `gorm:"size:20" json:"phone_number"`    // 手机号
	Email          string `gorm:"size:255" json:"email"`          // 邮箱
	IDCard         string `gorm:"size:32" json:"id_card"`         // 身份证号
	Address        string `gorm:"size:500" json:"address"`        // 联系地址
	PostalCode     string `gorm:"size:20" json:"postal_code"`     // 邮政编码
	FaxNumber      string `gorm:"size:20" json:"fax_number"`      // 传真号
	LandlineNumber string `gorm:"size:20" json:"landline_number"` // 座机号

	// 法人/非法人组织信息
	OrganizationName   string     `gorm:"size:255" json:"organization_name"`  // 企业/机构/单位/团体全称
	CertificateNumber  string     `gorm:"size:100" json:"certificate_number"` // 证件编号(营业执照证件编号)
	CertificateStartAt *time.Time `json:"certificate_start_at"`               // 证件有效期开始时间
	CertificateEndAt   *time.Time `json:"certificate_end_at"`                 // 证件有效期结束时间
	ContactName        string     `gorm:"size:100" json:"contact_name"`       // 联系人姓名
	ContactIDCard      string     `gorm:"size:32" json:"contact_id_card"`     // 联系人身份证号
	ContactEmail       string     `gorm:"size:255" json:"contact_email"`      // 联系邮箱
	ContactPhone       string     `gorm:"size:20" json:"contact_phone"`       // 联系人手机号
	ContactAddress     string     `gorm:"size:500" json:"contact_address"`    // 联系地址
	ContactPostalCode  string     `gorm:"size:20" json:"contact_postal_code"` // 邮政编码
	ContactFax         string     `gorm:"size:20" json:"contact_fax"`         // 传真号
	ContactLandline    string     `gorm:"size:20" json:"contact_landline"`    // 座机号

	// 审核信息
	ReviewerKSUID string     `gorm:"size:27" json:"reviewer_ksuid"`  // 审核人KSUID
	ReviewedAt    *time.Time `json:"reviewed_at"`                    // 审核时间
	ReviewNote    string     `gorm:"type:text" json:"review_note"`   // 审核备注
	RejectReason  string     `gorm:"type:text" json:"reject_reason"` // 拒绝原因
}

// TableName 指定表名
func (IdentityVerification) TableName() string {
	return "complaint_identity_verifications"
}

// Country 国家地区模型
type Country struct {
	ID        uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"-"`

	Code      string `gorm:"size:10;uniqueIndex;not null" json:"code"` // 国家代码(如CN, US, HK, MO, TW)
	Name      string `gorm:"size:100;not null" json:"name"`            // 国家名称
	NameEn    string `gorm:"size:100" json:"name_en"`                  // 英文名称
	NameLocal string `gorm:"size:100" json:"name_local"`               // 本地名称
	Continent string `gorm:"size:50" json:"continent"`                 // 所属大洲
	Region    string `gorm:"size:100" json:"region"`                   // 地区
	SubRegion string `gorm:"size:100" json:"sub_region"`               // 子地区
	IsActive  bool   `gorm:"default:true" json:"is_active"`            // 是否启用
	SortOrder int    `gorm:"default:0" json:"sort_order"`              // 排序
	Flag      string `gorm:"size:10" json:"flag"`                      // 国旗emoji
	PhoneCode string `gorm:"size:10" json:"phone_code"`                // 电话区号
	Currency  string `gorm:"size:10" json:"currency"`                  // 货币代码
	TimeZone  string `gorm:"size:50" json:"time_zone"`                 // 时区
}

// TableName 指定表名
func (Country) TableName() string {
	return "complaint_countries"
}

// TrademarkCategory 商标类型模型
type TrademarkCategory struct {
	ID        uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"-"`

	CategoryNumber int    `gorm:"uniqueIndex;not null" json:"category_number"` // 类别号(如1, 2, 3...)
	Name           string `gorm:"size:200;not null" json:"name"`               // 类别名称(如"第1类.化学原料")
	Description    string `gorm:"type:text" json:"description"`                // 类别描述
	IsActive       bool   `gorm:"default:true" json:"is_active"`               // 是否启用
	SortOrder      int    `gorm:"default:0" json:"sort_order"`                 // 排序
}

// TableName 指定表名
func (TrademarkCategory) TableName() string {
	return "complaint_trademark_categories"
}
