# PXPAT Backend 代码编写规范

## 1. 项目结构规范

### 1.1 目录结构
```
pxpat-backend/
├── cmd/                           # 应用程序入口
│   └── {cluster-name}/
│       └── {service-name}/
│           └── main.go           # 服务启动入口
├── internal/                     # 内部代码
│   └── {cluster-name}/
│       └── {service-name}/
│           ├── client/           # 外部服务客户端
│           ├── cron/            # 定时任务
│           ├── dto/             # 数据传输对象
│           ├── external/        # 对外服务层
│           │   ├── handler/     # HTTP处理器
│           │   └── service/     # 业务逻辑
│           ├── intra/           # 内部服务层
│           │   ├── handler/     # 内部HTTP处理器
│           │   └── service/     # 内部业务逻辑
│           ├── messaging/       # 消息队列
│           │   ├── consumer/    # 消费者
│           │   └── publisher/   # 发布者
│           ├── middleware/      # 中间件
│           ├── migrations/      # 数据库迁移
│           ├── model/           # 数据模型
│           ├── repository/      # 数据访问层
│           ├── routes/          # 路由定义
│           │   └── {module}/    # 模块路由
│           ├── types/           # 类型定义
│           └── utils/           # 工具函数
└── pkg/                         # 公共包
```

### 1.2 命名规范
- **集群名称**: 使用短横线分隔，如 `content-cluster`, `user-cluster`
- **服务名称**: 使用短横线分隔，如 `video-service`, `user-service`
- **包名**: 使用下划线或简短单词，如 `model`, `repository`, `external`
- **文件名**: 使用下划线分隔，如 `content_service.go`, `user_repository.go`

## 2. 代码结构规范

### 2.1 main.go 结构
```go
package main

import (
    // 标准库
    "context"
    "fmt"
    
    // 项目内部包
    "pxpat-backend/cmd"
    "pxpat-backend/internal/{cluster}/{service}/..."
    "pxpat-backend/pkg/..."
    
    // 第三方包
    "github.com/gin-gonic/gin"
    "go.uber.org/fx"
)

// 1. 适配器和接口实现
type serviceClientAdapter struct {
    baseURL string
}

// 2. 配置提供函数
func provideConfig() *types.Config { }

// 3. 基础设施提供函数
func provideLogger(cfg *types.Config) { }
func provideDatabase(cfg *types.Config) (*gorm.DB, error) { }
func provideRedis(cfg *types.Config) (*redis.Client, error) { }

// 4. 客户端提供函数
func provideUserServiceClient(cfg *types.Config) client.UserServiceClient { }

// 5. Repository层提供函数
func provideRepositories(...) (...) { }

// 6. Service层提供函数
func provideExternalServices(...) (...) { }
func provideInternalServices(...) (...) { }

// 7. 中间件和路由提供函数
func provideGinEngine(...) *gin.Engine { }

// 8. 生命周期管理
func manageLifecycle(...) { }

// 9. main函数
func main() {
    app := fx.New(
        fx.Provide(...),
        fx.Invoke(...),
    )
    app.Run()
}
```

### 2.2 Model 结构
```go
package model

import (
    "gorm.io/gorm"
    "time"
)

// 1. 枚举类型定义
type Status string

const (
    StatusDraft     Status = "draft"     // 草稿
    StatusPublished Status = "published" // 已发布
)

// 2. 结构体定义
type Entity struct {
    // 基础字段
    ID        uint      `gorm:"primaryKey" json:"id"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
    
    // 业务字段
    Name   string `gorm:"size:255;not null" json:"name"`
    Status Status `gorm:"size:50;default:'draft'" json:"status"`
}
```

### 2.3 Repository 结构
```go
package repository

import (
    "context"
    "github.com/redis/go-redis/v9"
    "gorm.io/gorm"
)

type EntityRepository struct {
    db          *gorm.DB
    rdb         *redis.Client
    cacheManage cache.Manager
}

func NewEntityRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) *EntityRepository {
    return &EntityRepository{
        db:          db,
        rdb:         rdb,
        cacheManage: cacheManage,
    }
}

// GetDB 获取数据库实例，用于事务处理
func (r *EntityRepository) GetDB() *gorm.DB {
    return r.db
}

func (r *EntityRepository) Create(ctx context.Context, entity *model.Entity) error {
    log.Debug().
        Str("entity_id", entity.ID).
        Msg("开始创建实体记录")
    
    err := r.db.WithContext(ctx).Create(entity).Error
    if err != nil {
        log.Error().
            Err(err).
            Str("entity_id", entity.ID).
            Msg("创建实体记录失败")
        return err
    }
    
    log.Debug().
        Str("entity_id", entity.ID).
        Msg("创建实体记录成功")
    
    return nil
}
```

### 2.4 Service 结构
```go
package service

import (
    "context"
    "github.com/rs/zerolog/log"
)

type EntityService struct {
    entityRepo   *repository.EntityRepository
    clientRepo   client.ClientInterface
}

func NewEntityService(
    entityRepo *repository.EntityRepository,
    clientRepo client.ClientInterface,
) *EntityService {
    return &EntityService{
        entityRepo: entityRepo,
        clientRepo: clientRepo,
    }
}

func (s *EntityService) CreateEntity(ctx context.Context, userKSUID string, req *dto.CreateEntityRequest) (*model.Entity, *errors.Errors) {
    log.Info().
        Str("user_ksuid", userKSUID).
        Str("entity_name", req.Name).
        Msg("开始创建实体")
    
    // 1. 参数验证
    if gErr := s.validateRequest(ctx, req); gErr != nil {
        return nil, gErr
    }
    
    // 2. 业务逻辑处理
    entity := &model.Entity{
        Name:   req.Name,
        Status: model.StatusDraft,
    }
    
    // 3. 数据持久化
    if err := s.entityRepo.Create(ctx, entity); err != nil {
        log.Error().
            Err(err).
            Str("user_ksuid", userKSUID).
            Msg("创建实体失败")
        return nil, errors.NewInternalError("创建实体失败")
    }
    
    log.Info().
        Str("user_ksuid", userKSUID).
        Str("entity_id", entity.ID).
        Msg("创建实体成功")
    
    return entity, nil
}
```

### 2.5 Handler 结构
```go
package handler

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "github.com/rs/zerolog/log"
)

type EntityHandler struct {
    entityService *service.EntityService
}

func NewEntityHandler(entityService *service.EntityService) *EntityHandler {
    return &EntityHandler{
        entityService: entityService,
    }
}

func (h *EntityHandler) CreateEntity(c *gin.Context) {
    // 1. 链路追踪
    ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "CreateEntity")
    defer span.End()
    c.Request = c.Request.WithContext(ctx)
    
    // 2. 获取trace信息
    traceID := tracing.GetTraceID(c)
    spanID := tracing.GetSpanID(c)
    userKSUID := ksuid.GetKSUID(c)
    
    // 3. 添加span属性
    opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
    opentelemetry.AddAttribute(span, "method", c.Request.Method)
    opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
    
    // 4. 记录请求日志
    log.Info().
        Str("trace_id", traceID).
        Str("span_id", spanID).
        Str("user_ksuid", userKSUID).
        Str("method", c.Request.Method).
        Str("path", c.Request.URL.Path).
        Msg("收到创建实体请求")
    
    // 5. 参数绑定
    var req dto.CreateEntityRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        log.Error().
            Str("trace_id", traceID).
            Err(err).
            Msg("参数绑定失败")
        
        opentelemetry.AddError(span, err)
        c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
            Code: errors.INVALID_PARAMETER,
        })
        return
    }
    
    // 6. 调用服务层
    entity, gErr := h.entityService.CreateEntity(ctx, userKSUID, &req)
    if gErr != nil {
        log.Error().
            Str("trace_id", traceID).
            Interface("error", gErr).
            Msg("创建实体失败")
        
        opentelemetry.AddError(span, gErr)
        c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
            Code: gErr.Code,
            Msg:  gErr.Message,
        })
        return
    }
    
    // 7. 返回成功响应
    log.Info().
        Str("trace_id", traceID).
        Str("entity_id", entity.ID).
        Msg("创建实体成功")
    
    c.JSON(http.StatusOK, globalTypes.GlobalResponse{
        Code: errors.SUCCESS,
        Data: dto.EntityToExportModel(entity),
    })
}
```

### 2.6 DTO 结构
```go
package dto

import (
    "pxpat-backend/internal/{cluster}/{service}/model"
    "pxpat-backend/pkg/errors"
    "time"
)

// 请求DTO
type CreateEntityRequest struct {
    Name        string `json:"name" binding:"required"`
    Description string `json:"description"`
    Type        string `json:"type" binding:"required"`
}

// 响应DTO
type ExportEntityModel struct {
    ID          uint      `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Status      string    `json:"status"`
    CreatedAt   time.Time `json:"created_at"`
}

// 验证函数
func ValidateCreateEntityRequest(req *CreateEntityRequest) *errors.Errors {
    if req.Name == "" {
        return errors.NewValidationError("名称不能为空")
    }
    if len(req.Name) > 255 {
        return errors.NewValidationError("名称长度不能超过255个字符")
    }
    return nil
}

// 转换函数
func EntityToExportModel(entity *model.Entity) *ExportEntityModel {
    return &ExportEntityModel{
        ID:          entity.ID,
        Name:        entity.Name,
        Description: entity.Description,
        Status:      string(entity.Status),
        CreatedAt:   entity.CreatedAt,
    }
}
```

### 2.7 Client 结构
```go
package client

import (
    "pxpat-backend/pkg/httpclient"
    "time"
)

// 接口定义
type EntityServiceClient interface {
    GetEntity(entityID string) (*EntityInfo, error)
    CreateEntity(req *CreateEntityRequest) (*EntityInfo, error)
    UpdateEntity(entityID string, req *UpdateEntityRequest) (*EntityInfo, error)
    DeleteEntity(entityID string) error
}

// 实现结构
type entityServiceClient struct {
    httpClient *httpclient.HTTPClient
}

// 配置结构
type EntityServiceConfig struct {
    BaseURL string
    Timeout time.Duration
}

// 构造函数
func NewEntityServiceClient(config EntityServiceConfig) EntityServiceClient {
    httpClient := httpclient.NewHTTPClient(httpclient.ClientConfig{
        BaseURL:          config.BaseURL,
        Timeout:          config.Timeout,
        RetryCount:       3,
        RetryWaitTime:    1 * time.Second,
        RetryMaxWaitTime: 5 * time.Second,
    })
    return &entityServiceClient{
        httpClient: httpClient,
    }
}

// 方法实现
func (c *entityServiceClient) GetEntity(entityID string) (*EntityInfo, error) {
    var response EntityInfo
    err := c.httpClient.Get(fmt.Sprintf("/api/v1/entity/%s", entityID), nil, &response)
    if err != nil {
        log.Error().
            Err(err).
            Str("entity_id", entityID).
            Msg("获取实体信息失败")
        return nil, err
    }
    return &response, nil
}
```

### 2.8 Routes 结构
```go
// router.go - 主路由注册
package routes

import (
    "github.com/gin-gonic/gin"
    externalHandler "pxpat-backend/internal/{cluster}/{service}/external/handler"
    intraHandler "pxpat-backend/internal/{cluster}/{service}/intra/handler"
)

func RegisterRoutes(
    router *gin.Engine,
    externalEntityHandler *externalHandler.EntityHandler,
    internalEntityHandler *intraHandler.EntityHandler,
    authMiddleware gin.HandlerFunc,
) {
    // 创建base路由
    v1 := router.Group("/api/v1")

    // 注册模块路由
    entity.RegisterEntityRouter(v1, externalEntityHandler, internalEntityHandler, authMiddleware)

    // Swagger 文档路由
    router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}

// entity/router.go - 模块路由
package entity

func RegisterEntityRouter(r *gin.RouterGroup, externalHandler *externalHandler.EntityHandler, internalHandler *intraHandler.EntityHandler, authMiddleware gin.HandlerFunc) {
    // 注册外部API路由
    RegisterEntityExternalRoutes(r, externalHandler, authMiddleware)

    // 注册内部服务路由
    RegisterEntityInternalRoutes(r, internalHandler)
}

// entity/external.go - 外部路由
func RegisterEntityExternalRoutes(r *gin.RouterGroup, handler *externalHandler.EntityHandler, authMiddleware gin.HandlerFunc) {
    entityGroup := r.Group("/entity")
    {
        needAuth := entityGroup.Group("")
        needAuth.Use(authMiddleware)

        // 公开接口（无需认证）
        entityGroup.GET("/public", handler.GetPublicEntities)

        // 需要认证的接口
        needAuth.POST("/", handler.CreateEntity)
        needAuth.GET("/:entity_id", handler.GetEntity)
        needAuth.PUT("/:entity_id", handler.UpdateEntity)
        needAuth.DELETE("/:entity_id", handler.DeleteEntity)
    }
}

// entity/internal.go - 内部路由
func RegisterEntityInternalRoutes(r *gin.RouterGroup, handler *intraHandler.EntityHandler) {
    internalGroup := r.Group("/internal/entity")
    {
        internalGroup.GET("/:entity_id", handler.GetEntity)
        internalGroup.POST("/batch", handler.BatchCreateEntities)
    }
}
```

## 3. 编码规范

### 3.1 导入规范
```go
import (
    // 1. 标准库
    "context"
    "fmt"
    "time"

    // 2. 项目内部包（按层级排序）
    "pxpat-backend/cmd"
    "pxpat-backend/internal/{cluster}/{service}/client"
    "pxpat-backend/internal/{cluster}/{service}/dto"
    "pxpat-backend/pkg/auth"
    "pxpat-backend/pkg/cache"

    // 3. 第三方包（按字母排序）
    "github.com/gin-gonic/gin"
    "github.com/redis/go-redis/v9"
    "github.com/rs/zerolog/log"
    "go.uber.org/fx"
    "gorm.io/gorm"
)
```

### 3.2 命名规范
- **变量名**: 使用驼峰命名法，如 `userKSUID`, `contentService`
- **常量名**: 使用大写字母和下划线，如 `STATUS_PUBLISHED`, `MAX_RETRY_COUNT`
- **函数名**: 使用驼峰命名法，公开函数首字母大写，如 `CreateEntity`, `validateRequest`
- **结构体名**: 使用驼峰命名法，首字母大写，如 `EntityService`, `ContentRepository`
- **接口名**: 通常以接口功能命名，如 `EntityServiceClient`, `Repository`

### 3.3 注释规范
```go
// Package service 提供实体相关的业务逻辑处理
package service

// EntityService 实体服务，负责处理实体相关的业务逻辑
type EntityService struct {
    entityRepo *repository.EntityRepository // 实体数据访问层
    userClient client.UserServiceClient     // 用户服务客户端
}

// NewEntityService 创建实体服务实例
// 参数:
//   - entityRepo: 实体数据访问层实例
//   - userClient: 用户服务客户端实例
// 返回:
//   - *EntityService: 实体服务实例
func NewEntityService(entityRepo *repository.EntityRepository, userClient client.UserServiceClient) *EntityService {
    return &EntityService{
        entityRepo: entityRepo,
        userClient: userClient,
    }
}

// CreateEntity 创建新实体
// 该方法会验证用户权限，检查实体名称唯一性，然后创建实体记录
func (s *EntityService) CreateEntity(ctx context.Context, userKSUID string, req *dto.CreateEntityRequest) (*model.Entity, *errors.Errors) {
    // 实现逻辑...
}
```

### 3.4 错误处理规范
```go
// 1. 使用项目统一的错误类型
func (s *EntityService) CreateEntity(ctx context.Context, req *dto.CreateEntityRequest) (*model.Entity, *errors.Errors) {
    // 参数验证错误
    if req.Name == "" {
        return nil, errors.NewValidationError("实体名称不能为空")
    }

    // 业务逻辑错误
    if exists, err := s.entityRepo.ExistsByName(ctx, req.Name); err != nil {
        log.Error().Err(err).Msg("检查实体名称唯一性失败")
        return nil, errors.NewInternalError("系统内部错误")
    } else if exists {
        return nil, errors.NewBusinessError("实体名称已存在")
    }

    // 数据库操作错误
    entity := &model.Entity{Name: req.Name}
    if err := s.entityRepo.Create(ctx, entity); err != nil {
        log.Error().Err(err).Msg("创建实体失败")
        return nil, errors.NewInternalError("创建实体失败")
    }

    return entity, nil
}

// 2. Handler层错误处理
func (h *EntityHandler) CreateEntity(c *gin.Context) {
    entity, gErr := h.entityService.CreateEntity(ctx, userKSUID, &req)
    if gErr != nil {
        // 根据错误类型返回不同的HTTP状态码
        var statusCode int
        switch gErr.Type {
        case errors.ValidationError:
            statusCode = http.StatusBadRequest
        case errors.BusinessError:
            statusCode = http.StatusConflict
        case errors.AuthError:
            statusCode = http.StatusUnauthorized
        default:
            statusCode = http.StatusInternalServerError
        }

        c.JSON(statusCode, globalTypes.GlobalResponse{
            Code: gErr.Code,
            Msg:  gErr.Message,
        })
        return
    }

    c.JSON(http.StatusOK, globalTypes.GlobalResponse{
        Code: errors.SUCCESS,
        Data: dto.EntityToExportModel(entity),
    })
}
```

### 3.5 日志规范
```go
import "github.com/rs/zerolog/log"

// 1. 结构化日志
log.Info().
    Str("user_ksuid", userKSUID).
    Str("entity_id", entityID).
    Str("operation", "create_entity").
    Msg("开始创建实体")

// 2. 错误日志
log.Error().
    Err(err).
    Str("user_ksuid", userKSUID).
    Str("entity_name", req.Name).
    Msg("创建实体失败")

// 3. 调试日志
log.Debug().
    Interface("request", req).
    Str("trace_id", traceID).
    Msg("收到创建实体请求")

// 4. 警告日志
log.Warn().
    Str("user_ksuid", userKSUID).
    Int("retry_count", retryCount).
    Msg("重试次数过多")
```

### 3.6 链路追踪规范
```go
import (
    "pxpat-backend/pkg/middleware/tracing"
    "pxpat-backend/pkg/opentelemetry"
)

func (h *EntityHandler) CreateEntity(c *gin.Context) {
    // 1. 创建span
    ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "CreateEntity")
    defer span.End()
    c.Request = c.Request.WithContext(ctx)

    // 2. 获取trace信息
    traceID := tracing.GetTraceID(c)
    spanID := tracing.GetSpanID(c)

    // 3. 添加span属性
    opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
    opentelemetry.AddAttribute(span, "method", c.Request.Method)
    opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

    // 4. 添加事件
    opentelemetry.AddEvent(span, "request_start")
    opentelemetry.AddEvent(span, "parameter_binding_success")

    // 5. 错误处理
    if err != nil {
        opentelemetry.AddError(span, err)
        opentelemetry.AddEvent(span, "operation_failed")
    }
}
```

## 4. 数据库规范

### 4.1 Model定义规范
```go
type Entity struct {
    // 1. 基础字段（必须）
    ID        uint           `gorm:"primaryKey" json:"id"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

    // 2. KSUID字段（如果需要）
    EntityKSUID string `gorm:"size:27;uniqueIndex;not null" json:"entity_ksuid"`

    // 3. 业务字段
    Name        string `gorm:"size:255;not null" json:"name"`
    Description string `gorm:"type:text" json:"description"`
    Status      Status `gorm:"size:50;default:'draft'" json:"status"`

    // 4. 外键字段
    UserKSUID  string `gorm:"size:27;not null;index" json:"user_ksuid"`
    CategoryID uint   `gorm:"not null;index" json:"category_id"`

    // 5. 关联关系
    Category Category `gorm:"foreignKey:CategoryID" json:"category,omitempty"`
    Tags     []Tag    `gorm:"many2many:entity_tags;" json:"tags,omitempty"`
}

// 表名定义
func (Entity) TableName() string {
    return "entities"
}
```

### 4.2 Migration规范
```go
package migrations

import (
    "gorm.io/gorm"
    "pxpat-backend/internal/{cluster}/{service}/model"
)

// AutoMigrate 自动迁移所有模型
func AutoMigrate(db *gorm.DB) error {
    return db.AutoMigrate(
        &model.Entity{},
        &model.Category{},
        &model.Tag{},
        // 按依赖关系排序
    )
}

// MigrateEntity 迁移实体表
func MigrateEntity(db *gorm.DB) error {
    if err := db.AutoMigrate(&model.Entity{}); err != nil {
        return err
    }

    // 创建索引
    if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_entities_user_ksuid_status ON entities(user_ksuid, status)").Error; err != nil {
        return err
    }

    return nil
}
```

## 5. 配置规范

### 5.1 Config结构
```go
package types

import (
    globalTypes "pxpat-backend/pkg/types"
    "time"
)

// Config 服务配置结构
type Config struct {
    Server     globalTypes.GlobalServerConfig   `mapstructure:"server"`
    Database   globalTypes.GlobalDatabaseConfig `mapstructure:"database"`
    Redis      globalTypes.GlobalRedisConfig    `mapstructure:"redis"`
    RabbitMQ   globalTypes.GlobalRabbitMQConfig `mapstructure:"rabbitmq"`
    JWT        globalTypes.GlobalJWTConfig      `mapstructure:"jwt"`
    Log        globalTypes.GlobalLogConfig      `mapstructure:"log"`
    Consul     globalTypes.GlobalConsulConfig   `mapstructure:"consul"`

    // 业务配置
    Entity     EntityConfig     `mapstructure:"entity"`
    Storage    StorageConfig    `mapstructure:"storage"`
    Security   SecurityConfig   `mapstructure:"security"`
    Monitoring MonitoringConfig `mapstructure:"monitoring"`
}

// EntityConfig 实体配置
type EntityConfig struct {
    Limits LimitsConfig `mapstructure:"limits"`
    Cache  CacheConfig  `mapstructure:"cache"`
}

// LimitsConfig 限制配置
type LimitsConfig struct {
    NameMaxLength        int `mapstructure:"name_max_length"`
    DescriptionMaxLength int `mapstructure:"description_max_length"`
    MaxEntitiesPerUser   int `mapstructure:"max_entities_per_user"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
    EntityTTL   time.Duration `mapstructure:"entity_ttl"`
    CategoryTTL time.Duration `mapstructure:"category_ttl"`
}
```

## 6. 测试规范

### 6.1 单元测试
```go
package service_test

import (
    "context"
    "testing"
    "time"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "pxpat-backend/internal/{cluster}/{service}/dto"
    "pxpat-backend/internal/{cluster}/{service}/model"
    "pxpat-backend/internal/{cluster}/{service}/service"
    "pxpat-backend/pkg/errors"
)

// MockEntityRepository 模拟实体仓库
type MockEntityRepository struct {
    mock.Mock
}

func (m *MockEntityRepository) Create(ctx context.Context, entity *model.Entity) error {
    args := m.Called(ctx, entity)
    return args.Error(0)
}

func (m *MockEntityRepository) ExistsByName(ctx context.Context, name string) (bool, error) {
    args := m.Called(ctx, name)
    return args.Bool(0), args.Error(1)
}

func TestEntityService_CreateEntity(t *testing.T) {
    // 准备测试数据
    ctx := context.Background()
    userKSUID := "test_user_ksuid"
    req := &dto.CreateEntityRequest{
        Name:        "测试实体",
        Description: "测试描述",
        Type:        "test",
    }

    // 创建模拟对象
    mockRepo := new(MockEntityRepository)
    mockUserClient := new(MockUserServiceClient)

    // 设置期望调用
    mockRepo.On("ExistsByName", ctx, req.Name).Return(false, nil)
    mockRepo.On("Create", ctx, mock.AnythingOfType("*model.Entity")).Return(nil)

    // 创建服务实例
    entityService := service.NewEntityService(mockRepo, mockUserClient)

    // 执行测试
    entity, gErr := entityService.CreateEntity(ctx, userKSUID, req)

    // 验证结果
    assert.Nil(t, gErr)
    assert.NotNil(t, entity)
    assert.Equal(t, req.Name, entity.Name)
    assert.Equal(t, req.Description, entity.Description)

    // 验证模拟对象调用
    mockRepo.AssertExpectations(t)
}

func TestEntityService_CreateEntity_NameExists(t *testing.T) {
    ctx := context.Background()
    userKSUID := "test_user_ksuid"
    req := &dto.CreateEntityRequest{
        Name: "已存在的实体",
        Type: "test",
    }

    mockRepo := new(MockEntityRepository)
    mockUserClient := new(MockUserServiceClient)

    // 设置名称已存在
    mockRepo.On("ExistsByName", ctx, req.Name).Return(true, nil)

    entityService := service.NewEntityService(mockRepo, mockUserClient)

    // 执行测试
    entity, gErr := entityService.CreateEntity(ctx, userKSUID, req)

    // 验证结果
    assert.Nil(t, entity)
    assert.NotNil(t, gErr)
    assert.Equal(t, errors.BusinessError, gErr.Type)

    mockRepo.AssertExpectations(t)
}
```

### 6.2 集成测试
```go
package integration_test

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"

    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
    "pxpat-backend/internal/{cluster}/{service}/dto"
)

func TestCreateEntityAPI(t *testing.T) {
    // 设置测试环境
    gin.SetMode(gin.TestMode)
    router := setupTestRouter()

    // 准备测试数据
    req := dto.CreateEntityRequest{
        Name:        "测试实体",
        Description: "测试描述",
        Type:        "test",
    }

    jsonData, _ := json.Marshal(req)

    // 创建HTTP请求
    w := httptest.NewRecorder()
    httpReq, _ := http.NewRequest("POST", "/api/v1/entity", bytes.NewBuffer(jsonData))
    httpReq.Header.Set("Content-Type", "application/json")
    httpReq.Header.Set("Authorization", "Bearer "+getTestToken())

    // 执行请求
    router.ServeHTTP(w, httpReq)

    // 验证响应
    assert.Equal(t, http.StatusOK, w.Code)

    var response globalTypes.GlobalResponse
    err := json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(t, err)
    assert.Equal(t, errors.SUCCESS, response.Code)
}
```

## 7. 性能规范

### 7.1 缓存使用
```go
// 1. Repository层缓存
func (r *EntityRepository) GetByID(ctx context.Context, id uint) (*model.Entity, error) {
    cacheKey := fmt.Sprintf("entity:%d", id)

    // 尝试从缓存获取
    var entity model.Entity
    if err := r.cacheManage.Get(ctx, cacheKey, &entity); err == nil {
        return &entity, nil
    }

    // 从数据库获取
    if err := r.db.WithContext(ctx).First(&entity, id).Error; err != nil {
        return nil, err
    }

    // 写入缓存
    r.cacheManage.Set(ctx, cacheKey, &entity, 30*time.Minute)

    return &entity, nil
}

// 2. 批量操作优化
func (r *EntityRepository) BatchGetByIDs(ctx context.Context, ids []uint) ([]*model.Entity, error) {
    if len(ids) == 0 {
        return []*model.Entity{}, nil
    }

    var entities []*model.Entity
    err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&entities).Error
    return entities, err
}
```

### 7.2 数据库优化
```go
// 1. 预加载关联数据
func (r *EntityRepository) GetWithRelations(ctx context.Context, id uint) (*model.Entity, error) {
    var entity model.Entity
    err := r.db.WithContext(ctx).
        Preload("Category").
        Preload("Tags").
        First(&entity, id).Error
    return &entity, err
}

// 2. 分页查询
func (r *EntityRepository) GetPaginated(ctx context.Context, page, pageSize int) ([]*model.Entity, int64, error) {
    var entities []*model.Entity
    var total int64

    // 获取总数
    if err := r.db.WithContext(ctx).Model(&model.Entity{}).Count(&total).Error; err != nil {
        return nil, 0, err
    }

    // 分页查询
    offset := (page - 1) * pageSize
    err := r.db.WithContext(ctx).
        Offset(offset).
        Limit(pageSize).
        Find(&entities).Error

    return entities, total, err
}
```

## 8. 安全规范

### 8.1 输入验证
```go
// 1. DTO验证
func ValidateCreateEntityRequest(req *CreateEntityRequest) *errors.Errors {
    // 必填字段验证
    if req.Name == "" {
        return errors.NewValidationError("名称不能为空")
    }

    // 长度验证
    if len(req.Name) > 255 {
        return errors.NewValidationError("名称长度不能超过255个字符")
    }

    // 格式验证
    if matched, _ := regexp.MatchString(`^[a-zA-Z0-9\u4e00-\u9fa5_-]+$`, req.Name); !matched {
        return errors.NewValidationError("名称包含非法字符")
    }

    return nil
}

// 2. SQL注入防护（使用GORM参数化查询）
func (r *EntityRepository) GetByName(ctx context.Context, name string) (*model.Entity, error) {
    var entity model.Entity
    // 正确：使用参数化查询
    err := r.db.WithContext(ctx).Where("name = ?", name).First(&entity).Error
    return &entity, err
}
```

### 8.2 权限控制
```go
// 1. 中间件权限检查
func (m *PermissionMiddleware) CheckEntityPermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userKSUID := ksuid.GetKSUID(c)

        hasPermission, err := m.checkUserPermission(c.Request.Context(), userKSUID, permission)
        if err != nil {
            c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
                Code: errors.INTERNAL_ERROR,
                Msg:  "权限检查失败",
            })
            c.Abort()
            return
        }

        if !hasPermission {
            c.JSON(http.StatusForbidden, globalTypes.GlobalResponse{
                Code: errors.PERMISSION_DENIED,
                Msg:  "权限不足",
            })
            c.Abort()
            return
        }

        c.Next()
    }
}

// 2. 资源所有权验证
func (s *EntityService) UpdateEntity(ctx context.Context, userKSUID string, entityID uint, req *dto.UpdateEntityRequest) (*model.Entity, *errors.Errors) {
    // 获取实体
    entity, err := s.entityRepo.GetByID(ctx, entityID)
    if err != nil {
        return nil, errors.NewNotFoundError("实体不存在")
    }

    // 检查所有权
    if entity.UserKSUID != userKSUID {
        return nil, errors.NewPermissionError("无权限修改此实体")
    }

    // 执行更新...
    return entity, nil
}
```

## 9. 部署和运维规范

### 9.1 健康检查
```go
// 健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB, rdb *redis.Client) *consul.HealthHandler {
    healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

    // 添加数据库健康检查
    healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
        sqlDB, err := db.DB()
        if err != nil {
            return err
        }
        return sqlDB.Ping()
    }))

    // 添加Redis健康检查
    healthHandler.AddChecker(health.NewRedisHealthChecker("redis", func() error {
        return rdb.Ping(context.Background()).Err()
    }))

    return healthHandler
}
```

### 9.2 优雅关闭
```go
// 生命周期管理
func manageLifecycle(
    lc fx.Lifecycle,
    cfg *types.Config,
    consulManager *consul.Manager,
    mqPublisher *publisher.Publisher,
    ginEngine *gin.Engine,
) {
    lc.Append(fx.Hook{
        OnStart: func(ctx context.Context) error {
            // 启动服务
            if err := consulManager.Start(ctx); err != nil {
                return err
            }

            go func() {
                cmd.GraceStartAndClose(cfg.Server, ginEngine)
            }()

            return nil
        },
        OnStop: func(ctx context.Context) error {
            // 优雅关闭
            if err := consulManager.Stop(); err != nil {
                log.Error().Err(err).Msg("停止Consul管理器失败")
            }

            if mqPublisher != nil {
                if err := mqPublisher.Close(); err != nil {
                    log.Error().Err(err).Msg("关闭MQ发布器失败")
                }
            }

            return nil
        },
    })
}
```

## 10. 代码质量要求

### 10.1 代码审查清单
- [ ] 代码结构符合项目规范
- [ ] 命名规范清晰易懂
- [ ] 错误处理完整
- [ ] 日志记录充分
- [ ] 单元测试覆盖率 > 80%
- [ ] 性能考虑（缓存、分页等）
- [ ] 安全验证（输入验证、权限检查）
- [ ] 文档注释完整

### 10.2 提交规范
```
feat: 添加实体管理功能
fix: 修复实体查询缓存问题
docs: 更新API文档
style: 代码格式调整
refactor: 重构实体服务层
test: 添加实体服务单元测试
chore: 更新依赖版本
```

---

**注意事项:**
1. 所有代码必须通过单元测试
2. 新功能需要添加对应的API文档
3. 数据库变更需要提供迁移脚本
4. 性能敏感的操作需要添加缓存
5. 对外接口需要进行权限验证
6. 错误信息不能暴露内部实现细节
7. 日志中不能包含敏感信息（密码、token等）
```
```
