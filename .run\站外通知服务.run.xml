<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="站外通知服务" type="GoApplicationRunConfiguration" factoryName="Go Application" folderName="通知组">
    <module name="pxpat-backend" />
    <working_directory value="$PROJECT_DIR$" />
    <kind value="PACKAGE" />
    <package value="pxpat-backend/cmd/notify-cluster/external-service" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/notify-cluster/external-service/main.go" />
    <method v="2" />
  </configuration>
</component>