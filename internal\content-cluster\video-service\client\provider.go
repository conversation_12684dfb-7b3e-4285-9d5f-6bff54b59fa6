package client

import (
	"fmt"

	"pxpat-backend/internal/content-cluster/video-service/types"
	"pxpat-backend/pkg/storage"

	"github.com/rs/zerolog/log"
)

// ProvideUserServiceClient 提供用户服务客户端
func ProvideUserServiceClient(cfg *types.Config) UserServiceClient {
	userServiceClient := NewUserServiceClient(UserServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.UserService.Host, cfg.Server.AllServiceList.UserService.Port),
		Timeout: cfg.Server.AllServiceList.UserService.Timeout,
	})
	log.Info().Msg("User service client initialized successfully")
	return userServiceClient
}

// ProvideInteractionServiceClient 提供交互服务客户端
func ProvideInteractionServiceClient(cfg *types.Config) InteractionServiceClient {
	interactionServiceClient := NewInteractionServiceClient(InteractionServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.InteractionService.Host, cfg.Server.AllServiceList.InteractionService.Port),
		Timeout: cfg.Server.AllServiceList.InteractionService.Timeout,
	})
	log.Info().Msg("Interaction service client initialized successfully")
	return interactionServiceClient
}

// ProvideAuditServiceClient 提供审核服务客户端
func ProvideAuditServiceClient(cfg *types.Config) AuditServiceClient {
	auditServiceClient := NewAuditServiceClient(AuditServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.AuditService.Host, cfg.Server.AllServiceList.AuditService.Port),
		Timeout: cfg.Server.AllServiceList.AuditService.Timeout,
	})
	log.Info().Msg("Audit service client initialized successfully")
	return auditServiceClient
}

// ProvideStorageClient 提供存储客户端
func ProvideStorageClient(cfg *types.Config) storage.StorageClient {
	var storageClient storage.StorageClient
	if cfg.Storage.Minio.Provider != "" {
		var err error
		storageClient, err = storage.NewStorageClient(cfg.Storage.Minio)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize storage client")
			log.Info().Msg("Continuing without storage client...")
		} else {
			log.Info().Str("provider", cfg.Storage.Minio.Provider).Msg("Storage client initialized successfully")
		}
	} else {
		log.Info().Msg("Storage provider not configured, skipping storage client initialization")
	}
	return storageClient
}
