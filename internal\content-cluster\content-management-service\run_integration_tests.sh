#!/bin/bash

# 内容管理服务集成测试运行脚本
# 用于运行所有集成测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_ROOT="$PROJECT_ROOT"

echo -e "${BLUE}=== 内容管理服务集成测试 ===${NC}"
echo "项目路径: $SERVICE_ROOT"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: Go未安装或不在PATH中${NC}"
    exit 1
fi

echo -e "${YELLOW}Go版本:${NC}"
go version
echo ""

# 进入服务目录
cd "$SERVICE_ROOT"

# 清理之前的测试结果
echo -e "${YELLOW}清理之前的测试结果...${NC}"
rm -f integration_coverage.out integration_coverage.html
rm -rf integration_test_results/
mkdir -p integration_test_results

# 下载依赖
echo -e "${YELLOW}下载测试依赖...${NC}"
go mod tidy
go mod download

# 检查测试环境
echo -e "${YELLOW}检查测试环境...${NC}"

# 检查Redis连接
REDIS_AVAILABLE=false
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo -e "${GREEN}✓ Redis连接正常${NC}"
        REDIS_AVAILABLE=true
    else
        echo -e "${YELLOW}⚠ Redis未运行${NC}"
    fi
else
    echo -e "${YELLOW}⚠ Redis CLI未安装${NC}"
fi

# 如果Redis不可用，询问是否继续
if [ "$REDIS_AVAILABLE" = false ]; then
    echo -e "${YELLOW}Redis不可用，某些集成测试可能会被跳过。${NC}"
    read -p "是否继续运行集成测试? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}集成测试已取消${NC}"
        exit 0
    fi
fi

# 设置测试环境变量
export GO_ENV=integration_test
export GIN_MODE=test

# 启动测试前的准备
echo -e "${YELLOW}准备集成测试环境...${NC}"

# 清理Redis测试数据库（如果可用）
if [ "$REDIS_AVAILABLE" = true ]; then
    redis-cli -n 15 FLUSHDB > /dev/null 2>&1 || true
    echo -e "${GREEN}✓ Redis测试数据库已清理${NC}"
fi

# 运行集成测试
echo -e "${YELLOW}运行集成测试...${NC}"
echo ""

# 设置测试超时时间
TEST_TIMEOUT="10m"

# 运行集成测试
echo -e "${BLUE}运行API集成测试...${NC}"
go test -v -race -timeout=$TEST_TIMEOUT -coverprofile=integration_test_results/integration_coverage.out ./integration_test/... || {
    echo -e "${RED}✗ 集成测试失败${NC}"
    
    # 显示失败的测试详情
    echo ""
    echo -e "${YELLOW}=== 测试失败详情 ===${NC}"
    go test -v ./integration_test/... 2>&1 | grep -E "(FAIL|ERROR|panic)" || true
    
    exit 1
}

echo -e "${GREEN}✓ 集成测试通过${NC}"
echo ""

# 生成覆盖率报告
if [ -f "integration_test_results/integration_coverage.out" ]; then
    echo -e "${YELLOW}生成集成测试覆盖率报告...${NC}"
    go tool cover -html=integration_test_results/integration_coverage.out -o integration_coverage.html
    
    # 计算覆盖率
    COVERAGE=$(go tool cover -func=integration_test_results/integration_coverage.out | grep total | awk '{print $3}')
    echo ""
    echo -e "${BLUE}=== 集成测试结果汇总 ===${NC}"
    echo -e "集成测试覆盖率: ${GREEN}$COVERAGE${NC}"
    
    # 显示详细覆盖率信息
    echo ""
    echo -e "${BLUE}=== 详细覆盖率信息 ===${NC}"
    go tool cover -func=integration_test_results/integration_coverage.out | grep -E "(handler|service)" | head -10
else
    echo -e "${YELLOW}⚠ 未生成覆盖率报告${NC}"
fi

# 运行端到端测试场景
echo ""
echo -e "${BLUE}=== 端到端测试场景 ===${NC}"

# 场景1: 完整的内容管理流程
echo -e "${YELLOW}场景1: 完整的内容管理流程${NC}"
go test -v -run "TestE2E_ContentManagementFlow" ./integration_test/... || {
    echo -e "${YELLOW}⚠ 端到端场景1测试失败或未实现${NC}"
}

# 场景2: 统计分析流程
echo -e "${YELLOW}场景2: 统计分析流程${NC}"
go test -v -run "TestE2E_StatsAnalysisFlow" ./integration_test/... || {
    echo -e "${YELLOW}⚠ 端到端场景2测试失败或未实现${NC}"
}

# 场景3: 批量操作流程
echo -e "${YELLOW}场景3: 批量操作流程${NC}"
go test -v -run "TestE2E_BatchOperationFlow" ./integration_test/... || {
    echo -e "${YELLOW}⚠ 端到端场景3测试失败或未实现${NC}"
}

# 性能测试（可选）
echo ""
echo -e "${BLUE}=== 性能测试 ===${NC}"
echo -e "${YELLOW}运行基础性能测试...${NC}"
go test -bench=. -benchtime=5s -benchmem ./integration_test/... || {
    echo -e "${YELLOW}⚠ 性能测试失败或未实现${NC}"
}

# 清理测试环境
echo ""
echo -e "${YELLOW}清理测试环境...${NC}"
if [ "$REDIS_AVAILABLE" = true ]; then
    redis-cli -n 15 FLUSHDB > /dev/null 2>&1 || true
    echo -e "${GREEN}✓ Redis测试数据已清理${NC}"
fi

echo ""
echo -e "${GREEN}=== 集成测试完成 ===${NC}"
echo "集成测试覆盖率报告: integration_coverage.html"
echo "详细结果: integration_test_results/"
echo ""

# 生成测试报告摘要
echo -e "${BLUE}=== 测试报告摘要 ===${NC}"
echo "测试类型: 集成测试"
echo "测试时间: $(date)"
echo "测试环境: Go $(go version | awk '{print $3}')"
echo "Redis状态: $([ "$REDIS_AVAILABLE" = true ] && echo "可用" || echo "不可用")"

if [ -f "integration_test_results/integration_coverage.out" ]; then
    echo "覆盖率: $COVERAGE"
fi

echo ""

# 可选：打开覆盖率报告
if [ -f "integration_coverage.html" ]; then
    if command -v xdg-open &> /dev/null; then
        read -p "是否打开集成测试覆盖率报告? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            xdg-open integration_coverage.html
        fi
    elif command -v open &> /dev/null; then
        read -p "是否打开集成测试覆盖率报告? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            open integration_coverage.html
        fi
    fi
fi

echo -e "${GREEN}集成测试脚本执行完成！${NC}"
