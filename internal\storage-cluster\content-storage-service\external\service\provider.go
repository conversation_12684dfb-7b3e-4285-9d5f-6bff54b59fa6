package service

import (
	"pxpat-backend/internal/storage-cluster/content-storage-service/client"
	"pxpat-backend/internal/storage-cluster/content-storage-service/repository"
	"pxpat-backend/internal/storage-cluster/content-storage-service/types"

	"github.com/rs/zerolog/log"
)

// ProvideVideoService 提供视频服务
func ProvideVideoService(cfg *types.Config, videoRepo repository.VideoRepository, contentStorageClient client.ContentStorageClient) *VideoService {
	service := NewVideoService(cfg, videoRepo, contentStorageClient.StorageClient)
	log.Info().Msg("外部视频服务初始化成功")
	return service
}

// ProvideCoverService 提供封面服务
func ProvideCoverService(cfg *types.Config, coverRepo repository.CoverRepository, coverStorageClient client.CoverStorageClient) *CoverService {
	service := NewCoverService(cfg, coverRepo, coverStorageClient.StorageClient)
	log.Info().Msg("外部封面服务初始化成功")
	return service
}
