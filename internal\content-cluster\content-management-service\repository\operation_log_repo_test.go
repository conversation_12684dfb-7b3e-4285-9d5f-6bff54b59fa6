package repository

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"pxpat-backend/internal/content-cluster/content-management-service/model"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

func TestOperationLogRepository_Create(t *testing.T) {
	testDB := SetupTestDB(t)
	defer testDB.TeardownTestDB()

	repo := NewOperationLogRepository(testDB.DB, testDB.Cache)
	ctx := context.Background()

	log := &model.OperationLog{
		UserKSUID:     "user_123",
		Operation:     "create_content",
		ResourceType:  "video",
		ResourceKSUID: "video_123",
		Details: map[string]interface{}{
			"title":  "测试视频",
			"status": "published",
		},
		IPAddress: "***********",
		UserAgent: "Mozilla/5.0",
		Status:    "success",
	}

	err := repo.Create(ctx, log)
	require.NoError(t, err)
	assert.NotZero(t, log.ID)
	assert.NotZero(t, log.CreatedAt)

	// 验证数据库中的记录
	var dbLog model.OperationLog
	err = testDB.DB.First(&dbLog, log.ID).Error
	require.NoError(t, err)
	assert.Equal(t, log.UserKSUID, dbLog.UserKSUID)
	assert.Equal(t, log.Operation, dbLog.Operation)
	assert.Equal(t, log.ResourceType, dbLog.ResourceType)
	assert.Equal(t, log.ResourceKSUID, dbLog.ResourceKSUID)
}

func TestOperationLogRepository_GetByID(t *testing.T) {
	testDB := SetupTestDB(t)
	defer testDB.TeardownTestDB()

	repo := NewOperationLogRepository(testDB.DB, testDB.Cache)
	ctx := context.Background()

	// 创建测试数据
	originalLog := testDB.CreateTestOperationLog("user_123", "test_operation")

	// 测试获取存在的记录
	log, err := repo.GetByID(ctx, originalLog.ID)
	require.NoError(t, err)
	require.NotNil(t, log)
	assert.Equal(t, originalLog.ID, log.ID)
	assert.Equal(t, originalLog.UserKSUID, log.UserKSUID)
	assert.Equal(t, originalLog.Operation, log.Operation)

	// 测试获取不存在的记录
	log, err = repo.GetByID(ctx, 99999)
	assert.Error(t, err)
	assert.Nil(t, log)
}

func TestOperationLogRepository_GetByFilters(t *testing.T) {
	testDB := SetupTestDB(t)
	defer testDB.TeardownTestDB()

	repo := NewOperationLogRepository(testDB.DB, testDB.Cache)
	ctx := context.Background()

	// 创建测试数据
	testDB.CreateTestOperationLog("user_123", "create_content")
	testDB.CreateTestOperationLog("user_123", "update_content")
	testDB.CreateTestOperationLog("user_456", "delete_content")

	tests := []struct {
		name     string
		filters  *types.OperationLogFilters
		expected int
	}{
		{
			name: "按用户筛选",
			filters: &types.OperationLogFilters{
				UserKSUID: "user_123",
				Page:      1,
				Limit:     10,
			},
			expected: 2,
		},
		{
			name: "按操作类型筛选",
			filters: &types.OperationLogFilters{
				Operation: "create_content",
				Page:      1,
				Limit:     10,
			},
			expected: 1,
		},
		{
			name: "按资源类型筛选",
			filters: &types.OperationLogFilters{
				ResourceType: "content",
				Page:         1,
				Limit:        10,
			},
			expected: 3,
		},
		{
			name: "分页测试",
			filters: &types.OperationLogFilters{
				Page:  1,
				Limit: 2,
			},
			expected: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logs, total, err := repo.GetByFilters(ctx, tt.filters)
			require.NoError(t, err)
			assert.Len(t, logs, tt.expected)
			assert.GreaterOrEqual(t, total, int64(tt.expected))
		})
	}
}

func TestOperationLogRepository_GetStats(t *testing.T) {
	testDB := SetupTestDB(t)
	defer testDB.TeardownTestDB()

	repo := NewOperationLogRepository(testDB.DB, testDB.Cache)
	ctx := context.Background()

	// 创建测试数据
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)

	// 创建不同时间的操作日志
	log1 := testDB.CreateTestOperationLog("user_123", "create_content")
	log1.CreatedAt = yesterday
	testDB.DB.Save(log1)

	log2 := testDB.CreateTestOperationLog("user_123", "update_content")
	log2.CreatedAt = now
	testDB.DB.Save(log2)

	log3 := testDB.CreateTestOperationLog("user_456", "delete_content")
	log3.CreatedAt = now
	testDB.DB.Save(log3)

	// 测试统计
	startTime := yesterday.Add(-time.Hour)
	endTime := now.Add(time.Hour)

	stats, err := repo.GetStats(ctx, &startTime, &endTime)
	require.NoError(t, err)
	require.NotNil(t, stats)

	assert.GreaterOrEqual(t, stats.TotalOperations, int64(3))
	assert.GreaterOrEqual(t, stats.UniqueUsers, int64(2))
	assert.Contains(t, stats.OperationCounts, "create_content")
	assert.Contains(t, stats.OperationCounts, "update_content")
	assert.Contains(t, stats.OperationCounts, "delete_content")
}

func TestOperationLogRepository_DeleteOldLogs(t *testing.T) {
	testDB := SetupTestDB(t)
	defer testDB.TeardownTestDB()

	repo := NewOperationLogRepository(testDB.DB, testDB.Cache)
	ctx := context.Background()

	// 创建测试数据
	now := time.Now()
	oldTime := now.AddDate(0, 0, -10) // 10天前

	// 创建旧日志
	oldLog := testDB.CreateTestOperationLog("user_123", "old_operation")
	oldLog.CreatedAt = oldTime
	testDB.DB.Save(oldLog)

	// 创建新日志
	newLog := testDB.CreateTestOperationLog("user_123", "new_operation")
	newLog.CreatedAt = now
	testDB.DB.Save(newLog)

	// 删除7天前的日志
	cutoffTime := now.AddDate(0, 0, -7)
	deletedCount, err := repo.DeleteOldLogs(ctx, cutoffTime)
	require.NoError(t, err)
	assert.Equal(t, int64(1), deletedCount)

	// 验证旧日志被删除
	var count int64
	err = testDB.DB.Model(&model.OperationLog{}).Where("id = ?", oldLog.ID).Count(&count).Error
	require.NoError(t, err)
	assert.Equal(t, int64(0), count)

	// 验证新日志仍然存在
	err = testDB.DB.Model(&model.OperationLog{}).Where("id = ?", newLog.ID).Count(&count).Error
	require.NoError(t, err)
	assert.Equal(t, int64(1), count)
}

func TestOperationLogRepository_GetUserOperations(t *testing.T) {
	testDB := SetupTestDB(t)
	defer testDB.TeardownTestDB()

	repo := NewOperationLogRepository(testDB.DB, testDB.Cache)
	ctx := context.Background()

	userKSUID := "user_123"

	// 创建测试数据
	testDB.CreateTestOperationLog(userKSUID, "create_content")
	testDB.CreateTestOperationLog(userKSUID, "update_content")
	testDB.CreateTestOperationLog("user_456", "delete_content") // 其他用户的操作

	logs, err := repo.GetUserOperations(ctx, userKSUID, 10)
	require.NoError(t, err)
	assert.Len(t, logs, 2)

	// 验证所有返回的日志都属于指定用户
	for _, log := range logs {
		assert.Equal(t, userKSUID, log.UserKSUID)
	}
}

func TestOperationLogRepository_GetResourceOperations(t *testing.T) {
	testDB := SetupTestDB(t)
	defer testDB.TeardownTestDB()

	repo := NewOperationLogRepository(testDB.DB, testDB.Cache)
	ctx := context.Background()

	resourceKSUID := "content_123"

	// 创建测试数据
	log1 := testDB.CreateTestOperationLog("user_123", "create_content")
	log1.ResourceKSUID = resourceKSUID
	testDB.DB.Save(log1)

	log2 := testDB.CreateTestOperationLog("user_456", "update_content")
	log2.ResourceKSUID = resourceKSUID
	testDB.DB.Save(log2)

	// 创建其他资源的操作日志
	testDB.CreateTestOperationLog("user_789", "delete_content")

	logs, err := repo.GetResourceOperations(ctx, resourceKSUID, 10)
	require.NoError(t, err)
	assert.Len(t, logs, 2)

	// 验证所有返回的日志都属于指定资源
	for _, log := range logs {
		assert.Equal(t, resourceKSUID, log.ResourceKSUID)
	}
}

func TestOperationLogRepository_BatchCreate(t *testing.T) {
	testDB := SetupTestDB(t)
	defer testDB.TeardownTestDB()

	repo := NewOperationLogRepository(testDB.DB, testDB.Cache)
	ctx := context.Background()

	logs := []*model.OperationLog{
		{
			UserKSUID:     "user_123",
			Operation:     "batch_create_1",
			ResourceType:  "content",
			ResourceKSUID: "content_1",
			Status:        "success",
		},
		{
			UserKSUID:     "user_123",
			Operation:     "batch_create_2",
			ResourceType:  "content",
			ResourceKSUID: "content_2",
			Status:        "success",
		},
	}

	err := repo.BatchCreate(ctx, logs)
	require.NoError(t, err)

	// 验证所有日志都被创建
	for _, log := range logs {
		assert.NotZero(t, log.ID)
		assert.NotZero(t, log.CreatedAt)
	}

	// 验证数据库中的记录数量
	var count int64
	err = testDB.DB.Model(&model.OperationLog{}).Where("user_ksuid = ?", "user_123").Count(&count).Error
	require.NoError(t, err)
	assert.Equal(t, int64(2), count)
}
