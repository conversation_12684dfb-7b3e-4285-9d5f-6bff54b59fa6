package client

import (
	"fmt"
	"pxpat-backend/internal/content-cluster/audit-service/types"

	"github.com/rs/zerolog/log"
)

// ProvideContentServiceClient 提供Content服务客户端
func ProvideContentServiceClient(cfg *types.Config) ContentServiceClient {
	contentServiceClient := NewContentServiceClient(ContentServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.VideoService.Host, cfg.Server.AllServiceList.VideoService.Port),
		Timeout: cfg.Server.AllServiceList.VideoService.Timeout,
	})
	log.Info().Msg("Content service client initialized successfully")
	return contentServiceClient
}

// ProvideUserServiceClient 提供User服务客户端
func ProvideUserServiceClient(cfg *types.Config) UserServiceClient {
	userServiceClient := NewUserServiceClient(UserServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.UserService.Host, cfg.Server.AllServiceList.UserService.Port),
		Timeout: cfg.Server.AllServiceList.UserService.Timeout,
	})
	log.Info().Msg("User service client initialized successfully")
	return userServiceClient
}

// ProvideTokenServiceClient 提供Token服务客户端
func ProvideTokenServiceClient(cfg *types.Config) TokenServiceClient {
	tokenServiceClient := NewTokenServiceClient(TokenServiceConfig{
		BaseURL: fmt.Sprintf("https://%s", cfg.Server.AllServiceList.TokenService.Host),
		Timeout: cfg.Server.AllServiceList.TokenService.Timeout,
	})
	log.Info().Msg("Token service client initialized successfully")
	return tokenServiceClient
}

// ProvideFinanceServiceClient 提供Finance服务客户端
func ProvideFinanceServiceClient(cfg *types.Config) FinanceServiceClient {
	log.Info().
		Str("points_host", cfg.Server.AllServiceList.PointsService.Host).
		Int("points_port", cfg.Server.AllServiceList.PointsService.Port).
		Dur("points_timeout", cfg.Server.AllServiceList.PointsService.Timeout).
		Msg("Points service configuration loaded")

	financeServiceClient := NewFinanceServiceClient(FinanceServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.PointsService.Host, cfg.Server.AllServiceList.PointsService.Port),
		Timeout: cfg.Server.AllServiceList.PointsService.Timeout,
	})
	log.Info().Msg("Finance service client initialized successfully")
	return financeServiceClient
}
