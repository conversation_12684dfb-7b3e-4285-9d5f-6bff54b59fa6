package service

import (
	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/internal/content-cluster/video-service/repository"

	"github.com/rs/zerolog/log"
)

// ProvideInternalServices 提供Internal服务层
func ProvideInternalServices(
	contentRepo *repository.ContentRepository,
	categoryRepo *repository.CategoryRepository,
	tagRepo *repository.TagRepository,
	commentRepo *repository.CommentRepository,
	contentUserRoleRepo repository.ContentUserRoleRepository,
	userServiceClient client.UserServiceClient,
	retryRepo *repository.UserCreationRetryRepository,
	publishStatsRepo *repository.PublishStatsRepository,
) (
	*ContentService,
	*CategoryService,
	*TagService,
	*CommentService,
) {
	// 创建Internal服务
	contentService := NewContentService(contentRepo, tagRepo, contentUserRoleRepo, userServiceClient, retryRepo, publishStatsRepo)
	categoryService := NewCategoryService(categoryRepo)
	tagService := NewTagService(tagRepo)
	commentService := NewCommentService(commentRepo, contentRepo)

	log.Info().Msg("Internal services initialized successfully")
	return contentService, categoryService, tagService, commentService
}
