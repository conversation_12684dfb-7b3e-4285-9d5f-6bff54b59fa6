#!/bin/bash

# Docker 清理脚本
set -e

echo "🧹 Docker 环境清理工具"
echo ""

# 显示当前 Docker 使用情况
echo "📊 当前 Docker 使用情况:"
docker system df
echo ""

# 询问用户要执行的清理操作
echo "请选择要执行的清理操作:"
echo "1) 清理未使用的镜像"
echo "2) 清理停止的容器"
echo "3) 清理未使用的网络"
echo "4) 清理未使用的数据卷"
echo "5) 清理构建缓存"
echo "6) 完全清理 (包括所有未使用的资源)"
echo "7) 清理 PXPAT 相关资源"
echo "8) 显示详细使用情况"
echo "0) 退出"
echo ""

read -p "请输入选项 (0-8): " choice

case $choice in
    1)
        echo "🖼️  清理未使用的镜像..."
        docker image prune -f
        echo "✅ 镜像清理完成"
        ;;
    2)
        echo "📦 清理停止的容器..."
        docker container prune -f
        echo "✅ 容器清理完成"
        ;;
    3)
        echo "🌐 清理未使用的网络..."
        docker network prune -f
        echo "✅ 网络清理完成"
        ;;
    4)
        echo "💾 清理未使用的数据卷..."
        echo "⚠️  注意: 这将删除所有未使用的数据卷，包括数据库数据！"
        read -p "确定要继续吗? (y/N): " confirm
        if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
            docker volume prune -f
            echo "✅ 数据卷清理完成"
        else
            echo "❌ 已取消数据卷清理"
        fi
        ;;
    5)
        echo "🔨 清理构建缓存..."
        docker builder prune -f
        echo "✅ 构建缓存清理完成"
        ;;
    6)
        echo "🧹 完全清理所有未使用的资源..."
        echo "⚠️  注意: 这将删除所有未使用的镜像、容器、网络和数据卷！"
        read -p "确定要继续吗? (y/N): " confirm
        if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
            docker system prune -a -f --volumes
            echo "✅ 完全清理完成"
        else
            echo "❌ 已取消完全清理"
        fi
        ;;
    7)
        echo "🎯 清理 PXPAT 相关资源..."
        
        # 停止并删除 PXPAT 相关容器
        echo "🛑 停止 PXPAT 相关容器..."
        docker ps -a --filter "name=content-management" --filter "name=postgres" --filter "name=redis" --filter "name=consul" -q | xargs -r docker stop
        docker ps -a --filter "name=content-management" --filter "name=postgres" --filter "name=redis" --filter "name=consul" -q | xargs -r docker rm
        
        # 删除 PXPAT 相关镜像
        echo "🖼️  删除 PXPAT 相关镜像..."
        docker images --filter "reference=*pxpat*" --filter "reference=*content-management*" -q | xargs -r docker rmi -f
        
        # 询问是否删除数据卷
        echo "💾 发现以下 PXPAT 相关数据卷:"
        docker volume ls --filter "name=postgres_data" --filter "name=redis_data" --filter "name=consul_data"
        read -p "是否删除这些数据卷? (y/N): " confirm
        if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
            docker volume ls --filter "name=postgres_data" --filter "name=redis_data" --filter "name=consul_data" -q | xargs -r docker volume rm
            echo "✅ 数据卷删除完成"
        else
            echo "ℹ️  保留数据卷"
        fi
        
        echo "✅ PXPAT 资源清理完成"
        ;;
    8)
        echo "📊 详细使用情况:"
        echo ""
        echo "🖼️  镜像:"
        docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        echo ""
        echo "📦 容器:"
        docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.CreatedAt}}"
        echo ""
        echo "🌐 网络:"
        docker network ls
        echo ""
        echo "💾 数据卷:"
        docker volume ls
        echo ""
        echo "🔨 构建缓存:"
        docker builder du
        ;;
    0)
        echo "👋 退出清理工具"
        exit 0
        ;;
    *)
        echo "❌ 无效选项"
        exit 1
        ;;
esac

echo ""
echo "📊 清理后的 Docker 使用情况:"
docker system df
echo ""
echo "✅ 清理操作完成！"
