package repository

import (
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideVideoRepository 提供视频仓储
func ProvideVideoRepository(db *gorm.DB) VideoRepository {
	repo := NewVideoRepository(db)
	log.Info().Msg("视频仓储初始化成功")
	return repo
}

// ProvideCoverRepository 提供封面仓储
func ProvideCoverRepository(db *gorm.DB) CoverRepository {
	repo := NewCoverRepository(db)
	log.Info().Msg("封面仓储初始化成功")
	return repo
}
