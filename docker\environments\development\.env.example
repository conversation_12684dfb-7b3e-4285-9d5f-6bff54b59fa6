# 开发环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# 应用环境
GO_ENV=development
GIN_MODE=debug

# 服务端口配置
CONTENT_MANAGEMENT_PORT=12010

# 数据库配置
POSTGRES_DB=content_management
POSTGRES_USER=content_user
POSTGRES_PASSWORD=content_password
POSTGRES_PORT=5432

DB_HOST=postgres
DB_PORT=5432
DB_USER=content_user
DB_PASSWORD=content_password
DB_NAME=content_management
DB_SSL_MODE=disable

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password
REDIS_DB=0

# JWT配置
JWT_SECRET_KEY=dev-jwt-secret-key-change-in-production
JWT_TOKEN_DURATION=24h
JWT_REFRESH_DURATION=168h

# Consul配置
CONSUL_HOST=consul
CONSUL_PORT=8500
CONSUL_DATACENTER=dc1

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=text

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123
JAEGER_UI_PORT=16686

# 外部服务配置
VIDEO_SERVICE_URL=http://video-service:12001
INTERACTION_SERVICE_URL=http://interaction-service:12002

# 开发工具配置
ENABLE_PPROF=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
