package impl

import (
	"context"

	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// RightsVerificationRepositoryImpl 权益认证仓储实现
type RightsVerificationRepositoryImpl struct {
	db           *gorm.DB
	cache        cache.Manager
	cacheKeyBase string
}

// NewRightsVerificationRepository 创建权益认证仓储实例
func NewRightsVerificationRepository(db *gorm.DB, cache cache.Manager) repository.RightsVerificationRepository {
	return &RightsVerificationRepositoryImpl{
		db:           db,
		cache:        cache,
		cacheKeyBase: "rights_verification:",
	}
}

// Create 创建权益认证
func (r *RightsVerificationRepositoryImpl) Create(ctx context.Context, verification *model.RightsVerification) error {
	return r.db.WithContext(ctx).Create(verification).Error
}

// GetByKSUID 根据KSUID获取权益认证
func (r *RightsVerificationRepositoryImpl) GetByKSUID(ctx context.Context, rightsKSUID string) (*model.RightsVerification, error) {
	var verification model.RightsVerification
	err := r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).First(&verification).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrRightsVerificationNotFound
		}
		return nil, err
	}
	return &verification, nil
}

// Update 更新权益认证
func (r *RightsVerificationRepositoryImpl) Update(ctx context.Context, verification *model.RightsVerification) error {
	return r.db.WithContext(ctx).Save(verification).Error
}

// Delete 删除权益认证
func (r *RightsVerificationRepositoryImpl) Delete(ctx context.Context, rightsKSUID string) error {
	return r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).Delete(&model.RightsVerification{}).Error
}

// GetByUserKSUID 根据用户KSUID获取权益认证列表
func (r *RightsVerificationRepositoryImpl) GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.RightsVerification, int64, error) {
	var verifications []*model.RightsVerification
	var total int64

	query := r.db.WithContext(ctx).Where("user_ksuid = ?", userKSUID)

	// 获取总数
	if err := query.Model(&model.RightsVerification{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&verifications).Error; err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// GetWithFilters 根据过滤条件获取权益认证列表
func (r *RightsVerificationRepositoryImpl) GetWithFilters(ctx context.Context, filters repository.RightsFilters) ([]*model.RightsVerification, int64, error) {
	var verifications []*model.RightsVerification
	var total int64

	query := r.db.WithContext(ctx).Model(&model.RightsVerification{})

	// 应用过滤条件
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.UserKSUID != "" {
		query = query.Where("user_ksuid = ?", filters.UserKSUID)
	}
	if filters.ReviewerKSUID != "" {
		query = query.Where("reviewer_ksuid = ?", filters.ReviewerKSUID)
	}
	if filters.IsAgent != nil {
		query = query.Where("is_agent = ?", *filters.IsAgent)
	}
	if filters.StartDate != "" {
		query = query.Where("created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != "" {
		query = query.Where("created_at <= ?", filters.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用排序
	if filters.SortBy != "" {
		order := filters.SortBy
		if filters.SortOrder != "" {
			order += " " + filters.SortOrder
		}
		query = query.Order(order)
	} else {
		query = query.Order("created_at DESC")
	}

	// 应用分页
	if filters.PageSize > 0 {
		offset := (filters.Page - 1) * filters.PageSize
		query = query.Offset(offset).Limit(filters.PageSize)
	}

	if err := query.Find(&verifications).Error; err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// GetByID 根据ID获取权益认证
func (r *RightsVerificationRepositoryImpl) GetByID(ctx context.Context, id uint) (*model.RightsVerification, error) {
	var verification model.RightsVerification
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&verification).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrRightsVerificationNotFound
		}
		return nil, err
	}
	return &verification, nil
}

// GetByStatus 根据状态获取权益认证列表
func (r *RightsVerificationRepositoryImpl) GetByStatus(ctx context.Context, status model.RightsStatus, page, pageSize int) ([]*model.RightsVerification, int64, error) {
	var verifications []*model.RightsVerification
	var total int64

	query := r.db.WithContext(ctx).Where("status = ?", status)

	// 获取总数
	if err := query.Model(&model.RightsVerification{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&verifications).Error; err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// GetByType 根据类型获取权益认证列表
func (r *RightsVerificationRepositoryImpl) GetByType(ctx context.Context, rightsType model.RightsType, page, pageSize int) ([]*model.RightsVerification, int64, error) {
	var verifications []*model.RightsVerification
	var total int64

	query := r.db.WithContext(ctx).Where("type = ?", rightsType)

	// 获取总数
	if err := query.Model(&model.RightsVerification{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&verifications).Error; err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// CountByUserKSUID 根据用户KSUID统计权益认证数量
func (r *RightsVerificationRepositoryImpl) CountByUserKSUID(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.RightsVerification{}).Where("user_ksuid = ?", userKSUID).Count(&count).Error
	return count, err
}

// CountByStatus 根据状态统计权益认证数量
func (r *RightsVerificationRepositoryImpl) CountByStatus(ctx context.Context, status model.RightsStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.RightsVerification{}).Where("status = ?", status).Count(&count).Error
	return count, err
}

// CountByType 根据类型统计权益认证数量
func (r *RightsVerificationRepositoryImpl) CountByType(ctx context.Context, rightsType model.RightsType) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.RightsVerification{}).Where("type = ?", rightsType).Count(&count).Error
	return count, err
}

// GetPendingVerifications 获取待审核的权益认证
func (r *RightsVerificationRepositoryImpl) GetPendingVerifications(ctx context.Context, page, pageSize int) ([]*model.RightsVerification, int64, error) {
	return r.GetByStatus(ctx, model.RightsStatusPending, page, pageSize)
}

// GetExpiredVerifications 获取过期的权益认证
func (r *RightsVerificationRepositoryImpl) GetExpiredVerifications(ctx context.Context) ([]*model.RightsVerification, error) {
	var verifications []*model.RightsVerification
	// 这里假设有一个过期时间字段，如果没有可以根据业务逻辑调整
	err := r.db.WithContext(ctx).
		Where("status = ? AND expires_at < NOW()", model.RightsStatusApproved).
		Find(&verifications).Error
	return verifications, err
}

// UpdateStatus 更新权益认证状态
func (r *RightsVerificationRepositoryImpl) UpdateStatus(ctx context.Context, rightsKSUID string, status model.RightsStatus, reviewerKSUID, reviewNote, rejectReason string) error {
	updates := map[string]interface{}{
		"status":         status,
		"reviewer_ksuid": reviewerKSUID,
		"review_note":    reviewNote,
	}

	if rejectReason != "" {
		updates["reject_reason"] = rejectReason
	}

	return r.db.WithContext(ctx).
		Model(&model.RightsVerification{}).
		Where("rights_ksuid = ?", rightsKSUID).
		Updates(updates).Error
}

// GetDB 获取数据库实例，用于事务处理
func (r *RightsVerificationRepositoryImpl) GetDB() interface{} {
	return r.db
}
