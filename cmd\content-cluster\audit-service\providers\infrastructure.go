package providers

import (
	"pxpat-backend/internal/content-cluster/audit-service/migrations"
	"pxpat-backend/internal/content-cluster/audit-service/types"
	"pxpat-backend/pkg/auth"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideDatabase 提供数据库连接
func ProvideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	// 自动创建数据库
	migrations.AutoMigrate(db)
	log.Info().Msg("Database migration completed")

	return db, nil
}

// ProvideJWTManager 提供JWT管理器
func ProvideJWTManager(cfg *types.Config) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized successfully")
	return &jwtManager
}

// ProvideConsulManager 提供Consul管理器
func ProvideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Consul manager")
		return nil, err
	}
	log.Info().Msg("Consul manager initialized successfully")
	return consulManager, nil
}

// ProvideHealthHandler 提供健康检查处理器
func ProvideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	log.Info().Msg("Health handler initialized successfully")
	return healthHandler
}
