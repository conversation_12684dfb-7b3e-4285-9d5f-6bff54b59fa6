package utils

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"pxpat-backend/internal/content-cluster/content-management-service/dto"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

func TestContentValidator_ValidateContentFilters(t *testing.T) {
	validator := NewContentValidator()

	tests := []struct {
		name    string
		filters *types.ContentFilters
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效的过滤器",
			filters: &types.ContentFilters{
				ContentTypes: []string{"video", "novel"},
				Status:       "published",
				UserKSUID:    "user_123",
				CategoryID:   1,
				Tags:         []string{"tag1", "tag2"},
				Page:         1,
				Limit:        20,
				SortBy:       "created_at",
				SortOrder:    "desc",
			},
			wantErr: false,
		},
		{
			name: "无效的内容类型",
			filters: &types.ContentFilters{
				ContentTypes: []string{"invalid_type"},
				Page:         1,
				Limit:        20,
			},
			wantErr: true,
			errMsg:  "不支持的内容类型",
		},
		{
			name: "无效的状态",
			filters: &types.ContentFilters{
				Status: "invalid_status",
				Page:   1,
				Limit:  20,
			},
			wantErr: true,
			errMsg:  "无效的状态值",
		},
		{
			name: "页码小于1",
			filters: &types.ContentFilters{
				Page:  0,
				Limit: 20,
			},
			wantErr: true,
			errMsg:  "页码必须大于0",
		},
		{
			name: "每页数量超出限制",
			filters: &types.ContentFilters{
				Page:  1,
				Limit: 1001,
			},
			wantErr: true,
			errMsg:  "每页数量不能超过1000",
		},
		{
			name: "无效的排序字段",
			filters: &types.ContentFilters{
				Page:   1,
				Limit:  20,
				SortBy: "invalid_field",
			},
			wantErr: true,
			errMsg:  "无效的排序字段",
		},
		{
			name: "无效的排序方向",
			filters: &types.ContentFilters{
				Page:      1,
				Limit:     20,
				SortBy:    "created_at",
				SortOrder: "invalid_order",
			},
			wantErr: true,
			errMsg:  "排序方向必须是asc或desc",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateContentFilters(tt.filters)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestContentValidator_ValidateBatchRequest(t *testing.T) {
	validator := NewContentValidator()

	tests := []struct {
		name    string
		request *dto.BatchOperationRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效的批量请求",
			request: &dto.BatchOperationRequest{
				ContentKSUIDs: []string{"content_1", "content_2", "content_3"},
				Operation:     "update_status",
				Parameters: map[string]interface{}{
					"status": "published",
				},
			},
			wantErr: false,
		},
		{
			name: "空的内容KSUID列表",
			request: &dto.BatchOperationRequest{
				ContentKSUIDs: []string{},
				Operation:     "update_status",
			},
			wantErr: true,
			errMsg:  "内容KSUID列表不能为空",
		},
		{
			name: "内容数量超出限制",
			request: &dto.BatchOperationRequest{
				ContentKSUIDs: make([]string, 1001), // 超过1000的限制
				Operation:     "update_status",
			},
			wantErr: true,
			errMsg:  "批量操作的内容数量不能超过1000",
		},
		{
			name: "无效的操作类型",
			request: &dto.BatchOperationRequest{
				ContentKSUIDs: []string{"content_1"},
				Operation:     "invalid_operation",
			},
			wantErr: true,
			errMsg:  "无效的批量操作类型",
		},
		{
			name: "缺少必要参数",
			request: &dto.BatchOperationRequest{
				ContentKSUIDs: []string{"content_1"},
				Operation:     "update_status",
				Parameters:    map[string]interface{}{},
			},
			wantErr: true,
			errMsg:  "缺少必要的参数",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateBatchRequest(tt.request)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestContentValidator_ValidateStatsRequest(t *testing.T) {
	validator := NewContentValidator()

	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	tomorrow := now.AddDate(0, 0, 1)

	tests := []struct {
		name    string
		request *dto.StatsRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效的统计请求",
			request: &dto.StatsRequest{
				ContentTypes: []string{"video"},
				StartTime:    &yesterday,
				EndTime:      &now,
				GroupBy:      "day",
				Metrics:      []string{"views", "likes"},
			},
			wantErr: false,
		},
		{
			name: "开始时间晚于结束时间",
			request: &dto.StatsRequest{
				StartTime: &tomorrow,
				EndTime:   &yesterday,
			},
			wantErr: true,
			errMsg:  "开始时间不能晚于结束时间",
		},
		{
			name: "无效的分组方式",
			request: &dto.StatsRequest{
				GroupBy: "invalid_group",
			},
			wantErr: true,
			errMsg:  "无效的分组方式",
		},
		{
			name: "无效的指标",
			request: &dto.StatsRequest{
				Metrics: []string{"invalid_metric"},
			},
			wantErr: true,
			errMsg:  "无效的统计指标",
		},
		{
			name: "时间范围过长",
			request: &dto.StatsRequest{
				StartTime: &time.Time{}, // 很久以前的时间
				EndTime:   &now,
			},
			wantErr: true,
			errMsg:  "统计时间范围不能超过1年",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateStatsRequest(tt.request)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestContentValidator_ValidateKSUID(t *testing.T) {
	validator := NewContentValidator()

	tests := []struct {
		name    string
		ksuid   string
		wantErr bool
	}{
		{"有效的KSUID", "2SwHhWkVcBKlmzOiP8zFJQOmOu6", true},
		{"空KSUID", "", false},
		{"过短的KSUID", "short", false},
		{"过长的KSUID", "this_is_a_very_long_string_that_exceeds_normal_ksuid_length", false},
		{"包含无效字符的KSUID", "invalid-ksuid-with-dashes", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.ValidateKSUID(tt.ksuid)
			assert.Equal(t, tt.wantErr, result)
		})
	}
}

func TestContentValidator_ValidateContentStatus(t *testing.T) {
	validator := NewContentValidator()

	tests := []struct {
		name   string
		status string
		valid  bool
	}{
		{"草稿状态", "draft", true},
		{"已发布状态", "published", true},
		{"已归档状态", "archived", true},
		{"已删除状态", "deleted", true},
		{"审核中状态", "reviewing", true},
		{"审核拒绝状态", "rejected", true},
		{"无效状态", "invalid", false},
		{"空状态", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.ValidateContentStatus(tt.status)
			assert.Equal(t, tt.valid, result)
		})
	}
}

func TestContentValidator_ValidateTimeRange(t *testing.T) {
	validator := NewContentValidator()

	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	tomorrow := now.AddDate(0, 0, 1)
	oneYearAgo := now.AddDate(-1, 0, 0)
	twoYearsAgo := now.AddDate(-2, 0, 0)

	tests := []struct {
		name      string
		startTime *time.Time
		endTime   *time.Time
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "有效的时间范围",
			startTime: &yesterday,
			endTime:   &now,
			wantErr:   false,
		},
		{
			name:      "开始时间晚于结束时间",
			startTime: &tomorrow,
			endTime:   &yesterday,
			wantErr:   true,
			errMsg:    "开始时间不能晚于结束时间",
		},
		{
			name:      "时间范围过长",
			startTime: &twoYearsAgo,
			endTime:   &now,
			wantErr:   true,
			errMsg:    "时间范围不能超过1年",
		},
		{
			name:      "边界情况-正好1年",
			startTime: &oneYearAgo,
			endTime:   &now,
			wantErr:   false,
		},
		{
			name:      "只有开始时间",
			startTime: &yesterday,
			endTime:   nil,
			wantErr:   false,
		},
		{
			name:      "只有结束时间",
			startTime: nil,
			endTime:   &now,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateTimeRange(tt.startTime, tt.endTime)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
