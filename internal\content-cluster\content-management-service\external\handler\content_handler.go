package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/dto"
	"pxpat-backend/internal/content-cluster/content-management-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

// ContentHandler 内容管理处理器
type ContentHandler struct {
	contentService service.ContentManagementService
}

// NewContentHandler 创建内容管理处理器实例
func NewContentHandler(contentService service.ContentManagementService) *ContentHandler {
	return &ContentHandler{
		contentService: contentService,
	}
}

// GetBaseContents 获取基础内容列表
// @Summary 获取基础内容列表
// @Description 获取跨内容类型的基础内容列表，支持分页、排序、筛选
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param content_types query []string false "内容类型过滤" collectionFormat(multi)
// @Param status query string false "状态过滤"
// @Param user_ksuid query string false "用户过滤"
// @Param category_id query int false "分类过滤"
// @Param tags query []string false "标签过滤" collectionFormat(multi)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc) Enums(asc, desc)
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentListResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/contents [get]
func (h *ContentHandler) GetBaseContents(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetBaseContents")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到获取基础内容列表请求")

	// 5. 参数绑定
	var req dto.ContentQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_types", req.ContentTypes)
	opentelemetry.AddAttribute(span, "status", req.Status)
	opentelemetry.AddAttribute(span, "page", req.Page)
	opentelemetry.AddAttribute(span, "limit", req.Limit)

	// 7. 调用服务层
	result, gErr := h.contentService.GetBaseContents(ctx, req.ToContentFilters())
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("获取基础内容列表失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 转换响应数据
	response := dto.FromBaseContentList(result)

	// 9. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("total_contents", response.Total).
		Int("page_contents", len(response.Contents)).
		Msg("获取基础内容列表成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// GetContentWithDetails 获取内容详情
// @Summary 获取内容详情
// @Description 根据内容KSUID获取带详细信息的内容
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param content_ksuid path string true "内容KSUID"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 404 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/contents/{content_ksuid} [get]
func (h *ContentHandler) GetContentWithDetails(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetContentWithDetails")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取路径参数
	contentKSUID := c.Param("content_ksuid")
	if contentKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("内容KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "内容KSUID不能为空",
		})
		return
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "content_ksuid", contentKSUID)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("content_ksuid", contentKSUID).
		Msg("收到获取内容详情请求")

	// 6. 调用服务层
	result, gErr := h.contentService.GetContentWithDetails(ctx, contentKSUID)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("content_ksuid", contentKSUID).
			Interface("error", gErr).
			Msg("获取内容详情失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 转换响应数据
	response := dto.FromContentWithDetails(result)

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("content_ksuid", contentKSUID).
		Str("content_type", result.ContentType).
		Msg("获取内容详情成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// UpdateContentStatus 更新内容状态
// @Summary 更新内容状态
// @Description 更新指定内容的状态
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param content_ksuid path string true "内容KSUID"
// @Param request body dto.UpdateContentStatusRequest true "更新状态请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 404 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/contents/{content_ksuid}/status [put]
func (h *ContentHandler) UpdateContentStatus(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "UpdateContentStatus")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 获取路径参数
	contentKSUID := c.Param("content_ksuid")
	if contentKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("内容KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "内容KSUID不能为空",
		})
		return
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "content_ksuid", contentKSUID)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("content_ksuid", contentKSUID).
		Str("user_ksuid", userKSUID).
		Msg("收到更新内容状态请求")

	// 6. 参数绑定
	var req dto.UpdateContentStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 7. 添加请求参数到span
	opentelemetry.AddAttribute(span, "new_status", req.Status)

	// 8. 调用服务层
	result, gErr := h.contentService.UpdateContentStatus(ctx, userKSUID, contentKSUID, req.Status)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("content_ksuid", contentKSUID).
			Str("new_status", req.Status).
			Interface("error", gErr).
			Msg("更新内容状态失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		} else if gErr.Code == errors.PERMISSION_DENIED {
			statusCode = http.StatusForbidden
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 9. 转换响应数据
	response := dto.FromContentWithDetails(result)

	// 10. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("content_ksuid", contentKSUID).
		Str("new_status", req.Status).
		Msg("更新内容状态成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// DeleteContent 删除内容
// @Summary 删除内容
// @Description 删除指定的内容
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param content_ksuid path string true "内容KSUID"
// @Success 200 {object} globalTypes.GlobalResponse
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 404 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/contents/{content_ksuid} [delete]
func (h *ContentHandler) DeleteContent(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "DeleteContent")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 获取路径参数
	contentKSUID := c.Param("content_ksuid")
	if contentKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("内容KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "内容KSUID不能为空",
		})
		return
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "content_ksuid", contentKSUID)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("content_ksuid", contentKSUID).
		Str("user_ksuid", userKSUID).
		Msg("收到删除内容请求")

	// 6. 调用服务层
	gErr := h.contentService.DeleteContent(ctx, userKSUID, contentKSUID)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("content_ksuid", contentKSUID).
			Interface("error", gErr).
			Msg("删除内容失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		} else if gErr.Code == errors.PERMISSION_DENIED {
			statusCode = http.StatusForbidden
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("content_ksuid", contentKSUID).
		Msg("删除内容成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Msg:  "删除成功",
	})
}

// GetUserAllContents 获取用户的所有内容
// @Summary 获取用户的所有内容
// @Description 获取指定用户的所有内容（基础信息）
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param user_ksuid path string true "用户KSUID"
// @Param content_types query []string false "内容类型过滤" collectionFormat(multi)
// @Param status query string false "状态过滤"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc) Enums(asc, desc)
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentListResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/users/{user_ksuid}/contents [get]
func (h *ContentHandler) GetUserAllContents(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetUserAllContents")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取路径参数
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("用户KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "用户KSUID不能为空",
		})
		return
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到获取用户所有内容请求")

	// 6. 参数绑定
	var req dto.ContentQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 7. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_types", req.ContentTypes)
	opentelemetry.AddAttribute(span, "status", req.Status)
	opentelemetry.AddAttribute(span, "page", req.Page)
	opentelemetry.AddAttribute(span, "limit", req.Limit)

	// 8. 调用服务层
	result, gErr := h.contentService.GetUserAllContents(ctx, userKSUID, req.ToContentFilters())
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("user_ksuid", userKSUID).
			Interface("error", gErr).
			Msg("获取用户所有内容失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 9. 转换响应数据
	response := dto.FromBaseContentList(result)

	// 10. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("user_ksuid", userKSUID).
		Int("total_contents", response.Total).
		Int("page_contents", len(response.Contents)).
		Msg("获取用户所有内容成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// GetUserContentsByType 获取用户指定类型的内容
// @Summary 获取用户指定类型的内容
// @Description 获取指定用户的指定类型内容（带详细信息）
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param user_ksuid path string true "用户KSUID"
// @Param content_type path string true "内容类型" Enums(video, novel, music)
// @Param status query string false "状态过滤"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc) Enums(asc, desc)
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentWithDetailsListResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/users/{user_ksuid}/contents/{content_type} [get]
func (h *ContentHandler) GetUserContentsByType(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetUserContentsByType")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取路径参数
	userKSUID := c.Param("user_ksuid")
	contentType := c.Param("content_type")

	if userKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("用户KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "用户KSUID不能为空",
		})
		return
	}

	if contentType == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("内容类型参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "内容类型不能为空",
		})
		return
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
	opentelemetry.AddAttribute(span, "content_type", contentType)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Str("content_type", contentType).
		Msg("收到获取用户指定类型内容请求")

	// 6. 参数绑定
	var req dto.ContentQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 7. 添加请求参数到span
	opentelemetry.AddAttribute(span, "status", req.Status)
	opentelemetry.AddAttribute(span, "page", req.Page)
	opentelemetry.AddAttribute(span, "limit", req.Limit)

	// 8. 调用服务层
	result, gErr := h.contentService.GetUserContentsByType(ctx, userKSUID, contentType, req.ToContentFilters())
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("user_ksuid", userKSUID).
			Str("content_type", contentType).
			Interface("error", gErr).
			Msg("获取用户指定类型内容失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		} else if gErr.Code == errors.SERVICE_UNAVAILABLE {
			statusCode = http.StatusServiceUnavailable
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 9. 转换响应数据
	response := dto.FromContentWithDetailsList(result)

	// 10. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("user_ksuid", userKSUID).
		Str("content_type", contentType).
		Int("total_contents", response.Total).
		Int("page_contents", len(response.Contents)).
		Msg("获取用户指定类型内容成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// GetVideoContents 获取视频内容列表
// @Summary 获取视频内容列表
// @Description 获取视频内容列表（带详细信息）
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param status query string false "状态过滤"
// @Param user_ksuid query string false "用户过滤"
// @Param category_id query int false "分类过滤"
// @Param tags query []string false "标签过滤" collectionFormat(multi)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc) Enums(asc, desc)
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentWithDetailsListResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/videos [get]
func (h *ContentHandler) GetVideoContents(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetVideoContents")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "content_type", "video")

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Msg("收到获取视频内容列表请求")

	// 5. 参数绑定
	var req dto.ContentQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "status", req.Status)
	opentelemetry.AddAttribute(span, "user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "page", req.Page)
	opentelemetry.AddAttribute(span, "limit", req.Limit)

	// 7. 调用服务层
	result, gErr := h.contentService.GetVideoContents(ctx, req.ToContentFilters())
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("获取视频内容列表失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 转换响应数据
	response := dto.FromContentWithDetailsList(result)

	// 9. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("total_contents", response.Total).
		Int("page_contents", len(response.Contents)).
		Msg("获取视频内容列表成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// BatchUpdateContentStatus 批量更新内容状态
// @Summary 批量更新内容状态
// @Description 批量更新多个内容的状态
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateStatusRequest true "批量更新状态请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/contents/batch/status [put]
func (h *ContentHandler) BatchUpdateContentStatus(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchUpdateContentStatus")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量更新内容状态请求")

	// 5. 参数绑定
	var req dto.BatchUpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))
	opentelemetry.AddAttribute(span, "new_status", req.Status)

	// 7. 调用服务层
	result, gErr := h.contentService.BatchUpdateContentStatus(ctx, userKSUID, &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Str("new_status", req.Status).
			Interface("error", gErr).
			Msg("批量更新内容状态失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Str("new_status", req.Status).
		Msg("批量更新内容状态完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchDeleteContents 批量删除内容
// @Summary 批量删除内容
// @Description 批量删除多个内容
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param request body dto.BatchDeleteRequest true "批量删除请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/contents/batch/delete [post]
func (h *ContentHandler) BatchDeleteContents(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchDeleteContents")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量删除内容请求")

	// 5. 参数绑定
	var req dto.BatchDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))

	// 7. 调用服务层
	result, gErr := h.contentService.BatchDeleteContents(ctx, userKSUID, &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Interface("error", gErr).
			Msg("批量删除内容失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量删除内容完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查内容管理服务的健康状态
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/health [get]
func (h *ContentHandler) HealthCheck(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "HealthCheck")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

	// 4. 记录请求日志
	log.Debug().
		Str("trace_id", traceID).
		Msg("收到健康检查请求")

	// 5. 调用服务层
	gErr := h.contentService.HealthCheck(ctx)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("健康检查失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 6. 返回成功响应
	log.Debug().
		Str("trace_id", traceID).
		Msg("健康检查成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Msg:  "服务健康",
	})
}
