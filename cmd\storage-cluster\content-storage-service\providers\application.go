package providers

import (
	"context"
	"pxpat-backend/cmd"
	externalHandler "pxpat-backend/internal/storage-cluster/content-storage-service/external/handler"
	internalHandler "pxpat-backend/internal/storage-cluster/content-storage-service/intra/handler"
	"pxpat-backend/internal/storage-cluster/content-storage-service/routes"
	"pxpat-backend/internal/storage-cluster/content-storage-service/types"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/opentelemetry"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
)

// ProvideRoutes 提供路由注册
func ProvideRoutes(
	cfg *types.Config,
	ginEngine *gin.Engine,
	videoHandler *externalHandler.VideoHandler,
	coverHandler *externalHandler.CoverHandler,
	internalCoverHandler *internalHandler.InternalCoverHandler,
	internalVideoHandler *internalHandler.InternalVideoHandler,
) *gin.Engine {
	// 注册业务路由
	routes.RegisterRoutes(cfg, ginEngine, videoHandler, coverHandler, internalCoverHandler, internalVideoHandler)
	log.Info().Msg("Routes registered")
	return ginEngine
}

// ManageLifecycle 应用生命周期管理
func ManageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(context.Background()); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Msg("内容存储服务启动中...")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭OpenTelemetry
			if err := otelProvider.Shutdown(ctx); err != nil {
				log.Error().Err(err).Msg("关闭 OpenTelemetry 失败")
			} else {
				log.Info().Msg("OpenTelemetry 关闭成功")
			}

			return nil
		},
	})
}
