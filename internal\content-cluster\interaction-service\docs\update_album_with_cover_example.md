# UpdateAlbum API 封面更新功能

## 概述

UpdateAlbum API 现在支持通过 `cover_ksuid` 参数更新合集封面。当用户提供 `cover_ksuid` 时，系统会：

1. 验证封面是否存在
2. 验证封面是否属于当前用户
3. 验证封面状态是否可用
4. 更新合集的封面信息

## API 请求示例

### 更新合集信息并设置封面

```http
PUT /api/v1/albums/{album_ksuid}
Content-Type: application/json
Authorization: Bearer <JWT_TOKEN>

{
    "album_name": "我的视频合集",
    "description": "这是一个包含精彩视频的合集",
    "is_public": true,
    "cover_ksuid": "2Z4PVTL0KOI7W29SJMJYF8KD6N2A"
}
```

### 只更新合集基本信息（不更新封面）

```http
PUT /api/v1/albums/{album_ksuid}
Content-Type: application/json
Authorization: Bearer <JWT_TOKEN>

{
    "album_name": "我的视频合集",
    "description": "这是一个包含精彩视频的合集",
    "is_public": true
}
```

### 清空封面（传空字符串）

```http
PUT /api/v1/albums/{album_ksuid}
Content-Type: application/json
Authorization: Bearer <JWT_TOKEN>

{
    "album_name": "我的视频合集",
    "description": "这是一个包含精彩视频的合集",
    "is_public": true,
    "cover_ksuid": ""
}
```

## API 响应示例

### 成功响应

```json
{
    "code": 0,
    "data": {
        "album_ksuid": "2Z4PVTL0KOI7W29SJMJYF8KD6N2B",
        "album_name": "我的视频合集",
        "album_type": "video",
        "cover_ksuid": "2Z4PVTL0KOI7W29SJMJYF8KD6N2A",
        "cover_url": "https://storage.example.com/covers/2Z4PVTL0KOI7W29SJMJYF8KD6N2A.jpg",
        "is_public": true,
        "description": "这是一个包含精彩视频的合集",
        "content_count": 5,
        "contents": [
            {
                "id": 1,
                "album_ksuid": "2Z4PVTL0KOI7W29SJMJYF8KD6N2B",
                "content_ksuid": "2Z4PVTL0KOI7W29SJMJYF8KD6N2C",
                "content_type": "video",
                "sort_order": 1,
                "added_at": "2024-01-15T10:30:00Z"
            }
        ],
        "updated_at": "2024-01-15T14:30:00Z"
    }
}
```

### 错误响应示例

#### 封面不存在

```json
{
    "code": -1004,
    "message": "封面不存在"
}
```

#### 无权使用该封面

```json
{
    "code": -1003,
    "message": "无权使用该封面"
}
```

#### 封面状态不可用

```json
{
    "code": -1005,
    "message": "封面状态不可用"
}
```

## 实现细节

### 封面验证流程

1. **存在性验证**：通过 `ContentStorageServiceClient.GetCoverInfo()` 获取封面信息
2. **权限验证**：检查封面的 `user_ksuid` 是否与当前用户匹配
3. **状态验证**：检查封面状态是否为 `uploaded` 或 `active`
4. **URL获取**：从封面信息中获取 `file_url` 并保存到合集记录中

### 数据库更新

当提供有效的 `cover_ksuid` 时，会更新以下字段：
- `cover_ksuid`: 封面的KSUID
- `cover_url`: 封面的访问URL
- `updated_at`: 更新时间

### 日志记录

系统会记录以下关键操作：
- 封面验证开始
- 封面信息获取成功/失败
- 权限检查结果
- 状态验证结果
- 封面验证成功

## 参考CreateAlbum实现

UpdateAlbum的封面处理逻辑参考了CreateAlbum函数的实现：

1. 使用相同的封面验证逻辑
2. 使用相同的错误处理机制
3. 使用相同的日志记录格式
4. 使用相同的存储服务客户端调用

## 注意事项

1. `cover_ksuid` 参数是可选的，如果不提供则不会更新封面信息
2. 如果提供空字符串，封面信息保持不变（不会清空现有封面）
3. 封面必须属于当前用户，不能使用其他用户的封面
4. 封面状态必须是可用状态（`uploaded` 或 `active`）
5. 系统会自动获取封面的访问URL并保存到数据库中
