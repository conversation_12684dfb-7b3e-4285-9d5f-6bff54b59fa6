# 投诉服务配置文件

# 服务器配置
server:
  port: 11003  # 投诉服务端口
  name: "complaint-service"
  read_timeout_seconds: 60s
  write_timeout_seconds: 60s
  idle_timeout_seconds: 60s

# Redis配置
redis:
  db: 1  # 使用数据库1，避免与其他服务冲突


# JWT配置
jwt:
  secret: your-secret-key
  expiration: 168h # 7天
  issuer: "pxpat-complaint-service"

# Consul服务发现配置
consul:
  enabled: false  # 暂时禁用Consul
  address: "127.0.0.1:8500"
  datacenter: "dc1"
  service_name: "complaint-service"
  service_id: "complaint-service-1"
  service_address: "localhost"
  service_port: 11003
  health_check_path: "/health"
  check_interval: "15s"
  check_timeout: "5s"
  deregister_after: "30s"
  tags:
    - "user-cluster"
    - "complaint"
    - "v1.0.0"

# 业务配置 - 投诉服务特有配置

# 业务配置
complaint:
  # 限制配置
  limits:
    title_max_length: 255
    description_max_length: 5000
    max_evidence_files: 10
    max_complaints_per_day: 10
    max_rights_per_user: 5

  # 缓存配置
  cache:
    complaint_ttl: 1h
    identity_ttl: 24h
    rights_ttl: 24h
    country_ttl: 168h # 7天
    trademark_ttl: 168h # 7天
    violation_ttl: 168h # 7天

  # 文件上传配置
  file_upload:
    max_file_size: 10485760 # 10MB
    allowed_types:
      - image/jpeg
      - image/png
      - image/gif
      - application/pdf
      - application/msword
      - application/vnd.openxmlformats-officedocument.wordprocessingml.document
    max_files_per_upload: 5
    storage_path: complaint/evidence

  # 认证配置
  verification:
    auto_approve: false
    review_timeout: 72h # 3天
    certificate_valid_days: 365
    require_manual_review: true

# 存储配置
storage:
  minio:
    endpoint: localhost:9000
    access_key: minioadmin
    secret_key: minioadmin
    bucket_name: pxpat-complaint
    use_ssl: false
    region: us-east-1

# OpenTelemetry配置
otlp:
  tracing:
    enabled: false
    endpoint: http://localhost:4317
    insecure: true
    timeout: 10s
    headers: {}

  metrics:
    enabled: false
    endpoint: http://localhost:4317
    insecure: true
    timeout: 10s
    headers: {}
    interval: 15s

# 监控配置
monitoring:
  metrics:
    enable: true
    endpoint: /metrics
    interval: 15s

  tracing:
    enable: false
    endpoint: http://localhost:14268/api/traces
    sample_rate: 0.1

  health:
    enable: true
    interval: 30s
    timeout: 5s
