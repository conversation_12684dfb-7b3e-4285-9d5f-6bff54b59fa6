package dto

import (
	"time"

	"pxpat-backend/internal/content-cluster/content-management-service/model"
)

// BaseResponse 基础响应DTO
type BaseResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"msg"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp int64       `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

// PaginationRequest 分页请求DTO
type PaginationRequest struct {
	Page  int `json:"page,omitempty" form:"page"`
	Limit int `json:"limit,omitempty" form:"limit"`
}

// PaginationResponse 分页响应DTO
type PaginationResponse struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// SortRequest 排序请求DTO
type SortRequest struct {
	SortBy    string `json:"sort_by,omitempty" form:"sort_by"`
	SortOrder string `json:"sort_order,omitempty" form:"sort_order"`
}

// TimeRangeRequest 时间范围请求DTO
type TimeRangeRequest struct {
	StartTime *time.Time `json:"start_time,omitempty" form:"start_time"`
	EndTime   *time.Time `json:"end_time,omitempty" form:"end_time"`
}

// HealthCheckResponse 健康检查响应DTO
type HealthCheckResponse struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Version   string                 `json:"version"`
	Services  map[string]ServiceInfo `json:"services"`
	Uptime    int64                  `json:"uptime_seconds"`
}

// ServiceInfo 服务信息DTO
type ServiceInfo struct {
	Status       string    `json:"status"`
	ResponseTime int64     `json:"response_time_ms"`
	LastCheck    time.Time `json:"last_check"`
	Error        string    `json:"error,omitempty"`
}

// ErrorResponse 错误响应DTO
type ErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Details   string `json:"details,omitempty"`
	Timestamp int64  `json:"timestamp"`
	RequestID string `json:"request_id,omitempty"`
	Path      string `json:"path,omitempty"`
}

// ValidationError 验证错误DTO
type ValidationError struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// ValidationErrorResponse 验证错误响应DTO
type ValidationErrorResponse struct {
	Code      string            `json:"code"`
	Message   string            `json:"message"`
	Errors    []ValidationError `json:"errors"`
	Timestamp int64             `json:"timestamp"`
	RequestID string            `json:"request_id,omitempty"`
}

// OperationResult 操作结果DTO
type OperationResult struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp int64       `json:"timestamp"`
}

// BulkOperationResult 批量操作结果DTO
type BulkOperationResult struct {
	TotalCount   int                  `json:"total_count"`
	SuccessCount int                  `json:"success_count"`
	FailureCount int                  `json:"failure_count"`
	Errors       []BulkOperationError `json:"errors,omitempty"`
	Message      string               `json:"message"`
	Timestamp    int64                `json:"timestamp"`
}

// BulkOperationError 批量操作错误DTO
type BulkOperationError struct {
	Index int    `json:"index"`
	ID    string `json:"id"`
	Error string `json:"error"`
}

// MetricsResponse 指标响应DTO
type MetricsResponse struct {
	RequestCount    int64            `json:"request_count"`
	ErrorCount      int64            `json:"error_count"`
	AvgResponseTime float64          `json:"avg_response_time_ms"`
	Endpoints       map[string]int64 `json:"endpoints"`
	StatusCodes     map[string]int64 `json:"status_codes"`
	Timestamp       time.Time        `json:"timestamp"`
}

// LogEntry 日志条目DTO
type LogEntry struct {
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Timestamp time.Time              `json:"timestamp"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
	Source    string                 `json:"source,omitempty"`
}

// LogQueryRequest 日志查询请求DTO
type LogQueryRequest struct {
	Level     string     `json:"level,omitempty" form:"level"`
	Source    string     `json:"source,omitempty" form:"source"`
	Message   string     `json:"message,omitempty" form:"message"`
	StartTime *time.Time `json:"start_time,omitempty" form:"start_time"`
	EndTime   *time.Time `json:"end_time,omitempty" form:"end_time"`
	Page      int        `json:"page,omitempty" form:"page"`
	Limit     int        `json:"limit,omitempty" form:"limit"`
}

// LogQueryResponse 日志查询响应DTO
type LogQueryResponse struct {
	Logs       []*LogEntry `json:"logs"`
	Total      int         `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"total_pages"`
}

// 工具函数

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data interface{}) *BaseResponse {
	return &BaseResponse{
		Code:      0,
		Message:   "success",
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, message string) *BaseResponse {
	return &BaseResponse{
		Code:      code,
		Message:   message,
		Timestamp: time.Now().Unix(),
	}
}

// NewErrorResponseWithDetails 创建带详细信息的错误响应
func NewErrorResponseWithDetails(code int, message, details string) *ErrorResponse {
	return &ErrorResponse{
		Code:      "ERROR",
		Message:   message,
		Details:   details,
		Timestamp: time.Now().Unix(),
	}
}

// NewValidationErrorResponse 创建验证错误响应
func NewValidationErrorResponse(errors []ValidationError) *ValidationErrorResponse {
	return &ValidationErrorResponse{
		Code:      "VALIDATION_ERROR",
		Message:   "请求参数验证失败",
		Errors:    errors,
		Timestamp: time.Now().Unix(),
	}
}

// NewOperationResult 创建操作结果
func NewOperationResult(success bool, message string, data interface{}) *OperationResult {
	return &OperationResult{
		Success:   success,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewBulkOperationResult 创建批量操作结果
func NewBulkOperationResult(totalCount, successCount, failureCount int, errors []BulkOperationError) *BulkOperationResult {
	message := "操作完成"
	if failureCount > 0 {
		message = "操作部分成功"
	}

	return &BulkOperationResult{
		TotalCount:   totalCount,
		SuccessCount: successCount,
		FailureCount: failureCount,
		Errors:       errors,
		Message:      message,
		Timestamp:    time.Now().Unix(),
	}
}

// FromOperationLog 从操作日志模型转换
func FromOperationLog(log *model.OperationLog) *OperationLogResponse {
	if log == nil {
		return nil
	}

	return &OperationLogResponse{
		ID:            log.ID,
		OperatorKSUID: log.OperatorKSUID,
		OperationType: log.OperationType,
		TargetType:    log.TargetType,
		TargetKSUID:   log.TargetKSUID,
		Description:   log.Description,
		BeforeData:    log.BeforeData,
		AfterData:     log.AfterData,
		IPAddress:     log.IPAddress,
		UserAgent:     log.UserAgent,
		CreatedAt:     log.CreatedAt,
	}
}

// FromOperationLogList 从操作日志列表转换
func FromOperationLogList(logList *model.OperationLogList) *OperationLogListResponse {
	if logList == nil {
		return &OperationLogListResponse{
			Logs: []*OperationLogResponse{},
		}
	}

	logs := make([]*OperationLogResponse, len(logList.Logs))
	for i, log := range logList.Logs {
		logs[i] = FromOperationLog(log)
	}

	return &OperationLogListResponse{
		Logs:       logs,
		Total:      logList.Total,
		Page:       logList.Page,
		Limit:      logList.Limit,
		TotalPages: logList.TotalPages,
	}
}

// FromManagementConfig 从管理配置模型转换
func FromManagementConfig(config *model.ManagementConfig) *ConfigResponse {
	if config == nil {
		return nil
	}

	return &ConfigResponse{
		ID:          config.ID,
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		ConfigType:  config.ConfigType,
		Description: config.Description,
		Category:    config.Category,
		IsActive:    config.IsActive,
		CreatedAt:   config.CreatedAt,
		UpdatedAt:   config.UpdatedAt,
	}
}

// FromConfigList 从配置列表转换
func FromConfigList(configList *model.ConfigList) *ConfigListResponse {
	if configList == nil {
		return &ConfigListResponse{
			Configs: []*ConfigResponse{},
		}
	}

	configs := make([]*ConfigResponse, len(configList.Configs))
	for i, config := range configList.Configs {
		configs[i] = FromManagementConfig(config)
	}

	return &ConfigListResponse{
		Configs:    configs,
		Total:      configList.Total,
		Page:       configList.Page,
		Limit:      configList.Limit,
		TotalPages: configList.TotalPages,
	}
}

// FromConfigStats 从配置统计转换
func FromConfigStats(stats *model.ConfigStats) *ConfigStatsResponse {
	if stats == nil {
		return nil
	}

	return &ConfigStatsResponse{
		TotalConfigs:      stats.TotalConfigs,
		ActiveConfigs:     stats.ActiveConfigs,
		InactiveConfigs:   stats.InactiveConfigs,
		ConfigsByCategory: stats.ConfigsByCategory,
		ConfigsByType:     stats.ConfigsByType,
		LastUpdated:       stats.LastUpdated,
	}
}

// SetRequestID 设置请求ID
func (r *BaseResponse) SetRequestID(requestID string) {
	r.RequestID = requestID
}

// SetRequestID 设置请求ID（错误响应）
func (r *ErrorResponse) SetRequestID(requestID string) {
	r.RequestID = requestID
}

// SetPath 设置请求路径
func (r *ErrorResponse) SetPath(path string) {
	r.Path = path
}

// AddValidationError 添加验证错误
func (r *ValidationErrorResponse) AddValidationError(field, message string, value interface{}) {
	r.Errors = append(r.Errors, ValidationError{
		Field:   field,
		Message: message,
		Value:   value,
	})
}

// CalculatePagination 计算分页信息
func CalculatePagination(page, limit, total int) PaginationResponse {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}

	totalPages := (total + limit - 1) / limit
	if totalPages <= 0 {
		totalPages = 1
	}

	return PaginationResponse{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
	}
}
