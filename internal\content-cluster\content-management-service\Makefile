# 内容管理服务 Makefile
# 提供常用的开发和测试命令

.PHONY: help test test-unit test-integration test-coverage clean build run deps lint fmt vet

# 默认目标
.DEFAULT_GOAL := help

# 项目配置
PROJECT_NAME := content-management-service
GO_VERSION := 1.21
COVERAGE_THRESHOLD := 80

# 颜色定义
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

## help: 显示帮助信息
help:
	@echo "$(BLUE)内容管理服务开发工具$(NC)"
	@echo ""
	@echo "$(YELLOW)可用命令:$(NC)"
	@sed -n 's/^##//p' $(MAKEFILE_LIST) | column -t -s ':' | sed -e 's/^/ /'
	@echo ""

## deps: 下载和整理依赖
deps:
	@echo "$(YELLOW)下载依赖...$(NC)"
	go mod tidy
	go mod download
	go mod verify
	@echo "$(GREEN)✓ 依赖下载完成$(NC)"

## fmt: 格式化代码
fmt:
	@echo "$(YELLOW)格式化代码...$(NC)"
	go fmt ./...
	@echo "$(GREEN)✓ 代码格式化完成$(NC)"

## vet: 静态代码分析
vet:
	@echo "$(YELLOW)运行静态代码分析...$(NC)"
	go vet ./...
	@echo "$(GREEN)✓ 静态代码分析完成$(NC)"

## lint: 运行golangci-lint
lint:
	@echo "$(YELLOW)运行代码检查...$(NC)"
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
		echo "$(GREEN)✓ 代码检查完成$(NC)"; \
	else \
		echo "$(RED)golangci-lint未安装，跳过代码检查$(NC)"; \
	fi

## test: 运行所有测试
test: test-unit test-integration

## test-unit: 运行单元测试
test-unit:
	@echo "$(BLUE)运行单元测试...$(NC)"
	@chmod +x run_tests.sh
	@./run_tests.sh

## test-utils: 测试工具函数
test-utils:
	@echo "$(YELLOW)测试工具函数...$(NC)"
	go test -v -race ./utils/...

## test-repository: 测试Repository层
test-repository:
	@echo "$(YELLOW)测试Repository层...$(NC)"
	go test -v -race ./repository/...

## test-service: 测试Service层
test-service:
	@echo "$(YELLOW)测试Service层...$(NC)"
	go test -v -race ./external/service/...

## test-handler: 测试Handler层
test-handler:
	@echo "$(YELLOW)测试Handler层...$(NC)"
	go test -v -race ./external/handler/...

## test-coverage: 生成测试覆盖率报告
test-coverage:
	@echo "$(YELLOW)生成测试覆盖率报告...$(NC)"
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@COVERAGE=$$(go tool cover -func=coverage.out | grep total | awk '{print $$3}' | sed 's/%//'); \
	echo "$(BLUE)总体覆盖率: $(GREEN)$$COVERAGE%$(NC)"; \
	if [ $$(echo "$$COVERAGE >= $(COVERAGE_THRESHOLD)" | bc -l) -eq 1 ]; then \
		echo "$(GREEN)✓ 覆盖率达到目标 ($(COVERAGE_THRESHOLD)%)$(NC)"; \
	else \
		echo "$(YELLOW)⚠ 覆盖率未达到目标 ($(COVERAGE_THRESHOLD)%)$(NC)"; \
	fi

## test-integration: 运行集成测试
test-integration:
	@echo "$(YELLOW)运行集成测试...$(NC)"
	@chmod +x run_integration_tests.sh
	@./run_integration_tests.sh

## benchmark: 运行性能测试
benchmark:
	@echo "$(YELLOW)运行性能测试...$(NC)"
	go test -bench=. -benchmem ./...

## clean: 清理构建文件和测试结果
clean:
	@echo "$(YELLOW)清理文件...$(NC)"
	rm -f coverage.out coverage.html
	rm -rf test_results/
	rm -f $(PROJECT_NAME)
	go clean -cache -testcache -modcache
	@echo "$(GREEN)✓ 清理完成$(NC)"

## build: 构建服务
build: deps fmt vet
	@echo "$(YELLOW)构建服务...$(NC)"
	go build -o $(PROJECT_NAME) ../../../cmd/content-cluster/content-management-service/main.go
	@echo "$(GREEN)✓ 构建完成: $(PROJECT_NAME)$(NC)"

## run: 运行服务
run: build
	@echo "$(YELLOW)启动服务...$(NC)"
	./$(PROJECT_NAME)

## dev: 开发模式运行
dev:
	@echo "$(YELLOW)开发模式启动...$(NC)"
	go run ../../../cmd/content-cluster/content-management-service/main.go

## docker-build: 构建Docker镜像
docker-build:
	@echo "$(YELLOW)构建Docker镜像...$(NC)"
	docker build -t $(PROJECT_NAME):latest -f ../../../docker/Dockerfile.content-management .
	@echo "$(GREEN)✓ Docker镜像构建完成$(NC)"

## docker-run: 运行Docker容器
docker-run:
	@echo "$(YELLOW)运行Docker容器...$(NC)"
	docker run -p 12010:12010 $(PROJECT_NAME):latest

## check: 运行所有检查 (格式化、静态分析、测试)
check: fmt vet lint test
	@echo "$(GREEN)✓ 所有检查完成$(NC)"

## ci: CI环境运行的命令
ci: deps fmt vet test-coverage
	@echo "$(GREEN)✓ CI检查完成$(NC)"

## install-tools: 安装开发工具
install-tools:
	@echo "$(YELLOW)安装开发工具...$(NC)"
	@if ! command -v golangci-lint >/dev/null 2>&1; then \
		echo "安装golangci-lint..."; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
	fi
	@if ! command -v mockery >/dev/null 2>&1; then \
		echo "安装mockery..."; \
		go install github.com/vektra/mockery/v2@latest; \
	fi
	@echo "$(GREEN)✓ 开发工具安装完成$(NC)"

## mock: 生成Mock文件
mock:
	@echo "$(YELLOW)生成Mock文件...$(NC)"
	@if command -v mockery >/dev/null 2>&1; then \
		mockery --all --output=./mocks --case=underscore; \
		echo "$(GREEN)✓ Mock文件生成完成$(NC)"; \
	else \
		echo "$(RED)mockery未安装，请先运行 make install-tools$(NC)"; \
	fi

## version: 显示版本信息
version:
	@echo "$(BLUE)版本信息:$(NC)"
	@echo "项目: $(PROJECT_NAME)"
	@echo "Go版本: $(GO_VERSION)"
	@go version
	@echo "覆盖率目标: $(COVERAGE_THRESHOLD)%"

## env: 显示环境信息
env:
	@echo "$(BLUE)环境信息:$(NC)"
	@echo "GOPATH: $(GOPATH)"
	@echo "GOROOT: $(GOROOT)"
	@echo "GOOS: $(GOOS)"
	@echo "GOARCH: $(GOARCH)"
	@echo "PWD: $(PWD)"
