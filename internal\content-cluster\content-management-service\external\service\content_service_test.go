package service

import (
	"context"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"pxpat-backend/internal/content-cluster/content-management-service/client"
	"pxpat-backend/internal/content-cluster/content-management-service/repository"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
	"pxpat-backend/internal/content-cluster/content-management-service/utils"
)

// MockVideoServiceClient 模拟视频服务客户端
type MockVideoServiceClient struct {
	mock.Mock
}

func (m *MockVideoServiceClient) GetVideoByKSUID(ctx context.Context, ksuid string) (*types.VideoContent, error) {
	args := m.Called(ctx, ksuid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.VideoContent), args.Error(1)
}

func (m *MockVideoServiceClient) GetVideosByKSUIDs(ctx context.Context, ksuids []string) ([]*types.VideoContent, error) {
	args := m.Called(ctx, ksuids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*types.VideoContent), args.Error(1)
}

func (m *MockVideoServiceClient) GetVideosByFilters(ctx context.Context, filters *types.VideoFilters) ([]*types.VideoContent, int64, error) {
	args := m.Called(ctx, filters)
	if args.Get(0) == nil {
		return nil, 0, args.Error(2)
	}
	return args.Get(0).([]*types.VideoContent), args.Get(1).(int64), args.Error(2)
}

func (m *MockVideoServiceClient) GetVideosByUserKSUID(ctx context.Context, userKSUID string, page, limit int) ([]*types.VideoContent, int64, error) {
	args := m.Called(ctx, userKSUID, page, limit)
	if args.Get(0) == nil {
		return nil, 0, args.Error(2)
	}
	return args.Get(0).([]*types.VideoContent), args.Get(1).(int64), args.Error(2)
}

func (m *MockVideoServiceClient) UpdateVideoStatus(ctx context.Context, ksuid, status string) error {
	args := m.Called(ctx, ksuid, status)
	return args.Error(0)
}

func (m *MockVideoServiceClient) BatchUpdateVideoStatus(ctx context.Context, ksuids []string, status string) error {
	args := m.Called(ctx, ksuids, status)
	return args.Error(0)
}

func (m *MockVideoServiceClient) DeleteVideo(ctx context.Context, ksuid string) error {
	args := m.Called(ctx, ksuid)
	return args.Error(0)
}

func (m *MockVideoServiceClient) BatchDeleteVideos(ctx context.Context, ksuids []string) error {
	args := m.Called(ctx, ksuids)
	return args.Error(0)
}

func (m *MockVideoServiceClient) GetVideoStats(ctx context.Context, startTime, endTime *time.Time) (*types.VideoStats, error) {
	args := m.Called(ctx, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.VideoStats), args.Error(1)
}

// MockInteractionServiceClient 模拟交互服务客户端
type MockInteractionServiceClient struct {
	mock.Mock
}

func (m *MockInteractionServiceClient) GetInteractionStats(ctx context.Context, contentKSUID string) (*types.InteractionStats, error) {
	args := m.Called(ctx, contentKSUID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.InteractionStats), args.Error(1)
}

func (m *MockInteractionServiceClient) BatchGetInteractionStats(ctx context.Context, contentKSUIDs []string) (map[string]*types.InteractionStats, error) {
	args := m.Called(ctx, contentKSUIDs)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*types.InteractionStats), args.Error(1)
}

func (m *MockInteractionServiceClient) GetOverallInteractionStats(ctx context.Context, startTime, endTime *time.Time) (*types.OverallInteractionStats, error) {
	args := m.Called(ctx, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.OverallInteractionStats), args.Error(1)
}

// MockRepository 模拟仓库接口
type MockContentCacheRepository struct {
	mock.Mock
}

func (m *MockContentCacheRepository) Set(ctx context.Context, contentKSUID, contentType string, data map[string]interface{}, ttl time.Duration) error {
	args := m.Called(ctx, contentKSUID, contentType, data, ttl)
	return args.Error(0)
}

func (m *MockContentCacheRepository) Get(ctx context.Context, contentKSUID string) (map[string]interface{}, error) {
	args := m.Called(ctx, contentKSUID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockContentCacheRepository) Delete(ctx context.Context, contentKSUID string) error {
	args := m.Called(ctx, contentKSUID)
	return args.Error(0)
}

func (m *MockContentCacheRepository) BatchDelete(ctx context.Context, contentKSUIDs []string) error {
	args := m.Called(ctx, contentKSUIDs)
	return args.Error(0)
}

func (m *MockContentCacheRepository) GetStats(ctx context.Context) (*types.CacheStats, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.CacheStats), args.Error(1)
}

func (m *MockContentCacheRepository) CleanExpired(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

// 创建测试服务实例
func createTestContentService() (*ContentManagementServiceImpl, *MockVideoServiceClient, *MockInteractionServiceClient, *MockContentCacheRepository) {
	mockVideoClient := &MockVideoServiceClient{}
	mockInteractionClient := &MockInteractionServiceClient{}
	mockCacheRepo := &MockContentCacheRepository{}

	// 创建真实的工具实例
	converter := utils.NewContentConverter()
	validator := utils.NewContentValidator()

	// 创建模拟的Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15,
	})

	service := &ContentManagementServiceImpl{
		videoClient:       mockVideoClient,
		interactionClient: mockInteractionClient,
		cacheRepo:         mockCacheRepo,
		converter:         converter,
		validator:         validator,
		redis:             rdb,
	}

	return service, mockVideoClient, mockInteractionClient, mockCacheRepo
}

func TestContentManagementService_GetBaseContents(t *testing.T) {
	service, mockVideoClient, _, mockCacheRepo := createTestContentService()
	ctx := context.Background()

	// 准备测试数据
	filters := &types.ContentFilters{
		ContentTypes: []string{"video"},
		Status:       "published",
		Page:         1,
		Limit:        10,
	}

	expectedVideos := []*types.VideoContent{
		{
			KSUID:       "video_1",
			Title:       "测试视频1",
			Description: "描述1",
			UserKSUID:   "user_1",
			Status:      "published",
			CategoryID:  1,
			Tags:        []string{"tag1"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			KSUID:       "video_2",
			Title:       "测试视频2",
			Description: "描述2",
			UserKSUID:   "user_2",
			Status:      "published",
			CategoryID:  2,
			Tags:        []string{"tag2"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	// 设置mock期望
	videoFilters := &types.VideoFilters{
		Status:    "published",
		Page:      1,
		Limit:     10,
		SortBy:    "created_at",
		SortOrder: "desc",
	}
	mockVideoClient.On("GetVideosByFilters", ctx, videoFilters).Return(expectedVideos, int64(2), nil)

	// 执行测试
	results, total, err := service.GetBaseContents(ctx, filters)

	// 验证结果
	require.NoError(t, err)
	assert.Len(t, results, 2)
	assert.Equal(t, int64(2), total)

	// 验证转换结果
	assert.Equal(t, "video_1", results[0].KSUID)
	assert.Equal(t, "video", results[0].ContentType)
	assert.Equal(t, "测试视频1", results[0].Title)

	assert.Equal(t, "video_2", results[1].KSUID)
	assert.Equal(t, "video", results[1].ContentType)
	assert.Equal(t, "测试视频2", results[1].Title)

	// 验证mock调用
	mockVideoClient.AssertExpectations(t)
}

func TestContentManagementService_GetVideoContents(t *testing.T) {
	service, mockVideoClient, mockInteractionClient, _ := createTestContentService()
	ctx := context.Background()

	// 准备测试数据
	filters := &types.ContentFilters{
		ContentTypes: []string{"video"},
		Status:       "published",
		Page:         1,
		Limit:        10,
	}

	expectedVideos := []*types.VideoContent{
		{
			KSUID:      "video_1",
			Title:      "测试视频1",
			UserKSUID:  "user_1",
			Status:     "published",
			Duration:   300,
			Resolution: "1920x1080",
			FileSize:   1024000,
		},
	}

	expectedInteractionStats := map[string]*types.InteractionStats{
		"video_1": {
			Views:     1000,
			Likes:     50,
			Favorites: 20,
		},
	}

	// 设置mock期望
	videoFilters := &types.VideoFilters{
		Status:    "published",
		Page:      1,
		Limit:     10,
		SortBy:    "created_at",
		SortOrder: "desc",
	}
	mockVideoClient.On("GetVideosByFilters", ctx, videoFilters).Return(expectedVideos, int64(1), nil)
	mockInteractionClient.On("BatchGetInteractionStats", ctx, []string{"video_1"}).Return(expectedInteractionStats, nil)

	// 执行测试
	results, total, err := service.GetVideoContents(ctx, filters)

	// 验证结果
	require.NoError(t, err)
	assert.Len(t, results, 1)
	assert.Equal(t, int64(1), total)

	// 验证详细信息
	result := results[0]
	assert.Equal(t, "video_1", result.KSUID)
	assert.Equal(t, "测试视频1", result.Title)
	assert.Equal(t, 300, result.Duration)
	assert.Equal(t, "1920x1080", result.Resolution)
	assert.Equal(t, int64(1024000), result.FileSize)
	assert.Equal(t, int64(1000), result.InteractionStats.Views)
	assert.Equal(t, int64(50), result.InteractionStats.Likes)
	assert.Equal(t, int64(20), result.InteractionStats.Favorites)

	// 验证mock调用
	mockVideoClient.AssertExpectations(t)
	mockInteractionClient.AssertExpectations(t)
}

func TestContentManagementService_UpdateContentStatus(t *testing.T) {
	service, mockVideoClient, _, mockCacheRepo := createTestContentService()
	ctx := context.Background()

	contentKSUID := "video_1"
	newStatus := "archived"
	userKSUID := "user_1"

	// 设置mock期望
	mockVideoClient.On("UpdateVideoStatus", ctx, contentKSUID, newStatus).Return(nil)
	mockCacheRepo.On("Delete", ctx, contentKSUID).Return(nil)

	// 执行测试
	err := service.UpdateContentStatus(ctx, contentKSUID, newStatus, userKSUID)

	// 验证结果
	require.NoError(t, err)

	// 验证mock调用
	mockVideoClient.AssertExpectations(t)
	mockCacheRepo.AssertExpectations(t)
}

func TestContentManagementService_DeleteContent(t *testing.T) {
	service, mockVideoClient, _, mockCacheRepo := createTestContentService()
	ctx := context.Background()

	contentKSUID := "video_1"
	userKSUID := "user_1"

	// 设置mock期望
	mockVideoClient.On("DeleteVideo", ctx, contentKSUID).Return(nil)
	mockCacheRepo.On("Delete", ctx, contentKSUID).Return(nil)

	// 执行测试
	err := service.DeleteContent(ctx, contentKSUID, userKSUID)

	// 验证结果
	require.NoError(t, err)

	// 验证mock调用
	mockVideoClient.AssertExpectations(t)
	mockCacheRepo.AssertExpectations(t)
}
