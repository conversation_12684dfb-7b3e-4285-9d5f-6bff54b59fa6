#!/bin/bash

# 监控系统设置脚本
# 用于部署和配置Prometheus、Grafana、Alertmanager等监控组件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/docker"

echo -e "${BLUE}=== 设置监控系统 ===${NC}"

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查依赖...${NC}"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}错误: Docker未安装${NC}"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo -e "${RED}错误: Docker Compose未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查通过${NC}"
}

# 创建监控配置目录
create_config_dirs() {
    echo -e "${YELLOW}创建配置目录...${NC}"
    
    # 创建Prometheus配置目录
    mkdir -p "$DOCKER_DIR/prometheus/rules"
    mkdir -p "$DOCKER_DIR/prometheus/data"
    
    # 创建Grafana配置目录
    mkdir -p "$DOCKER_DIR/grafana/provisioning/datasources"
    mkdir -p "$DOCKER_DIR/grafana/provisioning/dashboards"
    mkdir -p "$DOCKER_DIR/grafana/dashboards/content-management"
    mkdir -p "$DOCKER_DIR/grafana/dashboards/infrastructure"
    mkdir -p "$DOCKER_DIR/grafana/dashboards/business"
    mkdir -p "$DOCKER_DIR/grafana/data"
    
    # 创建Alertmanager配置目录
    mkdir -p "$DOCKER_DIR/alertmanager/data"
    mkdir -p "$DOCKER_DIR/alertmanager/templates"
    
    # 设置权限
    sudo chown -R 472:472 "$DOCKER_DIR/grafana/data" 2>/dev/null || true
    sudo chown -R 65534:65534 "$DOCKER_DIR/prometheus/data" 2>/dev/null || true
    sudo chown -R 65534:65534 "$DOCKER_DIR/alertmanager/data" 2>/dev/null || true
    
    echo -e "${GREEN}✓ 配置目录创建完成${NC}"
}

# 生成Grafana仪表板
generate_dashboards() {
    echo -e "${YELLOW}生成Grafana仪表板...${NC}"
    
    # 内容管理服务仪表板
    cat > "$DOCKER_DIR/grafana/dashboards/content-management/service-overview.json" << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "Content Management Service Overview",
    "tags": ["content-management", "service"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Service Status",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"content-management-service\"}",
            "legendFormat": "{{instance}}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"content-management-service\"}[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
EOF
    
    echo -e "${GREEN}✓ 仪表板生成完成${NC}"
}

# 生成Alertmanager模板
generate_alert_templates() {
    echo -e "${YELLOW}生成告警模板...${NC}"
    
    cat > "$DOCKER_DIR/alertmanager/templates/default.tmpl" << 'EOF'
{{ define "__subject" }}[{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] {{ .GroupLabels.SortedPairs.Values | join " " }} {{ if gt (len .CommonLabels) (len .GroupLabels) }}({{ with .CommonLabels.Remove .GroupLabels.Names }}{{ .Values | join " " }}{{ end }}){{ end }}{{ end }}

{{ define "__description" }}{{ end }}

{{ define "__text_alert_list" }}{{ range . }}Labels:
{{ range .Labels.SortedPairs }} - {{ .Name }} = {{ .Value }}
{{ end }}Annotations:
{{ range .Annotations.SortedPairs }} - {{ .Name }} = {{ .Value }}
{{ end }}Source: {{ .GeneratorURL }}
{{ end }}{{ end }}

{{ define "slack.default.title" }}{{ template "__subject" . }}{{ end }}
{{ define "slack.default.text" }}{{ template "__text_alert_list" .Alerts }}{{ end }}
EOF
    
    echo -e "${GREEN}✓ 告警模板生成完成${NC}"
}

# 启动监控服务
start_monitoring_services() {
    echo -e "${YELLOW}启动监控服务...${NC}"
    
    cd "$DOCKER_DIR"
    
    # 创建监控专用的docker-compose文件
    cat > docker-compose.monitoring.yml << 'EOF'
version: '3.8'

services:
  # Prometheus监控
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: content-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - monitoring-network

  # Alertmanager告警管理
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: content-alertmanager
    restart: unless-stopped
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - ./alertmanager/templates:/etc/alertmanager/templates:ro
      - alertmanager_data:/alertmanager
    ports:
      - "9093:9093"
    networks:
      - monitoring-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:10.0.0
    container_name: content-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3000:3000"
    networks:
      - monitoring-network

  # Node Exporter系统监控
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: content-node-exporter
    restart: unless-stopped
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    ports:
      - "9100:9100"
    networks:
      - monitoring-network

  # cAdvisor容器监控
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: content-cadvisor
    restart: unless-stopped
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker:/var/lib/docker:ro
      - /cgroup:/cgroup:ro
    ports:
      - "8080:8080"
    networks:
      - monitoring-network

networks:
  monitoring-network:
    driver: bridge

volumes:
  prometheus_data:
  alertmanager_data:
  grafana_data:
EOF
    
    # 启动监控服务
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.monitoring.yml up -d
    else
        docker compose -f docker-compose.monitoring.yml up -d
    fi
    
    echo -e "${GREEN}✓ 监控服务启动完成${NC}"
}

# 等待服务就绪
wait_for_services() {
    echo -e "${YELLOW}等待监控服务就绪...${NC}"
    
    # 等待Prometheus
    echo "等待Prometheus..."
    timeout=60
    while ! curl -f http://localhost:9090/-/ready &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            echo -e "${RED}✗ Prometheus启动超时${NC}"
            exit 1
        fi
    done
    
    # 等待Grafana
    echo "等待Grafana..."
    timeout=60
    while ! curl -f http://localhost:3000/api/health &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            echo -e "${RED}✗ Grafana启动超时${NC}"
            exit 1
        fi
    done
    
    # 等待Alertmanager
    echo "等待Alertmanager..."
    timeout=60
    while ! curl -f http://localhost:9093/-/ready &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            echo -e "${RED}✗ Alertmanager启动超时${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✓ 所有监控服务就绪${NC}"
}

# 验证监控配置
verify_monitoring() {
    echo -e "${YELLOW}验证监控配置...${NC}"
    
    # 检查Prometheus配置
    if curl -f http://localhost:9090/api/v1/status/config &>/dev/null; then
        echo -e "${GREEN}✓ Prometheus配置正常${NC}"
    else
        echo -e "${RED}✗ Prometheus配置异常${NC}"
    fi
    
    # 检查告警规则
    if curl -f http://localhost:9090/api/v1/rules &>/dev/null; then
        echo -e "${GREEN}✓ 告警规则加载正常${NC}"
    else
        echo -e "${RED}✗ 告警规则加载异常${NC}"
    fi
    
    # 检查Alertmanager配置
    if curl -f http://localhost:9093/api/v1/status &>/dev/null; then
        echo -e "${GREEN}✓ Alertmanager配置正常${NC}"
    else
        echo -e "${RED}✗ Alertmanager配置异常${NC}"
    fi
}

# 显示访问信息
show_access_info() {
    echo -e "${BLUE}=== 监控系统访问信息 ===${NC}"
    echo "Prometheus: http://localhost:9090"
    echo "Grafana: http://localhost:3000 (admin/admin123)"
    echo "Alertmanager: http://localhost:9093"
    echo "Node Exporter: http://localhost:9100"
    echo "cAdvisor: http://localhost:8080"
    echo ""
    echo -e "${YELLOW}建议:${NC}"
    echo "1. 登录Grafana后修改默认密码"
    echo "2. 配置Slack Webhook URL用于告警通知"
    echo "3. 根据实际需求调整告警阈值"
}

# 主函数
main() {
    check_dependencies
    create_config_dirs
    generate_dashboards
    generate_alert_templates
    start_monitoring_services
    wait_for_services
    verify_monitoring
    show_access_info
    
    echo -e "${GREEN}🎉 监控系统设置完成！${NC}"
}

# 执行主函数
main "$@"
