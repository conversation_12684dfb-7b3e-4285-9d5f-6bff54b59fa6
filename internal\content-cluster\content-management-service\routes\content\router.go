package content

import (
	"time"

	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/content-cluster/content-management-service/external/handler"
	"pxpat-backend/internal/content-cluster/content-management-service/middleware"
)

// RegisterContentRoutes 注册内容管理路由
func RegisterContentRoutes(
	group *gin.RouterGroup,
	contentHandler *handler.ContentHandler,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 注册外部API路由
	RegisterExternalRoutes(group, contentHandler, authMiddleware, permissionMiddleware, rateLimitMiddleware)

	// 注册内部API路由
	RegisterInternalRoutes(group, contentHandler, authMiddleware, permissionMiddleware, rateLimitMiddleware)
}

// RegisterExternalRoutes 注册外部API路由
func RegisterExternalRoutes(
	group *gin.RouterGroup,
	contentHandler *handler.ContentHandler,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 内容管理路由组
	contents := group.Group("/contents")
	{
		// 公开接口（可选认证）
		contents.GET("",
			authMiddleware.OptionalAuth(),
			rateLimitMiddleware.APIRateLimit(100, time.Minute),
			contentHandler.GetBaseContents,
		)

		// 需要认证的接口
		authenticated := contents.Group("")
		authenticated.Use(authMiddleware.RequireAuth())
		authenticated.Use(permissionMiddleware.RequirePermission("content:read"))
		{
			// 内容详情
			authenticated.GET("/:content_ksuid",
				rateLimitMiddleware.APIRateLimit(200, time.Minute),
				contentHandler.GetContentWithDetails,
			)

			// 更新内容状态
			authenticated.PUT("/:content_ksuid/status",
				permissionMiddleware.RequireAnyPermission("content:write", "content:manage"),
				permissionMiddleware.RequireContentOwnership(),
				rateLimitMiddleware.APIRateLimit(50, time.Minute),
				contentHandler.UpdateContentStatus,
			)

			// 删除内容
			authenticated.DELETE("/:content_ksuid",
				permissionMiddleware.RequireAnyPermission("content:delete", "content:manage"),
				permissionMiddleware.RequireContentOwnership(),
				rateLimitMiddleware.APIRateLimit(20, time.Minute),
				contentHandler.DeleteContent,
			)

			// 批量操作
			batch := authenticated.Group("/batch")
			batch.Use(permissionMiddleware.RequirePermission("batch:operate"))
			batch.Use(rateLimitMiddleware.BatchOperationRateLimit(10, time.Minute))
			{
				batch.PUT("/status", contentHandler.BatchUpdateContentStatus)
				batch.POST("/delete", contentHandler.BatchDeleteContents)
			}
		}
	}

	// 用户内容管理路由
	users := group.Group("/users")
	users.Use(authMiddleware.RequireAuth())
	users.Use(permissionMiddleware.RequirePermission("content:read"))
	{
		// 获取用户的所有内容
		users.GET("/:user_ksuid/contents",
			rateLimitMiddleware.APIRateLimit(100, time.Minute),
			contentHandler.GetUserAllContents,
		)

		// 获取用户指定类型的内容
		users.GET("/:user_ksuid/contents/:content_type",
			rateLimitMiddleware.APIRateLimit(100, time.Minute),
			contentHandler.GetUserContentsByType,
		)
	}

	// 按类型管理路由
	videos := group.Group("/videos")
	videos.Use(authMiddleware.RequireAuth())
	videos.Use(permissionMiddleware.RequirePermission("content:read"))
	{
		videos.GET("",
			rateLimitMiddleware.APIRateLimit(100, time.Minute),
			contentHandler.GetVideoContents,
		)
	}

	// 预留小说管理路由
	novels := group.Group("/novels")
	novels.Use(authMiddleware.RequireAuth())
	novels.Use(permissionMiddleware.RequirePermission("content:read"))
	{
		// TODO: 实现小说内容管理接口
		// novels.GET("", contentHandler.GetNovelContents)
	}

	// 预留音乐管理路由
	music := group.Group("/music")
	music.Use(authMiddleware.RequireAuth())
	music.Use(permissionMiddleware.RequirePermission("content:read"))
	{
		// TODO: 实现音乐内容管理接口
		// music.GET("", contentHandler.GetMusicContents)
	}
}

// RegisterInternalRoutes 注册内部API路由
func RegisterInternalRoutes(
	group *gin.RouterGroup,
	contentHandler *handler.ContentHandler,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 内部API路由组
	internal := group.Group("/internal/content")
	{
		// 健康检查（无需认证）
		internal.GET("/health", contentHandler.HealthCheck)

		// 内部服务间调用（需要服务认证）
		// TODO: 实现服务间认证中间件
		// internal.Use(serviceAuthMiddleware.RequireServiceAuth())
		{
			// 内容同步接口
			// internal.POST("/sync", contentHandler.SyncContent)

			// 缓存刷新接口
			// internal.POST("/cache/refresh", contentHandler.RefreshCache)

			// 批量内容查询接口
			// internal.POST("/batch/query", contentHandler.BatchQueryContents)
		}
	}
}
