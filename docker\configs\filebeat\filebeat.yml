# Filebeat 配置文件 - pxpat 微服务集群日志收集

# 文件输入配置
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/pxpat/**/*.log
  fields:
    project: pxpat-backend
    environment: development
  fields_under_root: true
  json.keys_under_root: true
  json.add_error_key: true
  json.message_key: message
  multiline.pattern: '^\{'
  multiline.negate: true
  multiline.match: after
  scan_frequency: 10s
  harvester_buffer_size: 16384
  max_bytes: 10485760

# Docker 容器日志收集
- type: container
  enabled: true
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"
    - decode_json_fields:
        fields: ["message"]
        target: ""
        overwrite_keys: true

# 处理器配置
processors:
  # 添加主机信息
  - add_host_metadata:
      when.not.contains.tags: forwarded
  
  # 添加 Docker 元数据
  - add_docker_metadata: ~
  
  # 删除不需要的字段
  - drop_fields:
      fields: ["agent", "ecs", "host.architecture", "host.os"]
  
  # 重命名字段
  - rename:
      fields:
        - from: "container.name"
          to: "intra"
      ignore_missing: true

# 输出配置
output.logstash:
  hosts: ["logstash:5044"]
  compression_level: 3
  bulk_max_size: 2048
  template.name: "pxpat-logs"
  template.pattern: "pxpat-logs-*"

# 日志配置
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644

# 监控配置
monitoring.enabled: true
monitoring.elasticsearch:
  hosts: ["elasticsearch:9200"]

# 设置配置
setup.template.settings:
  index.number_of_shards: 1
  index.number_of_replicas: 0
  index.refresh_interval: 5s

# Kibana 配置
setup.kibana:
  host: "kibana:5601"

# 队列配置
queue.mem:
  events: 4096
  flush.min_events: 512
  flush.timeout: 1s 