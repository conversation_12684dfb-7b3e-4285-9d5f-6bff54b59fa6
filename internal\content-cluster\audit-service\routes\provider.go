package routes

import (
	externalHandler "pxpat-backend/internal/content-cluster/audit-service/external/handler"
	serviceHandler "pxpat-backend/internal/content-cluster/audit-service/intra/handler"
	"pxpat-backend/internal/content-cluster/audit-service/types"
	"pxpat-backend/pkg/auth"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// ProvideGinEngine 提供Gin引擎
func ProvideGinEngine(
	cfg *types.Config,
	jwtManager *auth.Manager,
	externalAuditHandler *externalHandler.AuditTasksHandler,
	serviceAuditHandler *serviceHandler.ServiceAuditHandler,
	healthHandler *consul.HealthHandler,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建服务
	router := gin.Default()
	router.Use(cors.CORSMiddleware(cfg.Security.Cors))
	log.Info().Msg("Middleware configured")

	RegisterRoutes(router, jwtManager, externalAuditHandler, serviceAuditHandler)
	log.Info().Msg("Routes registered")

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)
	log.Info().Msg("Health routes registered")

	log.Info().Msg("Gin engine initialized successfully")
	return router
}
