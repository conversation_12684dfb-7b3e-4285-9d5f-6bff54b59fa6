# 开发环境完整配置
version: '3.8'

services:
  # 内容管理服务
  content-management-service:
    build:
      context: ../../..
      dockerfile: docker/services/content-management/Dockerfile
    container_name: content-management-service
    restart: unless-stopped
    ports:
      - "12010:12010"
    environment:
      - GO_ENV=development
      - GIN_MODE=debug
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=content_user
      - DB_PASSWORD=content_password
      - DB_NAME=content_management
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - JWT_SECRET_KEY=dev-jwt-secret-key
      - JWT_TOKEN_DURATION=24h
      - JWT_REFRESH_DURATION=168h
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      - CONSUL_DATACENTER=dc1
      - LOG_LEVEL=debug
      - LOG_FORMAT=text
    volumes:
      - ../logs:/app/logs
      - ../configs:/app/config:ro
    depends_on:
      - postgres
      - redis
      - consul
    networks:
      - content-network
      - database-network
      - service-discovery-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:12010/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL 数据库
  postgres:
    image: postgres:17-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=content_management
      - POSTGRES_USER=content_user
      - POSTGRES_PASSWORD=content_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - D:\DevelopData\data:/var/lib/postgresql/data
      - ../configs/init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - database-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U content_user -d content_management"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:8-alpine
    container_name: redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - database-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Consul 服务发现
  consul:
    image: consul:1.16
    container_name: consul
    restart: unless-stopped
    command: agent -server -bootstrap-expect=1 -ui -client=0.0.0.0 -bind=0.0.0.0
    environment:
      - CONSUL_DATACENTER=dc1
    volumes:
      - consul_data:/consul/data
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    networks:
      - service-discovery-network
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MinIO 对象存储
  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    container_name: pxpat-minio
    ports:
      - "9000:9000"  # API端口
      - "9001:9001"  # Web控制台端口
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    command: server --console-address ":9001" /data
    networks:
      - pxpat-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped

# 网络配置
networks:
  content-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  database-network:
    driver: bridge
  service-discovery-network:
    driver: bridge
  pxpat-network:
    driver: bridge

# 数据卷配置
volumes:
  redis_data:
    driver: local
  consul_data:
    driver: local
  minio_data:
    driver: local
