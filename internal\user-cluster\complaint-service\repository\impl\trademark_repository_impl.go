package impl

import (
	"context"

	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// TrademarkRepositoryImpl 商标权仓储实现
type TrademarkRepositoryImpl struct {
	db           *gorm.DB
	cache        cache.Manager
	cacheKeyBase string
}

// NewTrademarkRepository 创建商标权仓储实例
func NewTrademarkRepository(db *gorm.DB, cache cache.Manager) repository.TrademarkRepository {
	return &TrademarkRepositoryImpl{
		db:           db,
		cache:        cache,
		cacheKeyBase: "trademark:",
	}
}

// Create 创建商标权
func (r *TrademarkRepositoryImpl) Create(ctx context.Context, trademark *model.Trademark) error {
	return r.db.WithContext(ctx).Create(trademark).Error
}

// GetByID 根据ID获取商标权
func (r *TrademarkRepositoryImpl) GetByID(ctx context.Context, id uint) (*model.Trademark, error) {
	var trademark model.Trademark
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&trademark).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrTrademarkNotFound
		}
		return nil, err
	}
	return &trademark, nil
}

// Update 更新商标权
func (r *TrademarkRepositoryImpl) Update(ctx context.Context, trademark *model.Trademark) error {
	return r.db.WithContext(ctx).Save(trademark).Error
}

// Delete 删除商标权
func (r *TrademarkRepositoryImpl) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.Trademark{}).Error
}

// GetByRightsKSUID 根据权益认证KSUID获取商标权列表
func (r *TrademarkRepositoryImpl) GetByRightsKSUID(ctx context.Context, rightsKSUID string) ([]*model.Trademark, error) {
	var trademarks []*model.Trademark
	err := r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).Find(&trademarks).Error
	if err != nil {
		return nil, err
	}
	return trademarks, nil
}

// GetByCategory 根据商标类别获取商标权列表
func (r *TrademarkRepositoryImpl) GetByCategory(ctx context.Context, categoryNumber int) ([]*model.Trademark, error) {
	var trademarks []*model.Trademark
	err := r.db.WithContext(ctx).Where("category_number = ?", categoryNumber).Find(&trademarks).Error
	if err != nil {
		return nil, err
	}
	return trademarks, nil
}

// GetByCountry 根据国家代码获取商标权列表
func (r *TrademarkRepositoryImpl) GetByCountry(ctx context.Context, countryCode string) ([]*model.Trademark, error) {
	var trademarks []*model.Trademark
	err := r.db.WithContext(ctx).Where("country_code = ?", countryCode).Find(&trademarks).Error
	if err != nil {
		return nil, err
	}
	return trademarks, nil
}

// BatchCreate 批量创建商标权
func (r *TrademarkRepositoryImpl) BatchCreate(ctx context.Context, trademarks []*model.Trademark) error {
	if len(trademarks) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(&trademarks).Error
}

// BatchDelete 批量删除商标权
func (r *TrademarkRepositoryImpl) BatchDelete(ctx context.Context, rightsKSUID string) error {
	return r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).Delete(&model.Trademark{}).Error
}

// GetDB 获取数据库实例
func (r *TrademarkRepositoryImpl) GetDB() interface{} {
	return r.db
}
