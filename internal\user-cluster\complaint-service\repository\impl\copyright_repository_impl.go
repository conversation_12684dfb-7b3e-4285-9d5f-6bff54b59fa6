package impl

import (
	"context"

	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// CopyrightRepositoryImpl 著作权仓储实现
type CopyrightRepositoryImpl struct {
	db           *gorm.DB
	cache        cache.Manager
	cacheKeyBase string
}

// NewCopyrightRepository 创建著作权仓储实例
func NewCopyrightRepository(db *gorm.DB, cache cache.Manager) repository.CopyrightRepository {
	return &CopyrightRepositoryImpl{
		db:           db,
		cache:        cache,
		cacheKeyBase: "copyright:",
	}
}

// Create 创建著作权
func (r *CopyrightRepositoryImpl) Create(ctx context.Context, copyright *model.Copyright) error {
	return r.db.WithContext(ctx).Create(copyright).Error
}

// GetByID 根据ID获取著作权
func (r *CopyrightRepositoryImpl) GetByID(ctx context.Context, id uint) (*model.Copyright, error) {
	var copyright model.Copyright
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&copyright).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrCopyrightNotFound
		}
		return nil, err
	}
	return &copyright, nil
}

// Update 更新著作权
func (r *CopyrightRepositoryImpl) Update(ctx context.Context, copyright *model.Copyright) error {
	return r.db.WithContext(ctx).Save(copyright).Error
}

// Delete 删除著作权
func (r *CopyrightRepositoryImpl) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.Copyright{}).Error
}

// GetByRightsKSUID 根据权益认证KSUID获取著作权列表
func (r *CopyrightRepositoryImpl) GetByRightsKSUID(ctx context.Context, rightsKSUID string) ([]*model.Copyright, error) {
	var copyrights []*model.Copyright
	err := r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).Find(&copyrights).Error
	if err != nil {
		return nil, err
	}
	return copyrights, nil
}

// GetByType 根据著作权类型获取著作权列表
func (r *CopyrightRepositoryImpl) GetByType(ctx context.Context, copyrightType model.CopyrightType) ([]*model.Copyright, error) {
	var copyrights []*model.Copyright
	err := r.db.WithContext(ctx).Where("copyright_type = ?", copyrightType).Find(&copyrights).Error
	if err != nil {
		return nil, err
	}
	return copyrights, nil
}

// GetByCountry 根据国家代码获取著作权列表
func (r *CopyrightRepositoryImpl) GetByCountry(ctx context.Context, countryCode string) ([]*model.Copyright, error) {
	var copyrights []*model.Copyright
	err := r.db.WithContext(ctx).Where("country_code = ?", countryCode).Find(&copyrights).Error
	if err != nil {
		return nil, err
	}
	return copyrights, nil
}

// BatchCreate 批量创建著作权
func (r *CopyrightRepositoryImpl) BatchCreate(ctx context.Context, copyrights []*model.Copyright) error {
	if len(copyrights) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(&copyrights).Error
}

// BatchDelete 批量删除著作权
func (r *CopyrightRepositoryImpl) BatchDelete(ctx context.Context, rightsKSUID string) error {
	return r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).Delete(&model.Copyright{}).Error
}

// GetDB 获取数据库实例
func (r *CopyrightRepositoryImpl) GetDB() interface{} {
	return r.db
}
