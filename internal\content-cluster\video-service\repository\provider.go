package repository

import (
	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/pkg/cache"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideRepositories 提供Repository层
func ProvideRepositories(
	db *gorm.DB,
	rdb *redis.Client,
	cacheManager cache.Manager,
	userServiceClient client.UserServiceClient,
) (
	*ContentRepository,
	*CategoryRepository,
	*TagRepository,
	*CommentRepository,
	*ComplaintRepository,
	*UserCreationRetryRepository,
	ContentUserRoleRepository,
	*PublishStatsRepository,
) {
	contentRepo := NewContentRepository(db, rdb, cacheManager, userServiceClient)
	categoryRepo := NewCategoryRepository(db)
	tagRepo := NewTagRepository(db)
	commentRepo := NewCommentRepository(db, rdb, cacheManager)
	complaintRepo := NewComplaintRepository(db)
	retryRepo := NewUserCreationRetryRepository(db)
	contentUserRoleRepo := NewContentCollaboratorRepository(db)
	publishStatsRepo := NewPublishStatsRepository(db)

	log.Info().Msg("Repositories initialized successfully")
	return contentRepo, categoryRepo, tagRepo, commentRepo, complaintRepo, retryRepo, contentUserRoleRepo, publishStatsRepo
}
