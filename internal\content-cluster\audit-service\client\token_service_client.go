package client

import (
	"fmt"
	"time"

	"pxpat-backend/pkg/httpclient"

	"github.com/rs/zerolog/log"
)

// TokenServiceClient Token服务客户端接口
type TokenServiceClient interface {
	// RealUpload 真实上传内容到区块链
	RealUpload(req *RealUploadRequest) (*RealUploadResponse, error)
}

// tokenServiceClient Token服务客户端实现
type tokenServiceClient struct {
	httpClient *httpclient.HTTPClient
}

// TokenServiceConfig Token服务客户端配置
type TokenServiceConfig struct {
	BaseURL string
	Timeout time.Duration
}

// RealUploadRequest 真实上传请求
type RealUploadRequest struct {
	ContentKSUID string                 `json:"content_ksuid"`
	CreatorKSUID string                 `json:"creator_ksuid"`
	Title        string                 `json:"title"`
	Description  string                 `json:"description"`
	ContentType  string                 `json:"content_type"`
	FileData     string                 `json:"file_data"`
	FileName     string                 `json:"file_name"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// RealUploadResponse 真实上传响应
type RealUploadResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	OnChainID string `json:"on_chain_id,omitempty"`
	TxHash    string `json:"tx_hash,omitempty"`
}

// NewTokenServiceClient 创建Token服务客户端
func NewTokenServiceClient(config TokenServiceConfig) TokenServiceClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second // 默认超时
	}

	log.Info().
		Str("base_url", config.BaseURL).
		Dur("timeout", config.Timeout).
		Msg("Creating token service client")

	httpClient := httpclient.NewHTTPClient(httpclient.ClientConfig{
		BaseURL:          config.BaseURL,
		Timeout:          config.Timeout,
		RetryCount:       3,
		RetryWaitTime:    1 * time.Second,
		RetryMaxWaitTime: 5 * time.Second,
	})

	return &tokenServiceClient{
		httpClient: httpClient,
	}
}

// RealUpload 真实上传内容到区块链
func (c *tokenServiceClient) RealUpload(req *RealUploadRequest) (*RealUploadResponse, error) {
	log.Info().
		Str("content_ksuid", req.ContentKSUID).
		Str("creator_ksuid", req.CreatorKSUID).
		Str("title", req.Title).
		Str("content_type", req.ContentType).
		Msg("调用Token服务真实上传接口")

	requestURL := "/api/v1/content/real-upload"

	// 直接发送请求，不使用ServiceResponse包装
	err := c.httpClient.Post(requestURL, req, nil)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", req.ContentKSUID).
			Str("url", requestURL).
			Msg("调用Token服务真实上传API失败")
		return nil, fmt.Errorf("failed to call token service real-upload API: %w", err)
	}

	log.Info().
		Str("content_ksuid", req.ContentKSUID).
		Msg("Token服务真实上传请求发送成功")

	// 返回成功响应
	return &RealUploadResponse{
		Success: true,
		Message: "Content upload request sent successfully",
	}, nil
}
