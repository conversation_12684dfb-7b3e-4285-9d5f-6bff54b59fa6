package handler

import (
	"pxpat-backend/internal/storage-cluster/content-storage-service/intra/service"

	"github.com/rs/zerolog/log"
)

// ProvideInternalCoverHandler 提供内部封面处理器
func ProvideInternalCoverHandler(internalCoverService *service.InternalCoverService) *InternalCoverHandler {
	handler := NewInternalCoverHandler(internalCoverService)
	log.Info().Msg("内部封面处理器初始化成功")
	return handler
}

// ProvideInternalVideoHandler 提供内部视频处理器
func ProvideInternalVideoHandler(internalVideoService *service.InternalVideoService) *InternalVideoHandler {
	handler := NewInternalVideoHandler(internalVideoService)
	log.Info().Msg("内部视频处理器初始化成功")
	return handler
}
