#!/bin/bash

# 启动生产环境脚本
set -e

echo "🚀 启动 PXPAT Backend 生产环境..."

# 检查 Docker 和 Docker Compose 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/docker"
PROD_ENV_DIR="$DOCKER_DIR/environments/production"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "🐳 Docker 目录: $DOCKER_DIR"
echo "🏭 生产环境目录: $PROD_ENV_DIR"

# 检查环境文件
if [ ! -f "$PROD_ENV_DIR/.env" ]; then
    echo "❌ 生产环境配置文件不存在！"
    echo "📝 请复制 .env.example 为 .env 并配置生产环境参数"
    echo "cp $PROD_ENV_DIR/.env.example $PROD_ENV_DIR/.env"
    exit 1
fi

# 检查敏感配置
echo "🔒 检查生产环境配置..."
if grep -q "CHANGE_THIS" "$PROD_ENV_DIR/.env"; then
    echo "❌ 检测到未修改的默认配置，请修改所有 CHANGE_THIS 占位符"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p "$DOCKER_DIR/logs"
mkdir -p "$DOCKER_DIR/configs/init-scripts"
mkdir -p "$DOCKER_DIR/configs/ssl"

# 检查网络是否存在，不存在则创建
echo "🌐 检查 Docker 网络..."
networks=("content-network" "database-network" "service-discovery-network" "monitoring-network")
for network in "${networks[@]}"; do
    if ! docker network ls | grep -q "$network"; then
        echo "🌐 创建网络: $network"
        docker network create "$network"
    fi
done

# 进入生产环境目录
cd "$PROD_ENV_DIR"

# 拉取最新镜像
echo "📥 拉取最新镜像..."
docker-compose pull

# 备份现有数据（如果存在）
if docker volume ls | grep -q "postgres_data"; then
    echo "💾 备份现有数据..."
    BACKUP_DIR="$DOCKER_DIR/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份 PostgreSQL 数据
    docker run --rm \
        -v production_postgres_data:/data \
        -v "$BACKUP_DIR":/backup \
        alpine:latest \
        tar czf /backup/postgres_data.tar.gz -C /data .
    
    echo "✅ 数据备份完成: $BACKUP_DIR"
fi

# 启动服务
echo "🚀 启动生产环境服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 运行健康检查
echo "🏥 运行健康检查..."
sleep 10

services=("content-management-service" "postgres" "redis" "consul")
failed_services=()

for service in "${services[@]}"; do
    if docker-compose ps "$service" | grep -q "Up (healthy)"; then
        echo "✅ $service 健康检查通过"
    else
        echo "❌ $service 健康检查失败"
        failed_services+=("$service")
    fi
done

if [ ${#failed_services[@]} -gt 0 ]; then
    echo "⚠️  以下服务健康检查失败: ${failed_services[*]}"
    echo "📝 请检查服务日志:"
    for service in "${failed_services[@]}"; do
        echo "  docker-compose logs $service"
    done
    exit 1
fi

# 显示服务访问地址
echo ""
echo "🎉 生产环境启动完成！"
echo ""
echo "📊 服务访问地址:"
echo "  - 内容管理服务: http://localhost:12010"
echo "  - Consul UI: http://localhost:8500"
echo ""
echo "📝 查看日志:"
echo "  docker-compose logs -f [service_name]"
echo ""
echo "🛑 停止服务:"
echo "  docker-compose down"
echo ""
echo "🔄 重启服务:"
echo "  docker-compose restart [service_name]"
echo ""
echo "💾 备份数据:"
echo "  ./backup.sh"
echo ""

# 设置定时任务提醒
echo "⏰ 建议设置以下定时任务:"
echo "  - 每日数据备份"
echo "  - 日志轮转"
echo "  - 系统监控"
echo ""

echo "🔒 安全提醒:"
echo "  - 定期更新镜像版本"
echo "  - 监控系统资源使用"
echo "  - 检查安全漏洞"
echo "  - 备份重要数据"
