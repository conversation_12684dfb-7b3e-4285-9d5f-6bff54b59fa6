# Docker 配置说明

## 目录结构

```
docker/
├── README.md                           # 本文件，Docker配置说明
├── services/                           # 服务相关配置
│   ├── content-management/             # 内容管理服务
│   │   ├── Dockerfile                  # 内容管理服务Dockerfile
│   │   └── docker-compose.yml          # 内容管理服务compose文件
│   ├── video-service/                  # 视频服务（预留）
│   │   ├── Dockerfile
│   │   └── docker-compose.yml
│   ├── interaction-service/            # 交互服务（预留）
│   │   ├── Dockerfile
│   │   └── docker-compose.yml
│   └── templates/                      # 服务模板
│       ├── Dockerfile.template         # Dockerfile模板
│       └── docker-compose.template.yml # Compose模板
├── infrastructure/                     # 基础设施配置
│   ├── monitoring/                     # 监控相关
│   │   ├── prometheus/
│   │   ├── grafana/
│   │   └── jaeger/
│   ├── databases/                      # 数据库相关
│   │   ├── postgres/
│   │   └── redis/
│   ├── service-discovery/              # 服务发现
│   │   └── consul/
│   └── logging/                        # 日志相关
│       └── elk/
├── environments/                       # 环境配置
│   ├── development/                    # 开发环境
│   │   ├── docker-compose.yml          # 开发环境完整配置
│   │   └── .env.example                # 环境变量示例
│   ├── staging/                        # 测试环境
│   │   ├── docker-compose.yml
│   │   └── .env.example
│   └── production/                     # 生产环境
│       ├── docker-compose.yml
│       └── .env.example
├── scripts/                            # 部署脚本
│   ├── deploy.sh                       # 通用部署脚本
│   ├── start-dev.sh                    # 启动开发环境
│   ├── start-prod.sh                   # 启动生产环境
│   └── cleanup.sh                      # 清理脚本
└── configs/                            # 配置文件
    ├── nginx/                          # Nginx配置
    ├── ssl/                            # SSL证书
    └── init-scripts/                   # 初始化脚本
```

## 使用说明

### 开发环境
```bash
# 启动开发环境
./scripts/start-dev.sh

# 或者手动启动
cd environments/development
docker-compose up -d
```

### 生产环境
```bash
# 启动生产环境
./scripts/start-prod.sh

# 或者手动启动
cd environments/production
docker-compose up -d
```

### 添加新服务

1. 复制服务模板：
```bash
cp -r docker/services/templates docker/services/your-new-service
```

2. 修改Dockerfile和docker-compose.yml
3. 在环境配置中添加新服务
4. 更新CI/CD配置

## 服务端口分配

- 内容管理服务: 12010
- 视频服务: 12001 (预留)
- 交互服务: 12002 (预留)
- PostgreSQL: 5432
- Redis: 6379
- Consul: 8500
- Prometheus: 9090
- Grafana: 3000
- Jaeger: 16686

## 网络配置

- 开发环境网络: **********/16
- 生产环境网络: **********/16
- 测试环境网络: **********/16
