<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="用户服务" type="GoApplicationRunConfiguration" factoryName="Go Application" folderName="用户组">
    <module name="pxpat-backend" />
    <working_directory value="$PROJECT_DIR$" />
    <kind value="PACKAGE" />
    <package value="pxpat-backend/cmd/user-cluster/user-service" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/user-cluster/user-service/main.go" />
    <method v="2" />
  </configuration>
</component>