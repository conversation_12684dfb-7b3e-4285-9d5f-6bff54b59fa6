package providers

import (
	"pxpat-backend/internal/content-cluster/help-service/handler"
	"pxpat-backend/internal/content-cluster/help-service/routes"
	"pxpat-backend/internal/content-cluster/help-service/types"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/middleware/cors"
	metricsMiddleware "pxpat-backend/pkg/middleware/metrics"
	tracingMiddleware "pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// ProvideGinEngine 提供Gin引擎
func ProvideGinEngine(
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	healthHandler *consul.HealthHandler,
	helpHandler *handler.HelpHandler,
) *gin.Engine {
	r := gin.Default()

	// 添加中间件
	r.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 添加指标收集中间件
	if otelProvider.IsMetricsEnabled() {
		r.Use(metricsMiddleware.Middleware(otelProvider.MetricsProvider(), cfg.Otlp.Metrics.ServiceName))
	}

	// 添加链路追踪中间件
	if otelProvider.IsTracingEnabled() {
		r.Use(tracingMiddleware.Middleware(otelProvider.TracingProvider(), cfg.Otlp.Tracing.ServiceName))
	}

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(r)

	// 注册帮助中心路由
	routes.RegisterRoutes(r, helpHandler)

	log.Info().Msg("Gin引擎初始化成功")
	return r
}
