package service

import (
	"fmt"

	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/internal/content-cluster/video-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/video-service/repository"
	"pxpat-backend/internal/content-cluster/video-service/types"
	"pxpat-backend/pkg/storage"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProvideExternalServices 提供External服务层
func ProvideExternalServices(
	contentRepo *repository.ContentRepository,
	categoryRepo *repository.CategoryRepository,
	tagRepo *repository.TagRepository,
	commentRepo *repository.CommentRepository,
	complaintRepo *repository.ComplaintRepository,
	contentUserRoleRepo repository.ContentUserRoleRepository,
	publishStatsRepo *repository.PublishStatsRepository,
	mqPublisher *publisher.Publisher,
	storageClient storage.StorageClient,
	userServiceClient client.UserServiceClient,
	interactionServiceClient client.InteractionServiceClient,
	auditServiceClient client.AuditServiceClient,
	cfg *types.Config,
	db *gorm.DB,
) (
	*ContentService,
	*CategoryService,
	*TagService,
	*CommentService,
	*ComplaintService,
	*CollaborationService,
	*PublishStatsService,
) {
	// 创建存储服务客户端配置
	storageServiceConfig := client.ContentStorageServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.ContentStorageService.Host, cfg.Server.AllServiceList.ContentStorageService.Port),
		Timeout: cfg.Server.AllServiceList.ContentStorageService.Timeout,
	}

	// 创建External服务
	contentService := NewContentService(
		contentRepo,
		categoryRepo,
		contentUserRoleRepo,
		mqPublisher,
		storageClient,
		cfg.Storage.Minio.UrlExpiry,
		storageServiceConfig,
		userServiceClient,
		interactionServiceClient,
	)

	categoryService := NewCategoryService(categoryRepo)
	tagService := NewTagService(tagRepo)
	commentService := NewCommentService(commentRepo, contentRepo, userServiceClient, mqPublisher)
	complaintService := NewComplaintService(complaintRepo, contentRepo, commentRepo)
	collaborationService := NewCollaborationService(contentRepo, contentUserRoleRepo, mqPublisher, auditServiceClient)
	publishStatsService := NewPublishStatsService(publishStatsRepo, userServiceClient)

	log.Info().Msg("External services initialized successfully")
	return contentService, categoryService, tagService, commentService, complaintService, collaborationService, publishStatsService
}
