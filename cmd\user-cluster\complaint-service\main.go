// Package main 投诉服务主程序
// @title 投诉服务API
// @version 1.0
// @description PXPAT平台投诉服务API文档，包含投诉管理、身份认证、权益认证等功能
// @termsOfService https://pxpat.com/terms
// @contact.name API Support
// @contact.url https://pxpat.com/support
// @contact.email <EMAIL>
// @license.name MIT
// @license.url https://opensource.org/licenses/MIT
// @host localhost:11003
// @BasePath /api/v1
// @schemes http https
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.
package main

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
	"gorm.io/gorm"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/user-cluster/complaint-service/docs"
	"pxpat-backend/internal/user-cluster/complaint-service/external/handler"
	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
	intraHandler "pxpat-backend/internal/user-cluster/complaint-service/intra/handler"
	intraService "pxpat-backend/internal/user-cluster/complaint-service/intra/service"
	"pxpat-backend/internal/user-cluster/complaint-service/repository/impl"
	"pxpat-backend/internal/user-cluster/complaint-service/routes"
	"pxpat-backend/internal/user-cluster/complaint-service/routes/admin"
	"pxpat-backend/internal/user-cluster/complaint-service/routes/complaint"
	"pxpat-backend/internal/user-cluster/complaint-service/routes/identity"
	"pxpat-backend/internal/user-cluster/complaint-service/routes/rights"
	"pxpat-backend/internal/user-cluster/complaint-service/types"
	"pxpat-backend/pkg/auth"
	"pxpat-backend/pkg/cache"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	pkgauth "pxpat-backend/pkg/middleware/auth"
	"pxpat-backend/pkg/middleware/cors"
	"pxpat-backend/pkg/middleware/metrics"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "user"
	serviceName := "complaint"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) zerolog.Logger {
	serviceName := "complaint"

	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	log.Info().Msg("Complaint service starting...")
	return log.Logger
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	// 自动创建数据库
	// migrations.AutoMigrate(db) // 迁移文件只包含SQL，不需要Go代码
	log.Info().Msg("Database migration completed")

	return db, nil
}

// 提供Redis连接
func provideRedis(cfg *types.Config) (*redis.Client, error) {
	rdb, err := DBLoader.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to Redis")
		return nil, err
	}
	log.Info().Msg("Redis connected successfully")
	return rdb, nil
}

// 提供缓存管理器
func provideCacheManager(rdb *redis.Client) (cache.Manager, error) {
	cacheManager, err := cache.NewManager(rdb)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cache manager")
		return nil, err
	}
	log.Info().Msg("Cache manager initialized")
	return cacheManager, nil
}

// 提供JWT管理器
func provideJWTManager(cfg *types.Config) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")
	return &jwtManager
}

// 提供OpenTelemetry提供者
func provideOTLPProvider(cfg *types.Config) (*opentelemetry.Provider, error) {
	if !cfg.OTLP.Tracing.Enabled && !cfg.OTLP.Metrics.Enabled {
		log.Info().Msg("OpenTelemetry disabled")
		return nil, nil
	}

	provider, err := opentelemetry.NewProvider(cfg.OTLP)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize OpenTelemetry provider")
		return nil, err
	}

	log.Info().
		Bool("tracing_enabled", provider.IsTracingEnabled()).
		Bool("metrics_enabled", provider.IsMetricsEnabled()).
		Msg("OpenTelemetry provider initialized")

	return provider, nil
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 提供用户服务客户端
func provideUserServiceClient(cfg *types.Config) service.UserServiceClient {
	return &UserServiceClient{
		baseURL: fmt.Sprintf("http://%s:%d",
			cfg.Server.AllServiceList.UserService.Host,
			cfg.Server.AllServiceList.UserService.Port),
		timeout: cfg.Server.AllServiceList.UserService.Timeout,
		token:   cfg.Server.AllServiceList.UserService.Token,
	}
}

// UserServiceClient 用户服务客户端实现
type UserServiceClient struct {
	baseURL string
	timeout time.Duration
	token   string
}

func (c *UserServiceClient) BlockUser(ctx context.Context, blockerKSUID, blockedKSUID string) error {
	// TODO: 实现调用用户服务的拉黑接口
	log.Info().
		Str("blocker_ksuid", blockerKSUID).
		Str("blocked_ksuid", blockedKSUID).
		Str("user_service_url", c.baseURL).
		Msg("调用用户服务拉黑用户")
	return nil
}

func (c *UserServiceClient) GetUserInfo(ctx context.Context, userKSUID string) (*service.UserInfo, error) {
	// TODO: 实现调用用户服务获取用户信息接口
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("user_service_url", c.baseURL).
		Msg("调用用户服务获取用户信息")

	return &service.UserInfo{
		UserKSUID: userKSUID,
		Nickname:  "测试用户",
		Email:     "<EMAIL>",
	}, nil
}

// 提供Gin引擎
func provideGinEngine(
	cfg *types.Config,
	jwtManager *auth.Manager,
	complaintHandler *handler.ComplaintHandler,
	identityHandler *handler.IdentityHandler,
	rightsHandler *handler.RightsHandler,
	adminHandler *handler.AdminHandler,
	internalComplaintHandler *intraHandler.InternalComplaintHandler,
	healthHandler *consul.HealthHandler,
	otlpProvider *opentelemetry.Provider,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建服务
	router := gin.Default()

	// 添加CORS中间件
	router.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 添加链路追踪中间件
	if otlpProvider != nil && otlpProvider.IsTracingEnabled() {
		router.Use(tracing.Middleware(otlpProvider.TracingProvider(), "complaint-service"))
		log.Info().Msg("Tracing middleware enabled")
	}

	// 添加指标中间件
	if otlpProvider != nil && otlpProvider.IsMetricsEnabled() {
		router.Use(metrics.Middleware(otlpProvider.MetricsProvider(), "complaint-service"))
		log.Info().Msg("Metrics middleware enabled")
	}

	log.Info().Msg("Middleware configured")

	// API路由组
	apiV1 := router.Group("/api/v1")

	// 创建认证中间件
	authMiddleware := pkgauth.UserAuthMiddleware(*jwtManager)

	// 注册路由
	complaint.RegisterComplaintRoutes(apiV1, complaintHandler, authMiddleware)
	identity.RegisterIdentityRoutes(apiV1, identityHandler, authMiddleware)
	rights.RegisterRightsRoutes(apiV1, rightsHandler, authMiddleware)

	// 注册管理员路由
	admin.RegisterAdminRoutes(apiV1, adminHandler, jwtManager)

	// 注册内部路由
	routes.RegisterInternalRoutes(router, internalComplaintHandler)

	// 注册Swagger文档
	docs.SetupSwagger(router)

	log.Info().Msg("Routes registered")

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	return router
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
	otlpProvider *opentelemetry.Provider,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// OpenTelemetry提供者在创建时已经启动，无需额外启动
			if otlpProvider != nil {
				log.Info().Msg("OpenTelemetry提供者已启动")
			}

			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Int("port", cfg.Server.Port).Msg("Starting complaint service server")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭OpenTelemetry提供者
			if otlpProvider != nil {
				if err := otlpProvider.Shutdown(ctx); err != nil {
					log.Error().Err(err).Msg("关闭OpenTelemetry提供者失败")
				} else {
					log.Info().Msg("OpenTelemetry提供者关闭成功")
				}
			}

			return nil
		},
	})
}

func main() {
	fmt.Println("Starting complaint service...")
	log.Info().Msg("Complaint service main function started")

	fmt.Println("Creating fx app...")
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideRedis,
			provideCacheManager,
			provideJWTManager,
			provideOTLPProvider,
			// Repository层
			impl.NewComplaintRepository,
			impl.NewComplaintEvidenceRepository,
			impl.NewViolationCategoryRepository,
			impl.NewIdentityVerificationRepository,
			impl.NewCountryRepository,
			impl.NewTrademarkCategoryRepository,
			impl.NewRightsVerificationRepository,
			impl.NewCopyrightRepository,
			impl.NewTrademarkRepository,
			impl.NewPersonalityRightRepository,
			impl.NewRightsEvidenceRepository,
			// 客户端
			provideUserServiceClient,
			// Service层
			service.NewComplaintService,
			service.NewIdentityService,
			service.NewRightsService,
			// 内部Service层
			intraService.NewInternalComplaintService,
			// Handler层
			handler.NewComplaintHandler,
			handler.NewIdentityHandler,
			handler.NewRightsHandler,
			handler.NewAdminHandler,
			// 内部Handler层
			intraHandler.NewInternalComplaintHandler,
			// 基础设施
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	fmt.Println("Running fx app...")
	// 运行应用
	app.Run()
	fmt.Println("Fx app finished")
}
