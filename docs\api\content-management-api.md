# 内容管理服务 API 文档

## 概述

内容管理服务提供统一的内容管理接口，支持多种内容类型（视频、小说、音乐等）的管理、统计分析和批量操作。

- **服务名称**: content-management-service
- **版本**: v1.0.0
- **基础URL**: `http://localhost:12010/api/v1`
- **认证方式**: JWT Bearer Token

## 认证

所有需要认证的接口都需要在请求头中包含JWT Token：

```http
Authorization: Bearer <your-jwt-token>
```

## 响应格式

所有API响应都遵循统一的格式：

```json
{
  "code": 200,
  "msg": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req-123456"
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率过高 |
| 500 | 服务器内部错误 |

## 内容管理接口

### 1. 获取内容列表

获取基础内容列表，支持分页和筛选。

**接口地址**: `GET /management/contents`

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认20，最大100 |
| content_types | string | 否 | 内容类型，多个用逗号分隔 |
| status | string | 否 | 内容状态 |
| user_ksuid | string | 否 | 用户KSUID |
| category_id | int | 否 | 分类ID |
| sort_by | string | 否 | 排序字段，默认created_at |
| sort_order | string | 否 | 排序方向，asc/desc，默认desc |

**响应示例**:

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "contents": [
      {
        "ksuid": "content_123",
        "content_type": "video",
        "title": "示例视频",
        "description": "这是一个示例视频",
        "user_ksuid": "user_123",
        "status": "published",
        "category_id": 1,
        "tags": ["示例", "视频"],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

### 2. 获取内容详情

获取指定内容的详细信息，包括交互统计。

**接口地址**: `GET /management/contents/{content_ksuid}`

**认证**: 需要

**路径参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| content_ksuid | string | 是 | 内容KSUID |

**响应示例**:

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "ksuid": "video_123",
    "title": "示例视频",
    "description": "这是一个示例视频",
    "user_ksuid": "user_123",
    "status": "published",
    "category_id": 1,
    "tags": ["示例", "视频"],
    "duration": 300,
    "resolution": "1920x1080",
    "file_size": 1024000,
    "interaction_stats": {
      "views": 1000,
      "likes": 50,
      "favorites": 20,
      "comments": 10,
      "shares": 5
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 更新内容状态

更新指定内容的状态。

**接口地址**: `PUT /management/contents/{content_ksuid}/status`

**认证**: 需要

**请求体**:

```json
{
  "status": "archived"
}
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "内容状态更新成功"
}
```

### 4. 删除内容

删除指定内容（软删除）。

**接口地址**: `DELETE /management/contents/{content_ksuid}`

**认证**: 需要

**响应示例**:

```json
{
  "code": 200,
  "msg": "内容删除成功"
}
```

### 5. 批量更新状态

批量更新多个内容的状态。

**接口地址**: `PUT /management/contents/batch/status`

**认证**: 需要（管理员权限）

**请求体**:

```json
{
  "content_ksuids": ["content_1", "content_2", "content_3"],
  "status": "archived"
}
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "批量状态更新成功",
  "data": {
    "success_count": 3,
    "failed_count": 0
  }
}
```

## 统计分析接口

### 1. 获取总体统计

获取系统总体统计信息。

**接口地址**: `GET /management/stats/overview`

**认证**: 需要（管理员权限）

**响应示例**:

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "total_contents": 10000,
    "total_users": 1000,
    "total_views": 100000,
    "total_likes": 5000,
    "content_type_stats": {
      "video": 6000,
      "novel": 3000,
      "music": 1000
    },
    "status_stats": {
      "published": 8000,
      "draft": 1500,
      "archived": 500
    }
  }
}
```

### 2. 获取内容趋势

获取内容发布趋势数据。

**接口地址**: `GET /management/stats/trends`

**认证**: 需要（管理员权限）

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| start_time | string | 否 | 开始时间，ISO格式 |
| end_time | string | 否 | 结束时间，ISO格式 |
| group_by | string | 否 | 分组方式：day/week/month |

**响应示例**:

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "trends": [
      {
        "date": "2024-01-01",
        "total_contents": 100,
        "published_contents": 80,
        "total_views": 1000,
        "total_likes": 50
      }
    ]
  }
}
```

## 批量操作接口

### 1. 内容搜索

搜索内容，支持全文搜索和多条件筛选。

**接口地址**: `GET /management/batch/search`

**认证**: 需要

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | string | 否 | 搜索关键词 |
| content_types | string | 否 | 内容类型筛选 |
| status | string | 否 | 状态筛选 |
| category_id | int | 否 | 分类筛选 |
| user_ksuid | string | 否 | 用户筛选 |
| page | int | 否 | 页码 |
| limit | int | 否 | 每页数量 |

**响应示例**:

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "results": [
      {
        "ksuid": "content_123",
        "title": "匹配的内容标题",
        "content_type": "video",
        "status": "published",
        "relevance_score": 0.95
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50
    }
  }
}
```

## 健康检查接口

### 健康检查

检查服务健康状态。

**接口地址**: `GET /health`

**认证**: 不需要

**响应示例**:

```json
{
  "code": 200,
  "msg": "服务健康",
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00Z",
    "version": "1.0.0",
    "uptime": "24h30m15s"
  }
}
```

## 限流说明

API接口实现了多层次限流保护：

- **默认限流**: 每分钟100次请求
- **批量操作限流**: 每分钟10次请求
- **IP限流**: 每个IP每分钟200次请求
- **用户限流**: 每个用户每分钟150次请求

超出限流时返回429状态码：

```json
{
  "code": 429,
  "msg": "请求频率过高，请稍后重试",
  "data": {
    "retry_after": 60
  }
}
```

## SDK和示例

### cURL示例

```bash
# 获取内容列表
curl -X GET "http://localhost:12010/api/v1/management/contents?page=1&limit=10" \
  -H "Authorization: Bearer your-jwt-token"

# 更新内容状态
curl -X PUT "http://localhost:12010/api/v1/management/contents/content_123/status" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"status": "archived"}'
```

### JavaScript示例

```javascript
// 获取内容列表
const response = await fetch('/api/v1/management/contents', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const data = await response.json();
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基础内容管理功能
- 支持统计分析功能
- 支持批量操作功能
