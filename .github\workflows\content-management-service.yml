name: Content Management Service CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'internal/content-cluster/content-management-service/**'
      - 'cmd/content-cluster/content-management-service/**'
      - 'docker/services/content-management/**'
      - '.github/workflows/content-management-service.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'internal/content-cluster/content-management-service/**'
      - 'cmd/content-cluster/content-management-service/**'
      - 'docker/services/content-management/**'

env:
  GO_VERSION: '1.21'
  SERVICE_NAME: content-management-service
  CLUSTER_NAME: content-cluster
  REGISTRY: ghcr.io
  IMAGE_NAME: pxpat-backend/content-management-service

jobs:
  # 代码质量检查
  lint-and-test:
    name: Lint and Test
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: content_management_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        cache-dependency-path: go.sum
    
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: Download dependencies
      run: go mod download
    
    - name: Verify dependencies
      run: go mod verify
    
    - name: Run go vet
      run: go vet ./...
    
    - name: Install golangci-lint
      run: |
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2
        echo "$(go env GOPATH)/bin" >> $GITHUB_PATH
    
    - name: Run golangci-lint
      run: golangci-lint run --timeout=5m
    
    - name: Run unit tests
      working-directory: internal/content-cluster/content-management-service
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_USER: test_user
        DB_PASSWORD: test_password
        DB_NAME: content_management_test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        GO_ENV: test
      run: |
        chmod +x run_tests.sh
        ./run_tests.sh
    
    - name: Run integration tests
      working-directory: internal/content-cluster/content-management-service
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_USER: test_user
        DB_PASSWORD: test_password
        DB_NAME: content_management_test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        GO_ENV: integration_test
      run: |
        chmod +x run_integration_tests.sh
        ./run_integration_tests.sh
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./internal/content-cluster/content-management-service/coverage.out
        flags: content-management-service
        name: content-management-service-coverage
    
    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Content Management Service Tests
        path: 'internal/content-cluster/content-management-service/test_results/*.xml'
        reporter: java-junit

  # 安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: lint-and-test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: '-fmt sarif -out gosec-results.sarif ./...'
    
    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: gosec-results.sarif
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 构建Docker镜像
  build-image:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-scan]
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: docker/services/content-management/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64
        build-args: |
          SERVICE_NAME=${{ env.SERVICE_NAME }}
          CLUSTER_NAME=${{ env.CLUSTER_NAME }}

  # 部署到开发环境
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: build-image
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to development
      run: |
        echo "Deploying to development environment..."
        # 这里添加实际的部署逻辑
        # 例如：kubectl apply, docker-compose up, 或调用部署API
    
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # 添加冒烟测试逻辑

  # 部署到生产环境
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-image
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # 这里添加实际的部署逻辑
    
    - name: Run smoke tests
      run: |
        echo "Running production smoke tests..."
        # 添加生产环境冒烟测试逻辑
    
    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow

  # 性能测试
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: build-image
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Run performance tests
      working-directory: internal/content-cluster/content-management-service
      run: |
        go test -bench=. -benchmem ./performance/...
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: internal/content-cluster/content-management-service/performance-results.txt
