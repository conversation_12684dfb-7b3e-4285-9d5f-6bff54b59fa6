http.host: "0.0.0.0"
xpack.monitoring.elasticsearch.hosts: [ "http://elasticsearch:9200" ]

# 管道配置
path.config: /usr/share/logstash/pipeline
config.reload.automatic: true
config.reload.interval: 3s

# 日志配置
log.level: info
path.logs: /var/log/logstash

# 队列配置
queue.type: persisted
path.queue: /usr/share/logstash/data/queue

# 死信队列配置
dead_letter_queue.enable: true
path.dead_letter_queue: /usr/share/logstash/data/dead_letter_queue 