# {{SERVICE_NAME}} Docker Compose 配置
version: '3.8'

services:
  # {{SERVICE_DESCRIPTION}}
  {{SERVICE_NAME}}:
    build:
      context: ../../..
      dockerfile: docker/services/{{SERVICE_NAME}}/Dockerfile
    container_name: {{SERVICE_NAME}}
    restart: unless-stopped
    ports:
      - "${{{SERVICE_NAME_UPPER}}_PORT:-{{SERVICE_PORT}}}:{{SERVICE_PORT}}"
    environment:
      # 服务配置
      - GO_ENV=${GO_ENV:-production}
      - GIN_MODE=${GIN_MODE:-release}
      
      # 数据库配置
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USER=${DB_USER:-{{SERVICE_NAME}}_user}
      - DB_PASSWORD=${DB_PASSWORD:-{{SERVICE_NAME}}_password}
      - DB_NAME=${DB_NAME:-{{SERVICE_NAME}}}
      - DB_SSL_MODE=${DB_SSL_MODE:-disable}
      
      # Redis配置
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      - REDIS_DB=${REDIS_DB:-0}
      
      # JWT配置
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-super-secret-jwt-key-change-in-production}
      - JWT_TOKEN_DURATION=${JWT_TOKEN_DURATION:-1h}
      - JWT_REFRESH_DURATION=${JWT_REFRESH_DURATION:-24h}
      
      # 外部服务配置（根据需要添加）
      # - OTHER_SERVICE_URL=${OTHER_SERVICE_URL:-http://other-service:port}
      
      # Consul配置
      - CONSUL_HOST=${CONSUL_HOST:-consul}
      - CONSUL_PORT=${CONSUL_PORT:-8500}
      - CONSUL_DATACENTER=${CONSUL_DATACENTER:-dc1}
      
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FORMAT=${LOG_FORMAT:-json}
    volumes:
      - ../../logs:/app/logs
      - ../../configs:/app/config:ro
    networks:
      - {{NETWORK_NAME:-app-network}}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{{SERVICE_PORT}}/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# 网络配置
networks:
  {{NETWORK_NAME:-app-network}}:
    driver: bridge
    external: true
