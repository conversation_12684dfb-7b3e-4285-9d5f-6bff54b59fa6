{"openapi": "3.0.3", "info": {"title": "投诉服务API", "description": "PXPAT平台投诉服务API文档，包含投诉管理、身份认证、权益认证等功能", "version": "1.0.0", "contact": {"name": "API Support", "url": "https://pxpat.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "termsOfService": "https://pxpat.com/terms"}, "servers": [{"url": "http://localhost:11003/api/v1", "description": "开发环境"}, {"url": "https://api.pxpat.com/api/v1", "description": "生产环境"}], "security": [{"BearerAuth": []}], "paths": {"/complaints": {"post": {"tags": ["投诉管理"], "summary": "创建投诉", "description": "用户创建新的投诉", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateComplaintRequest"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "get": {"tags": ["投诉管理"], "summary": "获取投诉列表", "description": "获取投诉列表（管理员）", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}, {"name": "status", "in": "query", "description": "投诉状态", "schema": {"type": "string", "enum": ["pending", "processing", "resolved", "rejected"]}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintListResponse"}}}}}}}, "/complaints/{complaint_ksuid}": {"get": {"tags": ["投诉管理"], "summary": "获取投诉详情", "description": "根据KSUID获取投诉详情", "security": [{"BearerAuth": []}], "parameters": [{"name": "complaint_ksuid", "in": "path", "required": true, "description": "投诉KSUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintResponse"}}}}, "404": {"description": "投诉不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["投诉管理"], "summary": "更新投诉", "description": "更新投诉信息", "security": [{"BearerAuth": []}], "parameters": [{"name": "complaint_ksuid", "in": "path", "required": true, "description": "投诉KSUID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateComplaintRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintResponse"}}}}}}, "delete": {"tags": ["投诉管理"], "summary": "删除投诉", "description": "删除投诉", "security": [{"BearerAuth": []}], "parameters": [{"name": "complaint_ksuid", "in": "path", "required": true, "description": "投诉KSUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "删除成功"}}}}, "/complaints/my": {"get": {"tags": ["投诉管理"], "summary": "获取我的投诉列表", "description": "获取当前用户的投诉列表", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintListResponse"}}}}}}}, "/complaints/against-me": {"get": {"tags": ["投诉管理"], "summary": "获取针对我的投诉列表", "description": "获取针对当前用户的投诉列表", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintListResponse"}}}}}}}, "/complaints/stats": {"get": {"tags": ["投诉管理"], "summary": "获取投诉统计", "description": "获取投诉统计信息", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintStatsResponse"}}}}}}}, "/complaints/violation-categories": {"get": {"tags": ["投诉管理"], "summary": "获取违规类别", "description": "获取所有违规类别列表", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViolationCategoryResponse"}}}}}}}}, "/identity/verification": {"post": {"tags": ["身份认证"], "summary": "创建身份认证", "description": "用户创建身份认证申请", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIdentityVerificationRequest"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerificationResponse"}}}}}}}, "/identity/verification/{verification_ksuid}": {"get": {"tags": ["身份认证"], "summary": "获取身份认证详情", "description": "根据KSUID获取身份认证详情", "security": [{"BearerAuth": []}], "parameters": [{"name": "verification_ksuid", "in": "path", "required": true, "description": "身份认证KSUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerificationResponse"}}}}}}, "put": {"tags": ["身份认证"], "summary": "更新身份认证", "description": "更新身份认证信息", "security": [{"BearerAuth": []}], "parameters": [{"name": "verification_ksuid", "in": "path", "required": true, "description": "身份认证KSUID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIdentityVerificationRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerificationResponse"}}}}}}}, "/identity/verification/my": {"get": {"tags": ["身份认证"], "summary": "获取我的身份认证列表", "description": "获取当前用户的身份认证列表", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerificationListResponse"}}}}}}}, "/identity/countries": {"get": {"tags": ["身份认证"], "summary": "获取国家地区列表", "description": "获取支持的国家地区列表", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CountryResponse"}}}}}}}}, "/identity/trademark-categories": {"get": {"tags": ["身份认证"], "summary": "获取商标类别列表", "description": "获取商标类别列表", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TrademarkCategoryResponse"}}}}}}}}, "/rights/verification": {"post": {"tags": ["权益认证"], "summary": "创建权益认证", "description": "用户创建权益认证申请", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRightsVerificationRequest"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RightsVerificationResponse"}}}}}}}, "/rights/verification/{rights_ksuid}": {"get": {"tags": ["权益认证"], "summary": "获取权益认证详情", "description": "根据KSUID获取权益认证详情", "security": [{"BearerAuth": []}], "parameters": [{"name": "rights_ksuid", "in": "path", "required": true, "description": "权益认证KSUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RightsVerificationResponse"}}}}}}}, "/rights/verification/my": {"get": {"tags": ["权益认证"], "summary": "获取我的权益认证列表", "description": "获取当前用户的权益认证列表", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RightsVerificationListResponse"}}}}}}}, "/admin/complaints": {"get": {"tags": ["管理员接口-投诉"], "summary": "获取投诉列表（管理员）", "description": "管理员获取投诉列表", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}, {"name": "status", "in": "query", "description": "投诉状态", "schema": {"type": "string", "enum": ["pending", "processing", "resolved", "rejected"]}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintListResponse"}}}}}}}, "/admin/complaints/{complaint_ksuid}/process": {"post": {"tags": ["管理员接口-投诉"], "summary": "处理投诉", "description": "管理员处理投诉，可以通过、拒绝或要求补充材料", "security": [{"BearerAuth": []}], "parameters": [{"name": "complaint_ksuid", "in": "path", "required": true, "description": "投诉KSUID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessComplaintRequest"}}}}, "responses": {"200": {"description": "处理成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplaintResponse"}}}}}}}, "/admin/identity": {"get": {"tags": ["管理员接口-身份认证"], "summary": "获取身份认证列表（管理员）", "description": "管理员获取身份认证列表", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerificationListResponse"}}}}}}}, "/admin/identity/{verification_ksuid}/review": {"post": {"tags": ["管理员接口-身份认证"], "summary": "审核身份认证", "description": "管理员审核身份认证申请", "security": [{"BearerAuth": []}], "parameters": [{"name": "verification_ksuid", "in": "path", "required": true, "description": "身份认证KSUID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewIdentityRequest"}}}}, "responses": {"200": {"description": "审核成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerificationResponse"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\""}}, "schemas": {"ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}, "code": {"type": "integer", "description": "错误代码"}, "message": {"type": "string", "description": "详细错误信息"}}}, "CreateComplaintRequest": {"type": "object", "required": ["accused_user_ksuid", "violation_category_id", "description"], "properties": {"accused_user_ksuid": {"type": "string", "description": "被投诉用户KSUID"}, "violation_category_id": {"type": "integer", "description": "违规类别ID"}, "description": {"type": "string", "description": "投诉描述"}, "evidence_urls": {"type": "array", "items": {"type": "string"}, "description": "证据文件URL列表"}}}, "UpdateComplaintRequest": {"type": "object", "properties": {"description": {"type": "string", "description": "投诉描述"}, "evidence_urls": {"type": "array", "items": {"type": "string"}, "description": "证据文件URL列表"}}}, "ProcessComplaintRequest": {"type": "object", "required": ["action"], "properties": {"action": {"type": "string", "enum": ["approve", "reject", "request_more_info"], "description": "处理动作"}, "admin_note": {"type": "string", "description": "管理员备注"}, "reject_reason": {"type": "string", "description": "拒绝原因"}}}, "ComplaintResponse": {"type": "object", "properties": {"complaint_ksuid": {"type": "string", "description": "投诉KSUID"}, "complainant_ksuid": {"type": "string", "description": "投诉人KSUID"}, "accused_user_ksuid": {"type": "string", "description": "被投诉用户KSUID"}, "violation_category": {"$ref": "#/components/schemas/ViolationCategoryResponse"}, "description": {"type": "string", "description": "投诉描述"}, "status": {"type": "string", "enum": ["pending", "processing", "resolved", "rejected"], "description": "投诉状态"}, "evidence": {"type": "array", "items": {"$ref": "#/components/schemas/ComplaintEvidenceResponse"}, "description": "证据列表"}, "admin_note": {"type": "string", "description": "管理员备注"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "ComplaintListResponse": {"type": "object", "properties": {"complaints": {"type": "array", "items": {"$ref": "#/components/schemas/ComplaintResponse"}, "description": "投诉列表"}, "total": {"type": "integer", "description": "总数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}}}, "ComplaintStatsResponse": {"type": "object", "properties": {"total_complaints": {"type": "integer", "description": "总投诉数"}, "pending_complaints": {"type": "integer", "description": "待处理投诉数"}, "resolved_complaints": {"type": "integer", "description": "已解决投诉数"}, "rejected_complaints": {"type": "integer", "description": "已拒绝投诉数"}}}, "ComplaintEvidenceResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "证据ID"}, "complaint_ksuid": {"type": "string", "description": "投诉KSUID"}, "url": {"type": "string", "description": "证据文件URL"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}}}, "ViolationCategoryResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "违规类别ID"}, "name": {"type": "string", "description": "违规类别名称"}, "description": {"type": "string", "description": "违规类别描述"}}}, "CreateIdentityVerificationRequest": {"type": "object", "required": ["type", "real_name", "id_number", "id_card_front", "id_card_back"], "properties": {"type": {"type": "string", "enum": ["personal", "enterprise"], "description": "认证类型"}, "real_name": {"type": "string", "description": "真实姓名/企业名称"}, "id_number": {"type": "string", "description": "身份证号/营业执照号"}, "id_card_front": {"type": "string", "description": "身份证正面/营业执照照片URL"}, "id_card_back": {"type": "string", "description": "身份证背面URL"}, "country_id": {"type": "integer", "description": "国家/地区ID"}}}, "UpdateIdentityVerificationRequest": {"type": "object", "properties": {"real_name": {"type": "string", "description": "真实姓名/企业名称"}, "id_number": {"type": "string", "description": "身份证号/营业执照号"}, "id_card_front": {"type": "string", "description": "身份证正面/营业执照照片URL"}, "id_card_back": {"type": "string", "description": "身份证背面URL"}, "country_id": {"type": "integer", "description": "国家/地区ID"}}}, "ReviewIdentityRequest": {"type": "object", "required": ["action"], "properties": {"action": {"type": "string", "enum": ["approve", "reject", "request_more_info"], "description": "审核动作"}, "admin_note": {"type": "string", "description": "管理员备注"}, "reject_reason": {"type": "string", "description": "拒绝原因"}}}, "IdentityVerificationResponse": {"type": "object", "properties": {"verification_ksuid": {"type": "string", "description": "身份认证KSUID"}, "user_ksuid": {"type": "string", "description": "用户KSUID"}, "type": {"type": "string", "enum": ["personal", "enterprise"], "description": "认证类型"}, "real_name": {"type": "string", "description": "真实姓名/企业名称"}, "id_number": {"type": "string", "description": "身份证号/营业执照号"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected"], "description": "认证状态"}, "country": {"$ref": "#/components/schemas/CountryResponse"}, "admin_note": {"type": "string", "description": "管理员备注"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "IdentityVerificationListResponse": {"type": "object", "properties": {"verifications": {"type": "array", "items": {"$ref": "#/components/schemas/IdentityVerificationResponse"}, "description": "身份认证列表"}, "total": {"type": "integer", "description": "总数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}}}, "CountryResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "国家ID"}, "name": {"type": "string", "description": "国家名称"}, "code": {"type": "string", "description": "国家代码"}}}, "TrademarkCategoryResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "商标类别ID"}, "name": {"type": "string", "description": "商标类别名称"}, "description": {"type": "string", "description": "商标类别描述"}}}, "CreateRightsVerificationRequest": {"type": "object", "required": ["type", "title", "description"], "properties": {"type": {"type": "string", "enum": ["copyright", "trademark", "personality_right"], "description": "权益类型"}, "title": {"type": "string", "description": "权益标题"}, "description": {"type": "string", "description": "权益描述"}, "evidence_urls": {"type": "array", "items": {"type": "string"}, "description": "证据文件URL列表"}}}, "ReviewRightsRequest": {"type": "object", "required": ["action"], "properties": {"action": {"type": "string", "enum": ["approve", "reject", "request_more_info"], "description": "审核动作"}, "admin_note": {"type": "string", "description": "管理员备注"}, "reject_reason": {"type": "string", "description": "拒绝原因"}}}, "RightsVerificationResponse": {"type": "object", "properties": {"rights_ksuid": {"type": "string", "description": "权益认证KSUID"}, "user_ksuid": {"type": "string", "description": "用户KSUID"}, "type": {"type": "string", "enum": ["copyright", "trademark", "personality_right"], "description": "权益类型"}, "title": {"type": "string", "description": "权益标题"}, "description": {"type": "string", "description": "权益描述"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected"], "description": "认证状态"}, "admin_note": {"type": "string", "description": "管理员备注"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "RightsVerificationListResponse": {"type": "object", "properties": {"verifications": {"type": "array", "items": {"$ref": "#/components/schemas/RightsVerificationResponse"}, "description": "权益认证列表"}, "total": {"type": "integer", "description": "总数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}}}}}}