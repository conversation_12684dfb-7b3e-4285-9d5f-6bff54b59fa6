package integration_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"pxpat-backend/pkg/errors"
	globalTypes "pxpat-backend/pkg/types"
)

func TestContentAPI_GetContents(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	tests := []struct {
		name           string
		url            string
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "获取内容列表-无参数",
			url:            "/api/v1/management/contents",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取内容列表-带分页参数",
			url:            "/api/v1/management/contents?page=1&limit=10",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取内容列表-带筛选参数",
			url:            "/api/v1/management/contents?status=published&content_types=video",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取内容列表-无效参数",
			url:            "/api/v1/management/contents?page=0&limit=1001",
			expectedStatus: http.StatusBadRequest,
			expectedCode:   errors.INVALID_PARAMETER,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := http.Get(server.GetBaseURL() + tt.url)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)

			if tt.expectedStatus == http.StatusOK {
				assert.NotNil(t, response.Data)
			}
		})
	}
}

func TestContentAPI_GetContentWithDetails(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "user")
	require.NoError(t, err)

	tests := []struct {
		name           string
		contentKSUID   string
		useAuth        bool
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "获取内容详情-有效KSUID",
			contentKSUID:   "video_1",
			useAuth:        true,
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取内容详情-无认证",
			contentKSUID:   "video_1",
			useAuth:        false,
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errors.UNAUTHORIZED,
		},
		{
			name:           "获取内容详情-无效KSUID",
			contentKSUID:   "invalid_ksuid",
			useAuth:        true,
			expectedStatus: http.StatusNotFound,
			expectedCode:   errors.RESOURCE_NOT_FOUND,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := fmt.Sprintf("%s/api/v1/management/contents/%s", server.GetBaseURL(), tt.contentKSUID)

			req, err := http.NewRequest("GET", url, nil)
			require.NoError(t, err)

			if tt.useAuth {
				req.Header.Set("Authorization", "Bearer "+token)
			}

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
		})
	}
}

func TestContentAPI_UpdateContentStatus(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "user")
	require.NoError(t, err)

	tests := []struct {
		name           string
		contentKSUID   string
		requestBody    map[string]interface{}
		useAuth        bool
		expectedStatus int
		expectedCode   int
	}{
		{
			name:         "更新内容状态-有效请求",
			contentKSUID: "video_1",
			requestBody: map[string]interface{}{
				"status": "archived",
			},
			useAuth:        true,
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:         "更新内容状态-无认证",
			contentKSUID: "video_1",
			requestBody: map[string]interface{}{
				"status": "archived",
			},
			useAuth:        false,
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errors.UNAUTHORIZED,
		},
		{
			name:         "更新内容状态-无效状态",
			contentKSUID: "video_1",
			requestBody: map[string]interface{}{
				"status": "invalid_status",
			},
			useAuth:        true,
			expectedStatus: http.StatusBadRequest,
			expectedCode:   errors.INVALID_PARAMETER,
		},
		{
			name:           "更新内容状态-缺少请求体",
			contentKSUID:   "video_1",
			requestBody:    map[string]interface{}{},
			useAuth:        true,
			expectedStatus: http.StatusBadRequest,
			expectedCode:   errors.INVALID_PARAMETER,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBody, _ := json.Marshal(tt.requestBody)
			url := fmt.Sprintf("%s/api/v1/management/contents/%s/status", server.GetBaseURL(), tt.contentKSUID)

			req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonBody))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			if tt.useAuth {
				req.Header.Set("Authorization", "Bearer "+token)
			}

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
		})
	}
}

func TestContentAPI_DeleteContent(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "user")
	require.NoError(t, err)

	tests := []struct {
		name           string
		contentKSUID   string
		useAuth        bool
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "删除内容-有效请求",
			contentKSUID:   "video_1",
			useAuth:        true,
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "删除内容-无认证",
			contentKSUID:   "video_1",
			useAuth:        false,
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errors.UNAUTHORIZED,
		},
		{
			name:           "删除内容-无效KSUID",
			contentKSUID:   "invalid_ksuid",
			useAuth:        true,
			expectedStatus: http.StatusNotFound,
			expectedCode:   errors.RESOURCE_NOT_FOUND,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := fmt.Sprintf("%s/api/v1/management/contents/%s", server.GetBaseURL(), tt.contentKSUID)

			req, err := http.NewRequest("DELETE", url, nil)
			require.NoError(t, err)

			if tt.useAuth {
				req.Header.Set("Authorization", "Bearer "+token)
			}

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
		})
	}
}

func TestContentAPI_BatchOperations(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "admin")
	require.NoError(t, err)

	tests := []struct {
		name           string
		endpoint       string
		requestBody    map[string]interface{}
		useAuth        bool
		expectedStatus int
		expectedCode   int
	}{
		{
			name:     "批量更新状态-有效请求",
			endpoint: "/api/v1/management/contents/batch/status",
			requestBody: map[string]interface{}{
				"content_ksuids": []string{"video_1", "video_2"},
				"status":         "archived",
			},
			useAuth:        true,
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:     "批量删除-有效请求",
			endpoint: "/api/v1/management/contents/batch/delete",
			requestBody: map[string]interface{}{
				"content_ksuids": []string{"video_1", "video_2"},
			},
			useAuth:        true,
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:     "批量操作-无认证",
			endpoint: "/api/v1/management/contents/batch/status",
			requestBody: map[string]interface{}{
				"content_ksuids": []string{"video_1"},
				"status":         "archived",
			},
			useAuth:        false,
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errors.UNAUTHORIZED,
		},
		{
			name:     "批量操作-空内容列表",
			endpoint: "/api/v1/management/contents/batch/status",
			requestBody: map[string]interface{}{
				"content_ksuids": []string{},
				"status":         "archived",
			},
			useAuth:        true,
			expectedStatus: http.StatusBadRequest,
			expectedCode:   errors.INVALID_PARAMETER,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBody, _ := json.Marshal(tt.requestBody)
			url := server.GetBaseURL() + tt.endpoint

			req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonBody))
			if tt.endpoint == "/api/v1/management/contents/batch/delete" {
				req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
			}
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			if tt.useAuth {
				req.Header.Set("Authorization", "Bearer "+token)
			}

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
		})
	}
}

func TestContentAPI_HealthCheck(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	resp, err := http.Get(server.GetBaseURL() + "/api/v1/health")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var response globalTypes.GlobalResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(t, err)

	assert.Equal(t, errors.SUCCESS, response.Code)
	assert.Equal(t, "服务健康", response.Msg)
}
