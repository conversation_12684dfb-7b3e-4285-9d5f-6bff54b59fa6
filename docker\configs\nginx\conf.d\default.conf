# PXPAT 微服务反向代理配置

# 上游服务定义
upstream admin_service {
    server admin-service:10001;
}

upstream user_gateway {
    server user-gateway:11000;
}

upstream user_service {
    server user-service:11001;
}

upstream message_service {
    server message-service:11002;
}

upstream content_service {
    server content-service:12001;
}

upstream interaction_service {
    server interaction-service:12002;
}

upstream audit_service {
    server audit-service:12003;
}

upstream help_service {
    server help-service:12004;
}

upstream recommendation_service {
    server recommendation-service:12005;
}

upstream user_storage_service {
    server user-storage-service:13001;
}

upstream content_storage_service {
    server content-storage-service:13002;
}

upstream media_processing_service {
    server media-processing-service:13003;
}

upstream storage_migration_service {
    server storage-migration-service:13004;
}

upstream notify_gateway {
    server notify-gateway:14000;
}

upstream external_service {
    server external-service:14001;
}

upstream points_service {
    server points-service:15001;
}

upstream wallet_service {
    server wallet-service:15002;
}

upstream dividend_service {
    server dividend-service:15003;
}

upstream token_service {
    server token-service:15004;
}

# 主服务器配置
server {
    listen 80;
    server_name localhost;

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 管理集群
    location /api/admin/ {
        proxy_pass http://admin_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 用户集群
    location /api/user/ {
        proxy_pass http://user_gateway/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 内容集群
    location /api/content/ {
        proxy_pass http://content_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/interaction/ {
        proxy_pass http://interaction_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/audit/ {
        proxy_pass http://audit_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/help/ {
        proxy_pass http://help_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/recommendation/ {
        proxy_pass http://recommendation_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 存储集群
    location /api/storage/user/ {
        proxy_pass http://user_storage_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/storage/content/ {
        proxy_pass http://content_storage_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/storage/media/ {
        proxy_pass http://media_processing_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 通知集群
    location /api/notify/ {
        proxy_pass http://notify_gateway/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/external/ {
        proxy_pass http://external_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 金融集群
    location /api/finance/points/ {
        proxy_pass http://points_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/finance/wallet/ {
        proxy_pass http://wallet_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/finance/dividend/ {
        proxy_pass http://dividend_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/finance/token/ {
        proxy_pass http://token_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 默认页面
    location / {
        return 200 '{"message":"PXPAT API Gateway","status":"running","timestamp":"$time_iso8601"}';
        add_header Content-Type application/json;
    }
}
