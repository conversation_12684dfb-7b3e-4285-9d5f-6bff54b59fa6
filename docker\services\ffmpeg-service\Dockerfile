FROM alpine:3.19

# 安装FFmpeg和相关依赖
RUN apk update && \
    apk add --no-cache \
    ffmpeg \
    ffmpeg-dev \
    imagemagick \
    imagemagick-dev \
    libjpeg-turbo-dev \
    libpng-dev \
    libwebp-dev \
    bash \
    curl

# 设置工作目录
WORKDIR /app/media

# 创建必要的目录
RUN mkdir -p /app/media/input /app/media/output /app/media/temp

# 设置时区
ENV TZ=Asia/Shanghai

# 容器启动时保持运行
CMD ["tail", "-f", "/dev/null"] 