package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/intra/service"
)

// ComplaintClient 投诉服务客户端接口
type ComplaintClient interface {
	// 投诉相关
	CreateComplaint(ctx context.Context, req *dto.CreateComplaintRequest) (*dto.ComplaintResponse, error)
	GetComplaint(ctx context.Context, complaintKSUID string) (*dto.ComplaintResponse, error)
	GetUserComplaints(ctx context.Context, userKSUID string, page, pageSize int) (*dto.ComplaintListResponse, error)

	// 身份认证相关
	CreateIdentityVerification(ctx context.Context, req *dto.CreateIdentityVerificationRequest) (*dto.IdentityVerificationResponse, error)
	GetIdentityVerification(ctx context.Context, verificationKSUID string) (*dto.IdentityVerificationResponse, error)

	// 权益认证相关
	CreateRightsVerification(ctx context.Context, req *dto.CreateRightsVerificationRequest) (*dto.RightsVerificationResponse, error)
	GetRightsVerification(ctx context.Context, rightsKSUID string) (*dto.RightsVerificationResponse, error)

	// 内部接口
	GetUserComplaintStats(ctx context.Context, userKSUID string) (*service.UserComplaintStats, error)
	GetContentComplaintStats(ctx context.Context, contentKSUID string) (*service.ContentComplaintStats, error)
	BatchGetContentComplaintStatus(ctx context.Context, contentKSUIDs []string) (map[string]*service.ContentComplaintStatus, error)
	CanUserComplain(ctx context.Context, userKSUID string) (*service.UserComplaintPermission, error)
	GetComplaintResult(ctx context.Context, complaintKSUID string) (*service.ComplaintResult, error)
}

// ComplaintClientConfig 投诉服务客户端配置
type ComplaintClientConfig struct {
	BaseURL    string        `json:"base_url"`    // 服务基础URL
	Timeout    time.Duration `json:"timeout"`     // 请求超时时间
	APIKey     string        `json:"api_key"`     // API密钥
	MaxRetries int           `json:"max_retries"` // 最大重试次数
}

// complaintClient 投诉服务客户端实现
type complaintClient struct {
	config     ComplaintClientConfig
	httpClient *http.Client
}

// NewComplaintClient 创建投诉服务客户端实例
func NewComplaintClient(config ComplaintClientConfig) ComplaintClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}

	return &complaintClient{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// CreateComplaint 创建投诉
func (c *complaintClient) CreateComplaint(ctx context.Context, req *dto.CreateComplaintRequest) (*dto.ComplaintResponse, error) {
	url := fmt.Sprintf("%s/api/v1/complaints", c.config.BaseURL)

	var response dto.ComplaintResponse
	err := c.doRequest(ctx, "POST", url, req, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// GetComplaint 获取投诉详情
func (c *complaintClient) GetComplaint(ctx context.Context, complaintKSUID string) (*dto.ComplaintResponse, error) {
	url := fmt.Sprintf("%s/api/v1/complaints/%s", c.config.BaseURL, complaintKSUID)

	var response dto.ComplaintResponse
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// GetUserComplaints 获取用户投诉列表
func (c *complaintClient) GetUserComplaints(ctx context.Context, userKSUID string, page, pageSize int) (*dto.ComplaintListResponse, error) {
	url := fmt.Sprintf("%s/api/v1/complaints/my?page=%d&page_size=%d", c.config.BaseURL, page, pageSize)

	var response dto.ComplaintListResponse
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// CreateIdentityVerification 创建身份认证
func (c *complaintClient) CreateIdentityVerification(ctx context.Context, req *dto.CreateIdentityVerificationRequest) (*dto.IdentityVerificationResponse, error) {
	url := fmt.Sprintf("%s/api/v1/identity/verification", c.config.BaseURL)

	var response dto.IdentityVerificationResponse
	err := c.doRequest(ctx, "POST", url, req, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// GetIdentityVerification 获取身份认证详情
func (c *complaintClient) GetIdentityVerification(ctx context.Context, verificationKSUID string) (*dto.IdentityVerificationResponse, error) {
	url := fmt.Sprintf("%s/api/v1/identity/verification/%s", c.config.BaseURL, verificationKSUID)

	var response dto.IdentityVerificationResponse
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// CreateRightsVerification 创建权益认证
func (c *complaintClient) CreateRightsVerification(ctx context.Context, req *dto.CreateRightsVerificationRequest) (*dto.RightsVerificationResponse, error) {
	url := fmt.Sprintf("%s/api/v1/rights/verification", c.config.BaseURL)

	var response dto.RightsVerificationResponse
	err := c.doRequest(ctx, "POST", url, req, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// GetRightsVerification 获取权益认证详情
func (c *complaintClient) GetRightsVerification(ctx context.Context, rightsKSUID string) (*dto.RightsVerificationResponse, error) {
	url := fmt.Sprintf("%s/api/v1/rights/verification/%s", c.config.BaseURL, rightsKSUID)

	var response dto.RightsVerificationResponse
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// GetUserComplaintStats 获取用户投诉统计
func (c *complaintClient) GetUserComplaintStats(ctx context.Context, userKSUID string) (*service.UserComplaintStats, error) {
	url := fmt.Sprintf("%s/internal/v1/complaints/users/%s/stats", c.config.BaseURL, userKSUID)

	var response service.UserComplaintStats
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// GetContentComplaintStats 获取内容投诉统计
func (c *complaintClient) GetContentComplaintStats(ctx context.Context, contentKSUID string) (*service.ContentComplaintStats, error) {
	url := fmt.Sprintf("%s/internal/v1/complaints/contents/%s/stats", c.config.BaseURL, contentKSUID)

	var response service.ContentComplaintStats
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// BatchGetContentComplaintStatus 批量获取内容投诉状态
func (c *complaintClient) BatchGetContentComplaintStatus(ctx context.Context, contentKSUIDs []string) (map[string]*service.ContentComplaintStatus, error) {
	url := fmt.Sprintf("%s/internal/v1/complaints/contents/batch-status", c.config.BaseURL)

	// 构建查询参数
	contentKSUIDsStr := ""
	for i, ksuid := range contentKSUIDs {
		if i > 0 {
			contentKSUIDsStr += ","
		}
		contentKSUIDsStr += ksuid
	}
	url += "?content_ksuids=" + contentKSUIDsStr

	var response map[string]*service.ContentComplaintStatus
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// CanUserComplain 检查用户是否可以投诉
func (c *complaintClient) CanUserComplain(ctx context.Context, userKSUID string) (*service.UserComplaintPermission, error) {
	url := fmt.Sprintf("%s/internal/v1/complaints/users/%s/permission", c.config.BaseURL, userKSUID)

	var response service.UserComplaintPermission
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// GetComplaintResult 获取投诉处理结果
func (c *complaintClient) GetComplaintResult(ctx context.Context, complaintKSUID string) (*service.ComplaintResult, error) {
	url := fmt.Sprintf("%s/internal/v1/complaints/%s/result", c.config.BaseURL, complaintKSUID)

	var response service.ComplaintResult
	err := c.doRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// doRequest 执行HTTP请求
func (c *complaintClient) doRequest(ctx context.Context, method, url string, reqBody interface{}, respBody interface{}) error {
	var body io.Reader

	if reqBody != nil {
		jsonData, err := json.Marshal(reqBody)
		if err != nil {
			return fmt.Errorf("序列化请求体失败: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	if c.config.APIKey != "" {
		req.Header.Set("X-API-Key", c.config.APIKey)
	}

	// 执行请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("执行请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应体失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode >= 400 {
		return fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respData))
	}

	// 解析响应体
	if respBody != nil {
		err = json.Unmarshal(respData, respBody)
		if err != nil {
			return fmt.Errorf("解析响应体失败: %w", err)
		}
	}

	return nil
}
